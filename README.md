# Introduction 

The RTMC integration endpoints use very old/weak ciphers that are not available in .NET Core.
To get around this a custom compilation of openssl-1.0.1k (with 3DES cipher included) and curl-7.81.0 is used.
The API creates the SOAP message and passes that to the curl.sh script that calls the custom built curl.

## Base runtime docker image

The [*Base.Dockerfile*](Base.Dockerfile) creates a custom runtime image *webuycarswebsite.azurecr.io/webuycars-enatis-rtmc-api-base:latest* with the required openssl and curl versions installed in /usr/local.

## OpenVPN3 configuration

Openvpn3 could not be installed on the runtime container as it doesn't have D-BUS support. The VPN to RTMC is therefore created from the host.

The following steps can be used to setup the openvpn3 client to auto load:

```code
cp enatis.ovpn /etc/openvpn3/autoload/
echo '{"autostart":true}' > /etc/openvpn3/autoload/enatis.autoload
openvpn3-autoload --directory /etc/openvpn3/autoload
systemctl enable openvpn3-autoload
systemctl start openvpn3-autoload
```

Check the status of openvpn3 autoload service

```code
systemctl start openvpn3-autoload
```

It should output something similar to:

```code
openvpn3-autoload.service - OpenVPN 3 Linux configuration auto loader and starter
    Loaded: loaded (/lib/systemd/system/openvpn3-autoload.service; enabled; vendor preset: enabled)
     Active: active (exited) since Thu 2022-03-17 07:48:11 UTC; 4h 54min ago
    Process: 51459 ExecStart=/usr/sbin/openvpn3-autoload --directory /etc/openvpn3/autoload (code=exited, status=0/SUCCESS)
   Main PID: 51459 (code=exited, status=0/SUCCESS)
```

If the VPN is successfully established a *tun0* interface will be visible

```code
15: tun0: <POINTOPOINT,MULTICAST,NOARP,UP,LOWER_UP> mtu 1500 qdisc fq_codel state UNKNOWN group default qlen 500
    link/none
    inet 10.150.3.118/20 brd 10.150.15.255 scope global tun0
       valid_lft forever preferred_lft forever
    inet6 fe80::9a5a:e748:ee26:97d3/64 scope link stable-privacy
       valid_lft forever preferred_lft forever
```

## OpenVPN restart
Use the following command to restart the VPN
```code
openvpn3-autoload --directory /etc/openvpn3/autoload/
```
After the restart the *tun0* interface should show as up and vpn.enatis.co.za or vpndr.enatis.co.za should be ping-able.