using System;
using System.Linq;
using System.Text.Json;
using System.Text.Json.Serialization;
using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.Serialization;
using Elastic.Transport;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using WeBuyCars.Evolve.Infrastructure.Data.Elasticsearch.Configuration;

namespace WeBuyCars.Evolve.Infrastructure.Data.Elasticsearch;

public sealed class ElasticClientProvider
{
    #region Constructors

    public ElasticClientProvider(IOptions<ElasticConnectionSettings> settings, IHostEnvironment environment)
    {
        var config = settings.Value;
        
        // Create the connection settings.
        var pool = new CloudNodePool(config.CloudId, new ApiKey(config.EvolveApiKey));
        var connectionSettings = new ElasticsearchClientSettings(pool, sourceSerializer: (_, clientSettings) =>
            new DefaultSourceSerializer(clientSettings, ConfigureOptions));
        
        // This is going to enable us to see the raw queries sent to elastic when debugging.
        if (environment.EnvironmentName.Contains("local", StringComparison.InvariantCultureIgnoreCase) ||
            environment.EnvironmentName.Contains("development", StringComparison.InvariantCultureIgnoreCase) ||
            environment.EnvironmentName.Contains("staging", StringComparison.InvariantCultureIgnoreCase))
            connectionSettings.EnableDebugMode();

        // Create the actual client
        Client = new ElasticsearchClient(connectionSettings);
        
        DailySalesSummaryIndex = config.DailySalesSummaryIndex;
        VehicleDealsIndex = config.VehicleDealsIndex;
        DebtorsIndex = config.DebtorsIndex;
        CreditorsIndex = config.CreditorsIndex;
        BalancePerVehicleIndex = config.BalancePerVehicleIndex;
        MonthlySalesSummaryIndex = config.MonthlySalesSummaryIndex;
        StockIndex = config.StockIndex;
        StockSummaryIndex = config.StockSummaryIndex;
        InvoicesIndex = config.InvoicesIndex;
        UnallocatedTransactionsIndex = config.UnallocatedTransactionsIndex;
        AdjustedCostStatus = config.AdjustedCostStatus;
        AccountBalanceIndex = config.AccountBalanceIndex;
    }
    
    private static void ConfigureOptions(JsonSerializerOptions o)
    {
        o.Converters.Remove(o.Converters.OfType<JsonStringEnumConverter>().First());
    }

    #endregion

    #region Properties

    public ElasticsearchClient Client { get; }

    public string DailySalesSummaryIndex { get; }

    public string VehicleDealsIndex { get; }

    public string DebtorsIndex { get; }
    
    public string CreditorsIndex { get; }
        
    public string BalancePerVehicleIndex { get; }

    public string MonthlySalesSummaryIndex { get; }

    public string StockIndex { get; }

    public string StockSummaryIndex { get; }

    public string InvoicesIndex { get; }

    public string UnallocatedTransactionsIndex { get; }
        
    public string AdjustedCostStatus { get; }
    
    public string AccountBalanceIndex { get; }

    #endregion
}