using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Data.Context;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Repositories
{
    public class RTMCVehicleLicenceRenewalContactPersonRepository : Repository<RTMCVehicleLicenceRenewalContactPerson>, IRTMCVehicleLicenceRenewalContactPersonRepository
    {
        #region Constructors

        public RTMCVehicleLicenceRenewalContactPersonRepository(RTMCContext context)
            : base(context)
        {
        }

        #endregion

        public RTMCVehicleLicenceRenewalContactPerson AddRTMCVehicleLicenseRenewalContactPerson(RTMCVehicleLicenceRenewalContactPerson entity)
        {
            return _context.VehicleLicenceRenewalContactPersons.Add(entity).Entity;
        }

        public async Task<List<RTMCVehicleLicenceRenewalContactPerson>> Where(Expression<Func<RTMCVehicleLicenceRenewalContactPerson, bool>> predicate)
        {
            return await _context.VehicleLicenceRenewalContactPersons
                .Where(predicate)
                .AsNoTracking()
                .ToListAsync();
        }

        public async Task<RTMCVehicleLicenceRenewalContactPerson> FirstOrDefaultAsync(Expression<Func<RTMCVehicleLicenceRenewalContactPerson, bool>> predicate)
        {
            return await _context.VehicleLicenceRenewalContactPersons
                .Where(predicate)
                .OrderBy(x => x.Id)
                .FirstOrDefaultAsync();
        }
    }
}
