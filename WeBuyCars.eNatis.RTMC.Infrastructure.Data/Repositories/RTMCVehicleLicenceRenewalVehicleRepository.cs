using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Data.Context;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Repositories
{
    public class RTMCVehicleLicenceRenewalVehicleRepository : Repository<RTMCVehicleLicenceRenewalVehicle>, IRTMCVehicleLicenceRenewalVehicleRepository
    {
        #region Constructors

        public RTMCVehicleLicenceRenewalVehicleRepository(RTMCContext context)
            : base(context)
        {
        }

        #endregion

        public RTMCVehicleLicenceRenewalVehicle AddRTMCVehicleLicenceRenewalVehicle(RTMCVehicleLicenceRenewalVehicle entity)
        {
            return _context.VehicleLicenceRenewalVehicles.Add(entity).Entity;
        }

        public bool AddRTMCVehicleLicenceRenewalVehicleRange(List<RTMCVehicleLicenceRenewalVehicle> entities)
        {
            var result = false;
            _context.VehicleLicenceRenewalVehicles.AddRange(entities);
            result = true;
            return result;
        }

        public RTMCVehicleLicenceRenewalVehicle UpdateRTMCVehicleLicenceRenewalVehicle(RTMCVehicleLicenceRenewalVehicle entity)
        {
            _context.Entry(entity).State = EntityState.Modified;
            return _context.VehicleLicenceRenewalVehicles.Update(entity).Entity;
        }

        public async Task<List<RTMCVehicleLicenceRenewalVehicle>> Where(Expression<Func<RTMCVehicleLicenceRenewalVehicle, bool>> predicate)
        {
            return await _context.VehicleLicenceRenewalVehicles
                .Where(predicate)
                .AsNoTracking()
                .ToListAsync();
        }

        public async Task<RTMCVehicleLicenceRenewalVehicle> FirstOrDefaultAsync(Expression<Func<RTMCVehicleLicenceRenewalVehicle, bool>> predicate)
        {
            return await _context.VehicleLicenceRenewalVehicles
                .Where(predicate)
                .OrderBy(x => x.Id)
                .FirstOrDefaultAsync();
        }

        public async Task<List<RTMCVehicleLicenceRenewalVehicle>> GetByRenewalIdAsync(long renewalId)
        {
            return await _context.VehicleLicenceRenewalVehicles
                .Where(x => x.VehicleLicenceRenewalId == renewalId)
                .OrderBy(x => x.RegisterNumber)
                .AsNoTracking()
                .ToListAsync();
        }

        public async Task<List<RTMCVehicleLicenceRenewalVehicle>> GetByRenewalIdAndStatusAsync(long renewalId, string status)
        {
            return await _context.VehicleLicenceRenewalVehicles
                .Where(x => x.VehicleLicenceRenewalId == renewalId && x.Status == status)
                .OrderBy(x => x.RegisterNumber)
                .AsNoTracking()
                .ToListAsync();
        }
    }
}
