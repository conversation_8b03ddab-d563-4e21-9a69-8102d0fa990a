using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Data.Context;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Repositories
{
    public class RTMCVehicleLicenceRenewalAddressRepository : Repository<RTMCVehicleLicenceRenewalAddress>, IRTMCVehicleLicenceRenewalAddressRepository
    {
        #region Constructors

        public RTMCVehicleLicenceRenewalAddressRepository(RTMCContext context)
            : base(context)
        {
        }

        #endregion

        public RTMCVehicleLicenceRenewalAddress AddRTMCVehicleLicenceRenewalAddress(RTMCVehicleLicenceRenewalAddress entity)
        {
            return _context.VehicleLicenceRenewalAddresses.Add(entity).Entity;
        }

        public async Task<List<RTMCVehicleLicenceRenewalAddress>> Where(Expression<Func<RTMCVehicleLicenceRenewalAddress, bool>> predicate)
        {
            return await _context.VehicleLicenceRenewalAddresses
                .Where(predicate)
                .AsNoTracking()
                .ToListAsync();
        }

        public async Task<RTMCVehicleLicenceRenewalAddress> FirstOrDefaultAsync(Expression<Func<RTMCVehicleLicenceRenewalAddress, bool>> predicate)
        {
            return await _context.VehicleLicenceRenewalAddresses
                .Where(predicate)
                .OrderBy(x => x.Id)
                .FirstOrDefaultAsync();
        }
    }
}
