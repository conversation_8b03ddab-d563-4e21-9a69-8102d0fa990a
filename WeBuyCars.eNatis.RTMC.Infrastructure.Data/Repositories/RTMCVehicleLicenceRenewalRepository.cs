using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Data.Context;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Repositories
{
    public class RTMCVehicleLicenceRenewalRepository : Repository<RTMCVehicleLicenceRenewal>, IRTMCVehicleLicenceRenewalRepository
    {
        #region Constructors

        public RTMCVehicleLicenceRenewalRepository(RTMCContext context)
            : base(context)
        {
        }

        #endregion

        public RTMCVehicleLicenceRenewal AddRTMCVehicleLicenceRenewal(RTMCVehicleLicenceRenewal entity)
        {
            return _context.VehicleLicenceRenewals.Add(entity).Entity;
        }

        public async Task<List<RTMCVehicleLicenceRenewal>> Where(Expression<Func<RTMCVehicleLicenceRenewal, bool>> predicate)
        {
            return await _context.VehicleLicenceRenewals
                .Where(predicate)
                .AsNoTracking()
                .ToListAsync();
        }

        public async Task<RTMCVehicleLicenceRenewal> FirstOrDefaultAsync(Expression<Func<RTMCVehicleLicenceRenewal, bool>> predicate)
        {
            return await _context.VehicleLicenceRenewals
                .Where(predicate)
                .OrderBy(x => x.Id)
                .FirstOrDefaultAsync();
        }

        public async Task<RTMCVehicleLicenceRenewal> GetByReferenceNumberAsync(string referenceNumber)
        {
            return await _context.VehicleLicenceRenewals
                .Where(x => x.ReferenceNumber == referenceNumber)
                .FirstOrDefaultAsync();
        }

        public async Task<RTMCVehicleLicenceRenewal> GetByRequestReferenceAsync(Guid requestReference)
        {
            return await _context.VehicleLicenceRenewals
                .Where(x => x.RequestReference == requestReference)
                .FirstOrDefaultAsync();
        }

        public async Task<List<RTMCVehicleLicenceRenewal>> GetByBusinessRegistrationNumberAsync(string businessRegistrationNumber)
        {
            return await _context.VehicleLicenceRenewals
                .Where(x => x.BusinessRegistrationNumber == businessRegistrationNumber)
                .OrderByDescending(x => x.InitiatedDate)
                .AsNoTracking()
                .ToListAsync();
        }

        public async Task<RTMCVehicleLicenceRenewal> GetWithRelatedDataAsync(long id)
        {
            return await _context.VehicleLicenceRenewals
                .Include(x => x.Owner)
                    .ThenInclude(o => o.DeliveryAddress)
                .Include(x => x.Owner)
                    .ThenInclude(o => o.ContactPerson)
                .Include(x => x.Vehicles)
                .Where(x => x.Id == id)
                .FirstOrDefaultAsync();
        }

        public async Task<RTMCVehicleLicenceRenewal> GetWithRelatedDataByReferenceAsync(string referenceNumber)
        {
            return await _context.VehicleLicenceRenewals
                .Include(x => x.Owner)
                    .ThenInclude(o => o.DeliveryAddress)
                .Include(x => x.Owner)
                    .ThenInclude(o => o.ContactPerson)
                .Include(x => x.Vehicles)
                .Where(x => x.ReferenceNumber == referenceNumber)
                .FirstOrDefaultAsync();
        }
    }
}
