using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Data.Context;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Repositories
{
    public class RTMCVehicleLicenceRenewalOwnerRepository : Repository<RTMCVehicleLicenceRenewalOwner>, IRTMCVehicleLicenceRenewalOwnerRepository
    {
        #region Constructors

        public RTMCVehicleLicenceRenewalOwnerRepository(RTMCContext context)
            : base(context)
        {
        }

        #endregion

        public RTMCVehicleLicenceRenewalOwner AddRTMCVehicleLicenseRenewalOwner(RTMCVehicleLicenceRenewalOwner entity)
        {
            return _context.VehicleLicenceRenewalOwners.Add(entity).Entity;
        }

        public async Task<List<RTMCVehicleLicenceRenewalOwner>> Where(Expression<Func<RTMCVehicleLicenceRenewalOwner, bool>> predicate)
        {
            return await _context.VehicleLicenceRenewalOwners
                .Where(predicate)
                .AsNoTracking()
                .ToListAsync();
        }

        public async Task<RTMCVehicleLicenceRenewalOwner> FirstOrDefaultAsync(Expression<Func<RTMCVehicleLicenceRenewalOwner, bool>> predicate)
        {
            return await _context.VehicleLicenceRenewalOwners
                .Where(predicate)
                .OrderBy(x => x.Id)
                .FirstOrDefaultAsync();
        }

        public async Task<RTMCVehicleLicenceRenewalOwner> GetWithRelatedDataByRenewalIdAsync(long renewalId)
        {
            return await _context.VehicleLicenceRenewalOwners
                .Include(x => x.DeliveryAddress)
                .Include(x => x.ContactPerson)
                .Where(x => x.VehicleLicenceRenewalId == renewalId)
                .FirstOrDefaultAsync();
        }
    }
}
