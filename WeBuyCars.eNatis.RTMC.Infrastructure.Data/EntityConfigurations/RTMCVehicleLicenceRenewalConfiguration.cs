using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.EntityConfigurations
{
    public class RTMCVehicleLicenceRenewalConfiguration : IEntityTypeConfiguration<RTMCVehicleLicenceRenewal>
    {
        public void Configure(EntityTypeBuilder<RTMCVehicleLicenceRenewal> builder)
        {
            builder.ToTable("VehicleLicenceRenewal");

            builder.HasKey(i => i.Id);

            // Required properties
            builder.Property(i => i.ReferenceNumber)
                .HasMaxLength(100)
                .IsRequired();

            builder.Property(i => i.BusinessRegistrationNumber)
                .HasMaxLength(50)
                .IsRequired();

            builder.Property(i => i.Status)
                .HasMaxLength(50)
                .IsRequired();

            builder.Property(i => i.User)
                .HasMaxLength(200)
                .IsRequired();

            builder.Property(i => i.WorkStation)
                .HasMaxLength(100)
                .IsRequired();

            builder.Property(i => i.Locality)
                .HasMaxLength(100)
                .IsRequired();

            builder.Property(i => i.NetworkAddress)
                .HasMaxLength(50);

            builder.Property(i => i.InitiatedDate)
                .IsRequired();

            builder.Property(i => i.RequestReference)
                .IsRequired()
                .HasDefaultValueSql("NEWID()");

            // Optional properties
            builder.Property(i => i.PaymentRedirectUrl)
                .HasMaxLength(500);

            builder.Property(i => i.TotalRenewalAmount)
                .HasColumnType("decimal(18,2)")
                .HasDefaultValue(0);

            builder.Property(i => i.TotalDeliveryAmount)
                .HasColumnType("decimal(18,2)")
                .HasDefaultValue(0);

            builder.Property(i => i.TotalPaidAmount)
                .HasColumnType("decimal(18,2)")
                .HasDefaultValue(0);

            builder.Property(i => i.CompletedDate)
                .IsRequired(false);

            builder.Property(i => i.ErrorMessage)
                .HasMaxLength(1000);

            // Indexes
            builder.HasIndex(i => i.ReferenceNumber)
                .IsUnique();

            builder.HasIndex(i => i.RequestReference)
                .IsUnique();

            builder.HasIndex(i => new { i.BusinessRegistrationNumber, i.Status });

            builder.HasIndex(i => i.InitiatedDate);

            // Relationships
            builder.HasOne(i => i.Owner)
                .WithOne(o => o.VehicleLicenceRenewal)
                .HasForeignKey<RTMCVehicleLicenceRenewalOwner>(o => o.VehicleLicenceRenewalId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasMany(i => i.Vehicles)
                .WithOne(v => v.VehicleLicenceRenewal)
                .HasForeignKey(v => v.VehicleLicenceRenewal)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasQueryFilter(m => EF.Property<bool>(m, "Deleted") == false);
        }
    }
}
