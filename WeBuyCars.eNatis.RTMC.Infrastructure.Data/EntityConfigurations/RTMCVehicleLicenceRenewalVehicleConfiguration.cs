using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.EntityConfigurations
{
    public class RTMCVehicleLicenceRenewalVehicleConfiguration : IEntityTypeConfiguration<RTMCVehicleLicenceRenewalVehicle>
    {
        public void Configure(EntityTypeBuilder<RTMCVehicleLicenceRenewalVehicle> builder)
        {
            builder.ToTable("VehicleLicenceRenewalVehicle");

            builder.HasKey(i => i.Id);

            // Required properties
            builder.Property(i => i.VehicleLicenceRenewalId)
                .IsRequired();

            // Vehicle identification properties
            builder.Property(i => i.VinOrChassis)
                .HasMaxLength(50);

            builder.Property(i => i.LicenseNumber)
                .HasMaxLength(50);

            builder.Property(i => i.RegisterNumber)
                .HasMaxLength(50);

            builder.Property(i => i.EngineNumber)
                .HasMaxLength(50);

            builder.Property(i => i.LicenseExpiryDate)
                .HasMaxLength(20);

            builder.Property(i => i.Roadworthy)
                .HasMaxLength(10);

            // Vehicle details
            builder.Property(i => i.Make)
                .HasMaxLength(100);

            builder.Property(i => i.Model)
                .HasMaxLength(100);

            builder.Property(i => i.SeriesName)
                .HasMaxLength(100);

            builder.Property(i => i.Colour)
                .HasMaxLength(50);

            builder.Property(i => i.Tare)
                .IsRequired(false);

            // Financial properties
            builder.Property(i => i.Amount)
                .HasColumnType("decimal(18,2)")
                .HasDefaultValue(0);

            builder.Property(i => i.CourierFees)
                .HasColumnType("decimal(18,2)")
                .IsRequired(false);

            builder.Property(i => i.LicenseFees)
                .HasColumnType("decimal(18,2)")
                .IsRequired(false);

            builder.Property(i => i.TransactionFees)
                .HasColumnType("decimal(18,2)")
                .IsRequired(false);

            builder.Property(i => i.ConvenienceFees)
                .HasColumnType("decimal(18,2)")
                .IsRequired(false);

            builder.Property(i => i.MinAmountDue)
                .HasColumnType("decimal(18,2)")
                .IsRequired(false);

            builder.Property(i => i.MaxAmountDue)
                .HasColumnType("decimal(18,2)")
                .IsRequired(false);

            builder.Property(i => i.OutstandingDebtForOffencesFees)
                .HasColumnType("decimal(18,2)")
                .IsRequired(false);

            builder.Property(i => i.PreFees)
                .HasColumnType("decimal(18,2)")
                .IsRequired(false);

            builder.Property(i => i.PostFees)
                .HasColumnType("decimal(18,2)")
                .IsRequired(false);

            // builder.Property(i => i.Status)
            //     .;

            // Indexes
            builder.HasIndex(i => i.VehicleLicenceRenewalId);

            // Relationships
            builder.HasOne(i => i.VehicleLicenceRenewal)
                .WithMany(r => r.Vehicles)
                .HasForeignKey(i => i.VehicleLicenceRenewalId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasQueryFilter(m => EF.Property<bool>(m, "Deleted") == false);
        }
    }
}
