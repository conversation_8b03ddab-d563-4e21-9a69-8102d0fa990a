using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.EntityConfigurations
{
    public class RTMCVehicleLicenceRenewalOwnerConfiguration : IEntityTypeConfiguration<RTMCVehicleLicenceRenewalOwner>
    {
        public void Configure(EntityTypeBuilder<RTMCVehicleLicenceRenewalOwner> builder)
        {
            builder.ToTable("VehicleLicenceRenewalOwner");

            builder.HasKey(i => i.Id);

            // Required properties
            builder.Property(i => i.VehicleLicenceRenewalId)
                .IsRequired();

            builder.Property(i => i.IdentificationType)
                .HasMaxLength(RTMCVehicleLicenceRenewalOwnerConstants.IdentificationTypeMaxLength)
                .IsRequired();

            builder.Property(i => i.IdentificationNumber)
                .HasMaxLength(RTMCVehicleLicenceRenewalOwnerConstants.IdentificationNumberMaxLength)
                .IsRequired();

            // Indexes
            builder.HasIndex(i => i.VehicleLicenceRenewalId)
                .IsUnique(); // One-to-one relationship

            builder.HasIndex(i => new { i.IdentificationType, i.IdentificationNumber });

            // Relationships
            builder.HasOne(i => i.VehicleLicenceRenewal)
                .WithOne(r => r.Owner)
                .HasForeignKey<RTMCVehicleLicenceRenewalOwner>(i => i.VehicleLicenceRenewalId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(i => i.DeliveryAddress)
                .WithOne(a => a.Owner)
                .HasForeignKey<RTMCVehicleLicenceRenewalAddress>(a => a.VehicleLicenseRenewalOwnerId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(i => i.ContactPerson)
                .WithOne(c => c.Owner)
                .HasForeignKey<RTMCVehicleLicenceRenewalContactPerson>(c => c.VehicleLicenceRenewalOwnerId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasQueryFilter(m => EF.Property<bool>(m, "Deleted") == false);
        }
    }
}
