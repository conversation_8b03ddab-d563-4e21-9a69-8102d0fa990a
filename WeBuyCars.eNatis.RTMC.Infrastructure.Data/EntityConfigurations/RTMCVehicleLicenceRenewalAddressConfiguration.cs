using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.EntityConfigurations
{
    public class RTMCVehicleLicenceRenewalAddressConfiguration : IEntityTypeConfiguration<RTMCVehicleLicenceRenewalAddress>
    {
        public void Configure(EntityTypeBuilder<RTMCVehicleLicenceRenewalAddress> builder)
        {
            builder.ToTable("VehicleLicenceRenewalAddress");

            builder.HasKey(i => i.Id);

            builder.Property(i => i.VehicleLicenseRenewalOwnerId)
                .IsRequired();

            builder.Property(i => i.AddressType)
                .HasMaxLength(RTMCVehicleLicenceRenewalAddressConstants.AddressTypeMaxLength);

            builder.Property(i => i.Address1)
                .HasMaxLength(RTMCVehicleLicenceRenewalAddressConstants.Address1MaxLength);

            builder.Property(i => i.Address2)
                .HasMaxLength(RTMCVehicleLicenceRenewalAddressConstants.Address2MaxLength);

            builder.Property(i => i.Address3)
                .HasMaxLength(RTMCVehicleLicenceRenewalAddressConstants.Address3MaxLength);

            builder.Property(i => i.Address4)
                .HasMaxLength(RTMCVehicleLicenceRenewalAddressConstants.Address4MaxLength);

            builder.Property(i => i.Address5)
                .HasMaxLength(RTMCVehicleLicenceRenewalAddressConstants.Address5MaxLength);

            builder.Property(i => i.PostalCode)
                .HasMaxLength(RTMCVehicleLicenceRenewalAddressConstants.PostalCodeMaxLength);

            // Indexes
            builder.HasIndex(i => i.VehicleLicenseRenewalOwnerId)
                .IsUnique(); // One-to-one
            
            // Relationships
            builder.HasOne(i => i.Owner)
                .WithOne(o => o.DeliveryAddress)
                .HasForeignKey<RTMCVehicleLicenceRenewalAddress>(i => i.VehicleLicenseRenewalOwnerId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasQueryFilter(m => EF.Property<bool>(m, "Deleted") == false);
        }
    }
}
