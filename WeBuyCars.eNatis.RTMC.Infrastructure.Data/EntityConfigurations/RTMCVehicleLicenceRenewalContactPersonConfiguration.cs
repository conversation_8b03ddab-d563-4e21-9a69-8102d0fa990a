using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.EntityConfigurations
{
    public class RTMCVehicleLicenceRenewalContactPersonConfiguration : IEntityTypeConfiguration<RTMCVehicleLicenceRenewalContactPerson>
    {
        public void Configure(EntityTypeBuilder<RTMCVehicleLicenceRenewalContactPerson> builder)
        {
            builder.ToTable("VehicleLicenceRenewalContactPerson");

            builder.HasKey(i => i.Id);

            // Required properties
            builder.Property(i => i.VehicleLicenceRenewalOwnerId)
                .IsRequired();

            // Contact person details
            builder.Property(i => i.ContactName)
                .HasMaxLength(RTMCVehicleLicenceRenewalContactPersonConstants.ContactNameMaxLength);

            // Contact information
            builder.Property(i => i.ContactNumber)
                .HasMaxLength(RTMCVehicleLicenceRenewalContactPersonConstants.ContactNumberMaxLength);

            builder.Property(i => i.AlternativeContactNumber)
                .HasMaxLength(RTMCVehicleLicenceRenewalContactPersonConstants.ContactNumberMaxLength);

            // Base entity properties
            builder.Property(i => i.CreatedBy)
                .HasMaxLength(200)
                .IsRequired();

            builder.Property(i => i.ModifiedBy)
                .HasMaxLength(200);

            // Indexes
            builder.HasIndex(i => i.VehicleLicenceRenewalOwnerId)
                .IsUnique(); // One-to-one relationship

            builder.HasIndex(i => i.ContactNumber);

            // Relationships
            builder.HasOne(i => i.Owner)
                .WithOne(o => o.ContactPerson)
                .HasForeignKey<RTMCVehicleLicenceRenewalContactPerson>(i => i.VehicleLicenceRenewalOwnerId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasQueryFilter(m => EF.Property<bool>(m, "Deleted") == false);
        }
    }
}
