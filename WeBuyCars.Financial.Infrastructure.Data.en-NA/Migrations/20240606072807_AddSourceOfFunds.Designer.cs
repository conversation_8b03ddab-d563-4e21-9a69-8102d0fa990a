// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using WeBuyCars.Financial.Infrastructure.Data.Context;

#nullable disable

namespace WeBuyCars.Financial.Infrastructure.Data.en_NA.Migrations
{
    [DbContext(typeof(FinancialContext))]
    [Migration("20240606072807_AddSourceOfFunds")]
    partial class AddSourceOfFunds
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("FIN")
                .HasAnnotation("ProductVersion", "6.0.28")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder, 1L, 1);

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.Account", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<string>("AccountNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.HasIndex("AccountNumber")
                        .IsUnique();

                    b.ToTable("Account", "FIN");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.AccountType", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("Id");

                    b.ToTable("AccountType", "FIN");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Name = "AccountsReceivable"
                        },
                        new
                        {
                            Id = 2,
                            Name = "AccountsPayable"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.ActiveSalesLeads", b =>
                {
                    b.Property<string>("AccountNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("InitiateSaleChangeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LeadCategory")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LeadStatus")
                        .HasColumnType("nvarchar(max)");

                    b.ToView("ActiveSalesLeads");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.Audit", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset>("AuditDateTimeUtc")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("KeyValues")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("NewValues")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OldValues")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TableName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("User")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.ToTable("Audit", "FIN");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.Bank", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("Id");

                    b.ToTable("Bank", "FIN");

                    b.HasData(
                        new
                        {
                            Id = 0,
                            Name = "Other"
                        },
                        new
                        {
                            Id = 1,
                            Name = "Fnb"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BankAccount", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<string>("AccountNumber")
                        .IsRequired()
                        .HasMaxLength(13)
                        .HasColumnType("nvarchar(13)");

                    b.Property<long>("BankAccountHolderId")
                        .HasColumnType("bigint");

                    b.Property<int>("BankAccountTypeId")
                        .HasColumnType("int");

                    b.Property<string>("BranchCode")
                        .IsRequired()
                        .HasMaxLength(6)
                        .HasColumnType("nvarchar(6)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.HasIndex("BankAccountHolderId");

                    b.HasIndex("BankAccountTypeId");

                    b.ToTable("BankAccount", "FIN");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BankAccountHolder", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<int>("BankAccountHolderTypeId")
                        .HasColumnType("int");

                    b.Property<string>("CompanyName")
                        .HasMaxLength(60)
                        .HasColumnType("nvarchar(60)");

                    b.Property<string>("CompanyRegistrationNumber")
                        .HasMaxLength(13)
                        .HasColumnType("nvarchar(13)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("EmailAddress")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("EstateName")
                        .HasMaxLength(60)
                        .HasColumnType("nvarchar(60)");

                    b.Property<string>("EstateNumber")
                        .HasMaxLength(13)
                        .HasColumnType("nvarchar(13)");

                    b.Property<string>("IdNumber")
                        .HasMaxLength(13)
                        .HasColumnType("nvarchar(13)");

                    b.Property<string>("Initials")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.Property<string>("LastName")
                        .HasMaxLength(60)
                        .HasColumnType("nvarchar(60)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("PassportNumber")
                        .HasMaxLength(13)
                        .HasColumnType("nvarchar(13)");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.HasKey("Id");

                    b.HasIndex("BankAccountHolderTypeId");

                    b.ToTable("BankAccountHolder", "FIN");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BankAccountHolderType", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("BankAccountHolderType", "FIN");

                    b.HasData(
                        new
                        {
                            Id = 0,
                            Description = "Other",
                            Name = "Other"
                        },
                        new
                        {
                            Id = 1,
                            Description = "South African Individual",
                            Name = "SouthAfricanIndividual"
                        },
                        new
                        {
                            Id = 2,
                            Description = "Foreign Individual",
                            Name = "ForeignIndividual"
                        },
                        new
                        {
                            Id = 3,
                            Description = "Company",
                            Name = "Company"
                        },
                        new
                        {
                            Id = 4,
                            Description = "Estate",
                            Name = "Estate"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BankAccountType", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("Id");

                    b.ToTable("BankAccountType", "FIN");

                    b.HasData(
                        new
                        {
                            Id = 0,
                            Code = "00",
                            Name = "Unknown"
                        },
                        new
                        {
                            Id = 1,
                            Code = "01",
                            Name = "CurrentCheque"
                        },
                        new
                        {
                            Id = 2,
                            Code = "02",
                            Name = "Savings"
                        },
                        new
                        {
                            Id = 3,
                            Code = "03",
                            Name = "Transmission"
                        },
                        new
                        {
                            Id = 4,
                            Code = "04",
                            Name = "Bond"
                        },
                        new
                        {
                            Id = 6,
                            Code = "06",
                            Name = "Subscription"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BankAccountVerification", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<int?>("AccountAcceptsCredits")
                        .HasColumnType("int");

                    b.Property<int?>("AccountAcceptsDebits")
                        .HasColumnType("int");

                    b.Property<int?>("AccountCompanyRegistrationNumberMatch")
                        .HasColumnType("int");

                    b.Property<int?>("AccountEstateNumberMatch")
                        .HasColumnType("int");

                    b.Property<int?>("AccountExists")
                        .HasColumnType("int");

                    b.Property<int?>("AccountIdNumberMatch")
                        .HasColumnType("int");

                    b.Property<int?>("AccountOpen")
                        .HasColumnType("int");

                    b.Property<int?>("AccountOpenGtThreeMonths")
                        .HasColumnType("int");

                    b.Property<int?>("AccountPassportNumberMatch")
                        .HasColumnType("int");

                    b.Property<int?>("AccountTypeValid")
                        .HasColumnType("int");

                    b.Property<long>("BankAccountId")
                        .HasColumnType("bigint");

                    b.Property<string>("BuyLeadReference")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("ClientReference")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int?>("CompanyNameMatch")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<int?>("EmailValid")
                        .HasColumnType("int");

                    b.Property<int?>("EstateNameMatch")
                        .HasColumnType("int");

                    b.Property<int?>("InitialMatch")
                        .HasColumnType("int");

                    b.Property<int?>("LastNameMatch")
                        .HasColumnType("int");

                    b.Property<string>("MessageCode")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("MessageDescription")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Operator")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<int?>("PhoneValid")
                        .HasColumnType("int");

                    b.Property<int>("ProviderId")
                        .HasColumnType("int");

                    b.Property<DateTimeOffset?>("ReRequestDateTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTimeOffset>("RequestDateTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("TransactionReference")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("UserReference")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("Id");

                    b.HasIndex("BankAccountId");

                    b.HasIndex("ClientReference")
                        .IsUnique();

                    b.HasIndex("ProviderId");

                    b.HasIndex("UserReference")
                        .IsUnique();

                    b.ToTable("BankAccountVerification", "FIN");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BankAccountVerificationBuyLead", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<long>("BankAccountVerificationId")
                        .HasColumnType("bigint");

                    b.Property<string>("BuyLeadCode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.HasIndex("BankAccountVerificationId", "BuyLeadCode")
                        .IsUnique();

                    b.ToTable("BankAccountVerificationBuyLead", "FIN");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BankAccountVerificationMatchType", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("Id");

                    b.ToTable("BankAccountVerificationMatchType", "FIN");

                    b.HasData(
                        new
                        {
                            Id = 0,
                            Code = "00",
                            Name = "PositiveMatch"
                        },
                        new
                        {
                            Id = 1,
                            Code = "01",
                            Name = "NegativeMatch"
                        },
                        new
                        {
                            Id = 99,
                            Code = "99",
                            Name = "UnableToVerify"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BankAccountVerificationSetting", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<int>("VerificationExpirationDays")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("BankAccountVerificationSetting", "FIN");

                    b.HasData(
                        new
                        {
                            Id = 1L,
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2021, 2, 23, 11, 36, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            VerificationExpirationDays = 14
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BuyLead", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<bool>("BankAccountsManuallyVerified")
                        .HasColumnType("bit");

                    b.Property<string>("BankAccountsManuallyVerifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("BankAccountsManuallyVerifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<long>("BuyLeadBuyerId")
                        .HasColumnType("bigint");

                    b.Property<long>("BuyLeadClientId")
                        .HasColumnType("bigint");

                    b.Property<string>("BuyLeadCode")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("BuyLeadCompleteStatusId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<long?>("BuyLeadProcessId")
                        .HasColumnType("bigint");

                    b.Property<long>("BuyLeadVehicleId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool?>("DocumentsApproved")
                        .HasColumnType("bit");

                    b.Property<string>("DocumentsApprovedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DocumentsApprovedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LastAcceptedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("LastAcceptedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTimeOffset?>("LastAllocatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LastAllocatedTo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastCompletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("LastCompletedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTimeOffset?>("LeadCreationDateTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<long?>("NatisId")
                        .HasColumnType("bigint");

                    b.Property<byte[]>("Timestamp")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<bool>("UnreadQuestions")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.HasKey("Id");

                    b.HasIndex("BuyLeadBuyerId");

                    b.HasIndex("BuyLeadCode")
                        .IsUnique();

                    b.HasIndex("BuyLeadCompleteStatusId");

                    b.HasIndex("BuyLeadProcessId");

                    b.HasIndex("NatisId");

                    b.ToTable("BuyLead", "FIN");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BuyLeadBuyer", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<string>("BuyerId")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("EmailAddress")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("MobileNumber")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("EmailAddress")
                        .IsUnique();

                    b.ToTable("BuyLeadBuyer", "FIN");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BuyLeadClient", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<string>("Address")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("AlternatePhoneNumber")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<long>("BuyLeadId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("EmailAddress")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Name")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<bool>("SanctionCheckFailed")
                        .HasColumnType("bit");

                    b.Property<string>("Surname")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("BuyLeadId")
                        .IsUnique();

                    b.HasIndex("BuyLeadId", "Address", "Name", "Surname", "PhoneNumber", "EmailAddress")
                        .IsUnique()
                        .HasFilter("[Address] IS NOT NULL AND [Name] IS NOT NULL AND [Surname] IS NOT NULL AND [PhoneNumber] IS NOT NULL");

                    b.ToTable("BuyLeadClient", "FIN");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BuyLeadCompleteStatus", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("Id");

                    b.ToTable("BuyLeadCompleteStatus", "FIN");

                    b.HasData(
                        new
                        {
                            Id = 0,
                            Name = "Incomplete"
                        },
                        new
                        {
                            Id = 1,
                            Name = "Complete"
                        },
                        new
                        {
                            Id = 2,
                            Name = "DepositPaid"
                        },
                        new
                        {
                            Id = 3,
                            Name = "Pending"
                        },
                        new
                        {
                            Id = 4,
                            Name = "Rejected"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BuyLeadDocument", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<string>("AccountDomain")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("BlobUrl")
                        .IsRequired()
                        .HasMaxLength(1020)
                        .HasColumnType("nvarchar(1020)");

                    b.Property<long>("BuyLeadDocumentDescriptionId")
                        .HasColumnType("bigint");

                    b.Property<int>("BuyLeadDocumentStatusId")
                        .HasColumnType("int");

                    b.Property<string>("BuyLeadDocumentStatusLastChangedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("BuyLeadDocumentStatusLastChangedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<int>("BuyLeadDocumentTypeId")
                        .HasColumnType("int");

                    b.Property<long>("BuyLeadId")
                        .HasColumnType("bigint");

                    b.Property<string>("ContainerName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("FolderStructure")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<byte[]>("Timestamp")
                        .HasColumnType("varbinary(max)");

                    b.HasKey("Id");

                    b.HasIndex("BuyLeadDocumentDescriptionId");

                    b.HasIndex("BuyLeadDocumentStatusId");

                    b.HasIndex("BuyLeadDocumentTypeId");

                    b.HasIndex("BuyLeadId", "BuyLeadDocumentStatusId", "BuyLeadDocumentTypeId", "BlobUrl");

                    b.ToTable("BuyLeadDocument", "FIN");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BuyLeadDocumentDescription", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.HasIndex("Description")
                        .IsUnique();

                    b.ToTable("BuyLeadDocumentDescription", "FIN");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BuyLeadDocumentStatus", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("BuyLeadDocumentStatus", "FIN");

                    b.HasData(
                        new
                        {
                            Id = 0,
                            Description = "Received",
                            Name = "Received"
                        },
                        new
                        {
                            Id = 1,
                            Description = "Approved",
                            Name = "Approved"
                        },
                        new
                        {
                            Id = 2,
                            Description = "Rejected",
                            Name = "Rejected"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BuyLeadDocumentStatusHistory", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<long>("BuyLeadDocumentId")
                        .HasColumnType("bigint");

                    b.Property<int?>("BuyLeadDocumentStatusId")
                        .HasColumnType("int");

                    b.Property<string>("BuyLeadDocumentStatusLastChangedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("BuyLeadDocumentStatusLastChangedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.HasIndex("BuyLeadDocumentId");

                    b.HasIndex("BuyLeadDocumentStatusId");

                    b.ToTable("BuyLeadDocumentStatusHistory", "FIN");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BuyLeadDocumentType", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("Id");

                    b.ToTable("BuyLeadDocumentType", "FIN");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Name = "Contract"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BuyLeadHistory", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<bool?>("BankAccountsManuallyVerified")
                        .HasColumnType("bit");

                    b.Property<string>("BankAccountsManuallyVerifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("BankAccountsManuallyVerifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<int>("BuyLeadCompleteStatusId")
                        .HasColumnType("int");

                    b.Property<long>("BuyLeadId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool?>("DocumentsApproved")
                        .HasColumnType("bit");

                    b.Property<string>("DocumentsApprovedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DocumentsApprovedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LastAcceptedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("LastAcceptedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTimeOffset?>("LastAllocatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LastAllocatedTo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastCompletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("LastCompletedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.HasIndex("BuyLeadCompleteStatusId");

                    b.HasIndex("BuyLeadId");

                    b.ToTable("BuyLeadHistory", "FIN");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BuyLeadProcess", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ProcessDefinitionExternalId")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.ToTable("BuyLeadProcess", "FIN");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BuyLeadVehicle", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<long>("BuyLeadId")
                        .HasColumnType("bigint");

                    b.Property<string>("Category")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Colour")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("EngineNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<Guid?>("ExternalId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsTradeIn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<DateTimeOffset?>("LicenseExpiryDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LicensePlateNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Make")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<decimal?>("Mileage")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("MmCode")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Model")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTimeOffset?>("PurchaseDateTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<decimal>("PurchasePrice")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("RegisterNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("StockNumber")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<string>("Variant")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VinNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int?>("Year")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("BuyLeadId")
                        .IsUnique();

                    b.HasIndex("ExternalId");

                    b.HasIndex("BuyLeadId", "VinNumber", "RegisterNumber", "LicensePlateNumber", "PurchasePrice", "PurchaseDateTime")
                        .IsUnique()
                        .HasFilter("[VinNumber] IS NOT NULL AND [RegisterNumber] IS NOT NULL AND [LicensePlateNumber] IS NOT NULL AND [PurchaseDateTime] IS NOT NULL");

                    b.ToTable("BuyLeadVehicle", "FIN");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.Consent", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<bool>("Agreed")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<long?>("TransactionId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TransactionId");

                    b.ToTable("Consent", "FIN");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.Customer", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<string>("AccountNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BusinessName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BusinessRegistrationNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyRegistrationNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FirstName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IdNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsSuspended")
                        .HasColumnType("bit");

                    b.Property<string>("LastName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MobileNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PassportNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("QualifiesForDiscount")
                        .HasColumnType("bit");

                    b.Property<string>("RegisterNumber")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToView("CRMCustomers");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.FinanceHouse", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("BankAccountNumber")
                        .IsRequired()
                        .HasMaxLength(13)
                        .HasColumnType("nvarchar(13)");

                    b.Property<string>("BankBranchCode")
                        .IsRequired()
                        .HasMaxLength(6)
                        .HasColumnType("nvarchar(6)");

                    b.Property<string>("BankReference")
                        .IsRequired()
                        .HasMaxLength(13)
                        .HasColumnType("nvarchar(13)");

                    b.Property<string>("BeneficiaryCode")
                        .HasMaxLength(12)
                        .HasColumnType("nvarchar(12)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("FastBuy")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.HasKey("Id");

                    b.ToTable("FinanceHouse", "FIN");

                    b.HasData(
                        new
                        {
                            Id = 1L,
                            BankAccountNumber = "***********",
                            BankBranchCode = "282072",
                            BankReference = "WE BUY CARS",
                            BeneficiaryCode = "WES001",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2020, 10, 20, 10, 7, 45, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            FastBuy = false,
                            Name = "WESBANK LMS PAYMENTS ACCOUNT"
                        },
                        new
                        {
                            Id = 2L,
                            BankAccountNumber = "*********",
                            BankBranchCode = "083072",
                            BankReference = "WE BUY CARS",
                            BeneficiaryCode = "STD001",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2020, 10, 20, 10, 7, 45, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            FastBuy = false,
                            Name = "VAF PAYMENT SUSPENSE"
                        },
                        new
                        {
                            Id = 3L,
                            BankAccountNumber = "**********",
                            BankBranchCode = "482072",
                            BankReference = "WE BUY CARS",
                            BeneficiaryCode = "WIND001",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2020, 10, 20, 10, 7, 45, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            FastBuy = false,
                            Name = "BANK WINDHOEK"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.FinancialConstant", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<DateTimeOffset>("EffectiveFromDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("FinancialConstantType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<decimal>("Value")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.ToTable("FinancialConstant", "FIN");

                    b.HasData(
                        new
                        {
                            Id = 1L,
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2023, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            EffectiveFromDate = new DateTimeOffset(new DateTime(1993, 4, 6, 22, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            FinancialConstantType = "Vat",
                            Value = 14m
                        },
                        new
                        {
                            Id = 2L,
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2023, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            EffectiveFromDate = new DateTimeOffset(new DateTime(2018, 3, 31, 22, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            FinancialConstantType = "Vat",
                            Value = 15m
                        },
                        new
                        {
                            Id = 3L,
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2023, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            EffectiveFromDate = new DateTimeOffset(new DateTime(2022, 11, 24, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            FinancialConstantType = "Prime",
                            Value = 10.5m
                        },
                        new
                        {
                            Id = 4L,
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2023, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            EffectiveFromDate = new DateTimeOffset(new DateTime(2023, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            FinancialConstantType = "Offset",
                            Value = -0.5m
                        },
                        new
                        {
                            Id = 5L,
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2023, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            EffectiveFromDate = new DateTimeOffset(new DateTime(2023, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            FinancialConstantType = "Months",
                            Value = 72m
                        },
                        new
                        {
                            Id = 8L,
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2023, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            EffectiveFromDate = new DateTimeOffset(new DateTime(2023, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            FinancialConstantType = "DepositPercentage",
                            Value = 10m
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.FinancialServiceProvider", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("BranchCode")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.ToTable("FinancialServiceProvider", "FIN");

                    b.HasData(
                        new
                        {
                            Id = 1L,
                            BranchCode = "481972",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2022, 2, 4, 14, 1, 58, 833, DateTimeKind.Unspecified).AddTicks(7820), new TimeSpan(0, 2, 0, 0, 0)),
                            Deleted = false,
                            Name = "Bank Windhoek Limited"
                        },
                        new
                        {
                            Id = 2L,
                            BranchCode = "280172",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2022, 2, 4, 14, 1, 58, 833, DateTimeKind.Unspecified).AddTicks(7820), new TimeSpan(0, 2, 0, 0, 0)),
                            Deleted = false,
                            Name = "First National Bank Namibia Limited"
                        },
                        new
                        {
                            Id = 3L,
                            BranchCode = "461038",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2022, 2, 4, 14, 1, 58, 833, DateTimeKind.Unspecified).AddTicks(7820), new TimeSpan(0, 2, 0, 0, 0)),
                            Deleted = false,
                            Name = "Nedbank Namibia Limited"
                        },
                        new
                        {
                            Id = 4L,
                            BranchCode = "082372",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2022, 2, 4, 14, 1, 58, 833, DateTimeKind.Unspecified).AddTicks(7820), new TimeSpan(0, 2, 0, 0, 0)),
                            Deleted = false,
                            Name = "Standard Bank Namibia Limited"
                        },
                        new
                        {
                            Id = 5L,
                            BranchCode = "",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2022, 2, 4, 14, 1, 58, 833, DateTimeKind.Unspecified).AddTicks(7820), new TimeSpan(0, 2, 0, 0, 0)),
                            Deleted = false,
                            Name = "Trustco Bank Namibia Limited"
                        },
                        new
                        {
                            Id = 6L,
                            BranchCode = "",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2022, 2, 4, 14, 1, 58, 833, DateTimeKind.Unspecified).AddTicks(7820), new TimeSpan(0, 2, 0, 0, 0)),
                            Deleted = false,
                            Name = "Banco Atlantico"
                        },
                        new
                        {
                            Id = 7L,
                            BranchCode = "",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2022, 2, 4, 14, 1, 58, 833, DateTimeKind.Unspecified).AddTicks(7820), new TimeSpan(0, 2, 0, 0, 0)),
                            Deleted = false,
                            Name = "Bank BIC Namibia Limited"
                        },
                        new
                        {
                            Id = 8L,
                            BranchCode = "",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2022, 2, 4, 14, 1, 58, 833, DateTimeKind.Unspecified).AddTicks(7820), new TimeSpan(0, 2, 0, 0, 0)),
                            Deleted = false,
                            Name = "Letshego Bank Namibia Limited"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.HyphenErrorCode", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.HasKey("Id");

                    b.ToTable("HyphenErrorCode", "FIN");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Code = "H0",
                            Description = "Profile used for login is unknown (See note)"
                        },
                        new
                        {
                            Id = 2,
                            Code = "H4",
                            Description = "User checksum is invalid i.e. invalid password used"
                        },
                        new
                        {
                            Id = 3,
                            Code = "H5",
                            Description = "User checksum is valid but profile is disabled"
                        },
                        new
                        {
                            Id = 4,
                            Code = "H6",
                            Description = "Problem communicating with FACS server."
                        },
                        new
                        {
                            Id = 5,
                            Code = "H8",
                            Description = "User does not have the authority to use the requested method"
                        },
                        new
                        {
                            Id = 6,
                            Code = "WS01",
                            Description = "Invalid data checksum"
                        },
                        new
                        {
                            Id = 7,
                            Code = "WS02",
                            Description = "Profile missing FACS code"
                        },
                        new
                        {
                            Id = 8,
                            Code = "WS03",
                            Description = "Invalid length for transaction type"
                        },
                        new
                        {
                            Id = 9,
                            Code = "WS04",
                            Description = "Invalid length for document type"
                        },
                        new
                        {
                            Id = 10,
                            Code = "WS05",
                            Description = "Reference 1 too long"
                        },
                        new
                        {
                            Id = 11,
                            Code = "WS06",
                            Description = "Reference 2 not specified"
                        },
                        new
                        {
                            Id = 12,
                            Code = "WS07",
                            Description = "Reference 2 too long"
                        },
                        new
                        {
                            Id = 13,
                            Code = "WS08",
                            Description = "Code 1 too long"
                        },
                        new
                        {
                            Id = 14,
                            Code = "WS09",
                            Description = "Code 2 too long"
                        },
                        new
                        {
                            Id = 15,
                            Code = "WS10",
                            Description = "Amount not numeric"
                        },
                        new
                        {
                            Id = 16,
                            Code = "WS11",
                            Description = "Amount not specified"
                        },
                        new
                        {
                            Id = 17,
                            Code = "WS12",
                            Description = "Amount too large"
                        },
                        new
                        {
                            Id = 18,
                            Code = "WS13",
                            Description = "Client name not specified"
                        },
                        new
                        {
                            Id = 19,
                            Code = "WS14",
                            Description = "Client name too long"
                        },
                        new
                        {
                            Id = 20,
                            Code = "WS15",
                            Description = "Processing option 1 contains an invalid value"
                        },
                        new
                        {
                            Id = 21,
                            Code = "WS16",
                            Description = "Processing option 2 contains an invalid value"
                        },
                        new
                        {
                            Id = 22,
                            Code = "WS17",
                            Description = "Client bank account number contains invalid characters"
                        },
                        new
                        {
                            Id = 23,
                            Code = "WS18",
                            Description = "Client bank account not specified"
                        },
                        new
                        {
                            Id = 24,
                            Code = "WS19",
                            Description = "Client bank account too long"
                        },
                        new
                        {
                            Id = 25,
                            Code = "WS20",
                            Description = "Branch code contains invalid characters"
                        },
                        new
                        {
                            Id = 26,
                            Code = "WS21",
                            Description = "Branch code not specified"
                        },
                        new
                        {
                            Id = 27,
                            Code = "WS22",
                            Description = "Branch code too long"
                        },
                        new
                        {
                            Id = 28,
                            Code = "WS23",
                            Description = "Invalid value for account type"
                        },
                        new
                        {
                            Id = 29,
                            Code = "WS24",
                            Description = "CDV error"
                        },
                        new
                        {
                            Id = 30,
                            Code = "WS25",
                            Description = "Bank account number contains invalid characters"
                        },
                        new
                        {
                            Id = 31,
                            Code = "WS26",
                            Description = "Bank account number not specified"
                        },
                        new
                        {
                            Id = 32,
                            Code = "WS27",
                            Description = "Bank account number too long"
                        },
                        new
                        {
                            Id = 33,
                            Code = "WS28",
                            Description = "Action date invalid"
                        },
                        new
                        {
                            Id = 34,
                            Code = "WS29",
                            Description = "Invalid FEDI value specified"
                        },
                        new
                        {
                            Id = 35,
                            Code = "0016",
                            Description = "The transaction type is blank or invalid"
                        },
                        new
                        {
                            Id = 36,
                            Code = "0020",
                            Description = "Document type is blank or invalid"
                        },
                        new
                        {
                            Id = 37,
                            Code = "0024",
                            Description = "Invalid transaction type"
                        },
                        new
                        {
                            Id = 38,
                            Code = "0080",
                            Description = "Amount is less than zero"
                        },
                        new
                        {
                            Id = 39,
                            Code = "1009",
                            Description = "Invalid bank account number"
                        },
                        new
                        {
                            Id = 40,
                            Code = "1014",
                            Description = "Bank account details not transmitted"
                        },
                        new
                        {
                            Id = 41,
                            Code = "1042",
                            Description = "Client branch code invalid for FEDI transaction"
                        },
                        new
                        {
                            Id = 42,
                            Code = "1047",
                            Description = "FEDI not available for this bank at present"
                        },
                        new
                        {
                            Id = 43,
                            Code = "1054",
                            Description = "Invalid branch code"
                        },
                        new
                        {
                            Id = 44,
                            Code = "1055",
                            Description = "Invalid bank acc number/invalid acc number acc type combination"
                        },
                        new
                        {
                            Id = 45,
                            Code = "1056",
                            Description = "Account type not valid for this branch"
                        },
                        new
                        {
                            Id = 46,
                            Code = "1057",
                            Description = "Invalid branch code or blank"
                        },
                        new
                        {
                            Id = 47,
                            Code = "1058",
                            Description = "FNB saving account debit order not allowed"
                        },
                        new
                        {
                            Id = 48,
                            Code = "1059",
                            Description = "Account number too short/long"
                        },
                        new
                        {
                            Id = 49,
                            Code = "1078",
                            Description = "Duplicate transaction sent to HYPHEN"
                        },
                        new
                        {
                            Id = 50,
                            Code = "1084",
                            Description = "Bond account type '4' must be loaded as '1'"
                        },
                        new
                        {
                            Id = 51,
                            Code = "2061",
                            Description = "Transaction can not be created on credit card account"
                        },
                        new
                        {
                            Id = 52,
                            Code = "2062",
                            Description = "Credit card account number in invalid"
                        },
                        new
                        {
                            Id = 53,
                            Code = "2080",
                            Description = "Invalid branch"
                        },
                        new
                        {
                            Id = 54,
                            Code = "2138",
                            Description = "Record inactive on master file (Nominated payments)"
                        },
                        new
                        {
                            Id = 55,
                            Code = "2147",
                            Description = "Value exceeded for NPS limit"
                        },
                        new
                        {
                            Id = 56,
                            Code = "2150",
                            Description = "Reference not loaded on master file (Nominated payments)"
                        },
                        new
                        {
                            Id = 57,
                            Code = "2201",
                            Description = "Amount greater than R5000 for Naedo transactions"
                        },
                        new
                        {
                            Id = 58,
                            Code = "2202",
                            Description = "Service Branch. EFT not allowed"
                        },
                        new
                        {
                            Id = 59,
                            Code = "2203",
                            Description = "Zero amount received in mail"
                        },
                        new
                        {
                            Id = 60,
                            Code = "7777",
                            Description = "Technical error on HYPHEN system. Please contact HYPHEN Help Desk"
                        },
                        new
                        {
                            Id = 61,
                            Code = "8888",
                            Description = "Redirect information. Update line of business with new account details"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.HyphenProcessingOption1", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.HasKey("Id");

                    b.ToTable("HyphenProcessingOption1", "FIN");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Description = "Creation of single transaction (Itemised)",
                            Name = "I"
                        },
                        new
                        {
                            Id = 2,
                            Description = "Grouping a transaction to any transaction previously created on FACS with the same bank account number, action date and ‘G’ indicator",
                            Name = "G"
                        },
                        new
                        {
                            Id = 3,
                            Description = "Data only to be validated. Not to be created on FACS.",
                            Name = "V"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.HyphenProcessingOption2", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.HasKey("Id");

                    b.ToTable("HyphenProcessingOption2", "FIN");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Description = "Create transaction on FACS",
                            Name = "S"
                        },
                        new
                        {
                            Id = 2,
                            Description = "Data only to be validated. Not to be created on FACS.",
                            Name = "V"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.HyphenTransactionDetail", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<DateTimeOffset>("ActionDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("AgencyNumber")
                        .HasMaxLength(6)
                        .HasColumnType("nvarchar(6)");

                    b.Property<string>("AgencyPrefix")
                        .HasMaxLength(1)
                        .HasColumnType("nvarchar(1)");

                    b.Property<decimal>("Amount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("BankAccountNumber")
                        .IsRequired()
                        .HasMaxLength(17)
                        .HasColumnType("nvarchar(17)");

                    b.Property<int>("BankId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<string>("ChequeClearanceCode")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("ChequeNumber")
                        .HasMaxLength(9)
                        .HasColumnType("nvarchar(9)");

                    b.Property<int>("ClientBankAccountHolderTypeId")
                        .HasColumnType("int");

                    b.Property<string>("ClientBankAccountNumber")
                        .IsRequired()
                        .HasMaxLength(17)
                        .HasColumnType("nvarchar(17)");

                    b.Property<int>("ClientBankAccountTypeId")
                        .HasColumnType("int");

                    b.Property<string>("ClientBranchCode")
                        .IsRequired()
                        .HasMaxLength(6)
                        .HasColumnType("nvarchar(6)");

                    b.Property<string>("ClientName")
                        .IsRequired()
                        .HasMaxLength(80)
                        .HasColumnType("nvarchar(80)");

                    b.Property<string>("Code1")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("Code2")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("DepositType")
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<string>("DocumentNumber")
                        .HasMaxLength(8)
                        .HasColumnType("nvarchar(8)");

                    b.Property<string>("DocumentType")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<string>("ErrorCode")
                        .HasMaxLength(4)
                        .HasColumnType("nvarchar(4)");

                    b.Property<bool>("FediIndicator")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<int>("ProcessingOption1Id")
                        .HasColumnType("int");

                    b.Property<int>("ProcessingOption2Id")
                        .HasColumnType("int");

                    b.Property<string>("Reference1")
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<string>("Reference2")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("RequisitionNumber")
                        .HasMaxLength(9)
                        .HasColumnType("nvarchar(9)");

                    b.Property<long>("TransactionId")
                        .HasColumnType("bigint");

                    b.Property<Guid>("TransactionReference")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("TransactionTypeId")
                        .HasColumnType("int");

                    b.Property<string>("UniqueUserCode")
                        .HasMaxLength(4)
                        .HasColumnType("nvarchar(4)");

                    b.HasKey("Id");

                    b.HasIndex("BankId");

                    b.HasIndex("ClientBankAccountHolderTypeId");

                    b.HasIndex("ClientBankAccountTypeId");

                    b.HasIndex("ProcessingOption1Id");

                    b.HasIndex("ProcessingOption2Id");

                    b.HasIndex("TransactionId");

                    b.HasIndex("TransactionReference")
                        .IsUnique();

                    b.HasIndex("TransactionTypeId");

                    b.ToTable("HyphenTransactionDetail", "FIN");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.HyphenTransactionType", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<int>("BankId")
                        .HasColumnType("int");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int>("TransactionTypeId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("BankId");

                    b.HasIndex("TransactionTypeId");

                    b.ToTable("HyphenTransactionType", "FIN");

                    b.HasData(
                        new
                        {
                            Id = 7,
                            BankId = 0,
                            Code = "FNBSP",
                            Name = "Settlement",
                            TransactionTypeId = 7
                        },
                        new
                        {
                            Id = 8,
                            BankId = 0,
                            Code = "DEPPT",
                            Name = "Deposit",
                            TransactionTypeId = 8
                        },
                        new
                        {
                            Id = 9,
                            BankId = 0,
                            Code = "BALPT",
                            Name = "BalancePayment",
                            TransactionTypeId = 9
                        },
                        new
                        {
                            Id = 11,
                            BankId = 1,
                            Code = "FNBSP",
                            Name = "FnbSettlement",
                            TransactionTypeId = 7
                        },
                        new
                        {
                            Id = 12,
                            BankId = 1,
                            Code = "FNBDP",
                            Name = "FnbDeposit",
                            TransactionTypeId = 8
                        },
                        new
                        {
                            Id = 13,
                            BankId = 1,
                            Code = "FNBBP",
                            Name = "FnbBalancePayment",
                            TransactionTypeId = 9
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.Natis", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<int>("NatisStatusId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("NatisStatusId");

                    b.ToTable("Natis", "FIN");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.NatisStatus", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("NatisStatus", "FIN");

                    b.HasData(
                        new
                        {
                            Id = 0,
                            Status = "Unknown"
                        },
                        new
                        {
                            Id = 1,
                            Status = "BuyerToCollect"
                        },
                        new
                        {
                            Id = 2,
                            Status = "OriginalNatisReceived"
                        },
                        new
                        {
                            Id = 3,
                            Status = "Settlement"
                        },
                        new
                        {
                            Id = 4,
                            Status = "PaidUp"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.OnlineSale", b =>
                {
                    b.Property<string>("AccountNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("Amount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Consent")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CustomerName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("MobileNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTimeOffset?>("PaymentDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Provider")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StockDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StockNumber")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("StockNumberReference");

                    b.Property<string>("TransactionReference")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransactionStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransactionType")
                        .HasColumnType("nvarchar(max)");

                    b.ToView("OnlineSales");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.OutstandingPayment", b =>
                {
                    b.Property<string>("BuyLeadCode")
                        .HasColumnType("nvarchar(450)");

                    b.Property<bool?>("BuyLeadAccepted")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("BuyLeadAllocatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("BuyLeadAllocatedTo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool?>("BuyLeadBankAccountsManuallyVerified")
                        .HasColumnType("bit");

                    b.Property<string>("BuyLeadBankAccountsManuallyVerifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("BuyLeadBankAccountsManuallyVerifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("BuyLeadClientName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyLeadClientSurname")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyLeadCompleteStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("BuyLeadCompletedOutstanding")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("BuyLeadCompletedTotal")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTimeOffset?>("BuyLeadCreationDateTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool?>("BuyLeadDocumentsApproved")
                        .HasColumnType("bit");

                    b.Property<string>("BuyLeadDocumentsApprovedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("BuyLeadDocumentsApprovedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("BuyLeadLastAcceptedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("BuyLeadLastAcceptedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("BuyLeadLastCompletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("BuyLeadLastCompletedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTimeOffset?>("BuyLeadRefCreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("BuyLeadUnreadQuestions")
                        .HasColumnType("bit");

                    b.Property<string>("BuyLeadVehicleEngineNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool?>("BuyLeadVehicleIsTradeIn")
                        .HasColumnType("bit");

                    b.Property<string>("BuyLeadVehicleLicensePlateNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyLeadVehicleMake")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyLeadVehicleMmCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyLeadVehicleModel")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("BuyLeadVehiclePurchaseDateTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<decimal?>("BuyLeadVehiclePurchasePrice")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("BuyLeadVehicleRegisterNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyLeadVehicleVariant")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyLeadVehicleVinNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyLeadVehicleYear")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyerEmailAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyerId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyerMobileNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyerName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerBusinessName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerBusinessRegistrationNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerCompanyRegistrationNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerFinancialAccountNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerFirstName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerIdNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerIdentification")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerInitials")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerLastName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerPassportNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerRegisterNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NatisStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProcessDefinitionExternalId")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("BuyLeadCode");

                    b.ToView("OutstandingPaymentGridView");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.OutstandingPaymentDetail", b =>
                {
                    b.Property<bool?>("BuyLeadAccepted")
                        .HasColumnType("bit");

                    b.Property<bool?>("BuyLeadBankAccountsManuallyVerified")
                        .HasColumnType("bit");

                    b.Property<string>("BuyLeadBankAccountsManuallyVerifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("BuyLeadBankAccountsManuallyVerifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("BuyLeadClientName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyLeadClientSurname")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyLeadCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyLeadCompleteStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("BuyLeadCompletedOutstanding")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("BuyLeadCompletedTotal")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTimeOffset?>("BuyLeadCreationDateTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool?>("BuyLeadDocumentsApproved")
                        .HasColumnType("bit");

                    b.Property<string>("BuyLeadDocumentsApprovedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("BuyLeadDocumentsApprovedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("BuyLeadLastAcceptedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("BuyLeadLastAcceptedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("BuyLeadLastCompletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("BuyLeadLastCompletedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTimeOffset?>("BuyLeadRefCreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("BuyLeadVehicleEngineNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyLeadVehicleLicensePlateNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyLeadVehicleMake")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyLeadVehicleMmCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyLeadVehicleModel")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("BuyLeadVehiclePurchaseDateTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<decimal?>("BuyLeadVehiclePurchasePrice")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("BuyLeadVehicleRegisterNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyLeadVehicleVariant")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyLeadVehicleVinNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyLeadVehicleYear")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyerEmailAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyerId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyerMobileNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuyerName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerBusinessName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerBusinessRegistrationNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerCompanyRegistrationNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerFinancialAccountNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerFirstName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerIdNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerIdentification")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerInitials")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerLastName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerPassportNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerRegisterNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HyphenTransactionClientName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HyphenTransactionReference")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HyphenTransactionTypeCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("NatisStatusId")
                        .HasColumnType("int");

                    b.Property<string>("ProcessDefinitionExternalId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("TransactionAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("TransactionBankAccountBranchBank")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransactionBankAccountBranchCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransactionBankAccountHolderType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransactionBankAccountNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransactionBankAccountType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransactionCancellationReason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransactionCancellationReasonDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransactionCreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("TransactionCreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("TransactionCurrencyCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransactionFinanceHouseName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransactionModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("TransactionModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTimeOffset?>("TransactionPaymentDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("TransactionProvider")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransactionProviderReference")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransactionReference")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransactionStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransactionType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("TransactionTypeId")
                        .HasColumnType("int");

                    b.ToView("OutstandingPaymentDetailView");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.OutstandingPaymentTransactionHistory", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<long>("AccountId")
                        .HasColumnType("bigint");

                    b.Property<int>("AccountTypeId")
                        .HasColumnType("int");

                    b.Property<decimal>("Amount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("BuyLeadReference")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTimeOffset?>("PaymentDate")
                        .IsRequired()
                        .HasColumnType("datetimeoffset");

                    b.Property<int>("ProviderId")
                        .HasColumnType("int");

                    b.Property<string>("ProviderReference")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("StockNumberReference")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<byte[]>("Timestamp")
                        .HasColumnType("varbinary(max)");

                    b.Property<long>("TransactionId")
                        .HasColumnType("bigint");

                    b.Property<string>("TransactionReference")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("TransactionStatusId")
                        .HasColumnType("int");

                    b.Property<int>("TransactionTypeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.HasKey("Id");

                    b.HasIndex("AccountId");

                    b.HasIndex("AccountTypeId");

                    b.HasIndex("BuyLeadReference");

                    b.HasIndex("ProviderId");

                    b.HasIndex("TransactionId");

                    b.HasIndex("TransactionReference");

                    b.HasIndex("TransactionStatusId");

                    b.HasIndex("TransactionTypeId");

                    b.ToTable("OutstandingPaymentTransactionHistory", "FIN");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.OutstandingPaymentVerification", b =>
                {
                    b.Property<string>("AccountAcceptsCredits")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("AccountAcceptsCreditsCode")
                        .HasColumnType("int");

                    b.Property<string>("AccountAcceptsDebits")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("AccountAcceptsDebitsCode")
                        .HasColumnType("int");

                    b.Property<string>("AccountCompanyRegistrationNumberMatch")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("AccountCompanyRegistrationNumberMatchCode")
                        .HasColumnType("int");

                    b.Property<string>("AccountEstateNumberMatch")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("AccountEstateNumberMatchCode")
                        .HasColumnType("int");

                    b.Property<string>("AccountExists")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("AccountExistsCode")
                        .HasColumnType("int");

                    b.Property<string>("AccountIdNumberMatch")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("AccountIdNumberMatchCode")
                        .HasColumnType("int");

                    b.Property<string>("AccountOpen")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("AccountOpenCode")
                        .HasColumnType("int");

                    b.Property<string>("AccountOpenGtThreeMonths")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("AccountOpenGtThreeMonthsCode")
                        .HasColumnType("int");

                    b.Property<string>("AccountPassportNumberMatch")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("AccountPassportNumberMatchCode")
                        .HasColumnType("int");

                    b.Property<string>("AccountTypeValid")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("AccountTypeValidCode")
                        .HasColumnType("int");

                    b.Property<string>("BankAccountHolderType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BankAccountType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BankAccountVerificationBankAccountNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BankAccountVerificationBranchBank")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BankAccountVerificationBranchCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BankAccountVerificationBuyLeadReference")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BankAccountVerificationCompanyName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BankAccountVerificationCompanyRegistrationNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BankAccountVerificationEmailAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BankAccountVerificationEstateName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BankAccountVerificationEstateNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BankAccountVerificationIdNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BankAccountVerificationIdentification")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BankAccountVerificationIdentificationName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BankAccountVerificationInitials")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BankAccountVerificationLastName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("BankAccountVerificationLatestRequestDateTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("BankAccountVerificationMessageCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BankAccountVerificationMessageDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BankAccountVerificationPassportNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BankAccountVerificationPhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BankAccountVerificationProvider")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("BankAccountVerificationRequestDateTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("BankAccountVerificationUserReference")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyNameMatch")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("CompanyNameMatchCode")
                        .HasColumnType("int");

                    b.Property<string>("EmailValid")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("EmailValidCode")
                        .HasColumnType("int");

                    b.Property<string>("EstateNameMatch")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("EstateNameMatchCode")
                        .HasColumnType("int");

                    b.Property<string>("InitialMatch")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("InitialMatchCode")
                        .HasColumnType("int");

                    b.Property<string>("LastNameMatch")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("LastNameMatchCode")
                        .HasColumnType("int");

                    b.Property<string>("PhoneValid")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("PhoneValidCode")
                        .HasColumnType("int");

                    b.Property<bool>("VerificationExpired")
                        .HasColumnType("bit");

                    b.ToView("OutstandingPaymentVerification");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.PaymentConfiguration", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<long>("AccountId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Amount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<long?>("BankAccountId")
                        .HasColumnType("bigint");

                    b.Property<long>("BuyLeadId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<long?>("FinanceHouseId")
                        .HasColumnType("bigint");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<int>("ProviderId")
                        .HasColumnType("int");

                    b.Property<string>("SettlementReference")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("ShortfallBankAccountId")
                        .HasColumnType("bigint");

                    b.Property<long?>("SourceOfFundId")
                        .HasColumnType("bigint");

                    b.Property<int>("TransactionTypeId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AccountId");

                    b.HasIndex("BankAccountId");

                    b.HasIndex("BuyLeadId");

                    b.HasIndex("FinanceHouseId");

                    b.HasIndex("Id");

                    b.HasIndex("ProviderId");

                    b.HasIndex("ShortfallBankAccountId");

                    b.HasIndex("SourceOfFundId");

                    b.HasIndex("TransactionTypeId");

                    b.ToTable("PaymentConfiguration", "FIN");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.Provider", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("Id");

                    b.ToTable("Provider", "FIN");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Name = "None"
                        },
                        new
                        {
                            Id = 2,
                            Name = "Evolve"
                        },
                        new
                        {
                            Id = 3,
                            Name = "Ozow"
                        },
                        new
                        {
                            Id = 4,
                            Name = "Hyphen"
                        },
                        new
                        {
                            Id = 5,
                            Name = "Manual"
                        },
                        new
                        {
                            Id = 6,
                            Name = "Walletdoc"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.RefundReason", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.HasKey("Id");

                    b.ToTable("RefundReason", "FIN");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.ShortfallBankAccount", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AccountNumber")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("BankName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("BeneficiaryCode")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("BranchCode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.ToTable("ShortfallBankAccount", "FIN");

                    b.HasData(
                        new
                        {
                            Id = 1L,
                            AccountNumber = "***********",
                            BankName = "WE BUY CARS PTY LTD",
                            BeneficiaryCode = "WBC01",
                            BranchCode = "FIRNNANX",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2023, 6, 2, 14, 1, 58, 833, DateTimeKind.Unspecified).AddTicks(7820), new TimeSpan(0, 2, 0, 0, 0)),
                            Deleted = false
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.SourceOfFund", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Source")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.ToTable("SourceOfFund", "FIN");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.StockItem", b =>
                {
                    b.Property<string>("StockNumber")
                        .HasColumnType("nvarchar(450)");

                    b.Property<decimal>("BuyNowPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Discount")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("StockNumber");

                    b.ToView("StockItems");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.Transaction", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<long>("AccountId")
                        .HasColumnType("bigint");

                    b.Property<int>("AccountTypeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<decimal>("Amount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("BuyLeadReference")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTimeOffset?>("PaymentDate")
                        .IsRequired()
                        .HasColumnType("datetimeoffset");

                    b.Property<int>("ProviderId")
                        .HasColumnType("int");

                    b.Property<string>("ProviderReference")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<long?>("RefundReasonId")
                        .HasColumnType("bigint");

                    b.Property<string>("StockNumberReference")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<byte[]>("Timestamp")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.Property<int?>("TransactionCancellationReasonId")
                        .HasColumnType("int");

                    b.Property<string>("TransactionReference")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("TransactionStatusId")
                        .HasColumnType("int");

                    b.Property<int>("TransactionTypeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.HasKey("Id");

                    b.HasIndex("AccountId");

                    b.HasIndex("AccountTypeId");

                    b.HasIndex("ProviderId");

                    b.HasIndex("RefundReasonId");

                    b.HasIndex("TransactionCancellationReasonId");

                    b.HasIndex("TransactionReference")
                        .IsUnique();

                    b.HasIndex("TransactionStatusId");

                    b.HasIndex("TransactionTypeId");

                    b.ToTable("Transaction", "FIN");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.TransactionCancellationReason", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("TransactionCancellationReason", "FIN");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Description = "Year model",
                            Name = "YearModel"
                        },
                        new
                        {
                            Id = 2,
                            Description = "Engine / VIN no differs",
                            Name = "EngineOrVinDiffers"
                        },
                        new
                        {
                            Id = 3,
                            Description = "Banking details incorrect / cannot be verified",
                            Name = "BankingDetailsIncorrectCannotBeVerified"
                        },
                        new
                        {
                            Id = 4,
                            Description = "ID no incorrect",
                            Name = "IdNumberIncorrect"
                        },
                        new
                        {
                            Id = 5,
                            Description = "Surname / Business Name differs",
                            Name = "SurnameOrBusinessNameDiffers"
                        },
                        new
                        {
                            Id = 6,
                            Description = "Third party supporting doc",
                            Name = "ThirdPartySupportingDoc"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.TransactionStatus", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("Id");

                    b.ToTable("TransactionStatus", "FIN");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Name = "Complete"
                        },
                        new
                        {
                            Id = 2,
                            Name = "Cancelled"
                        },
                        new
                        {
                            Id = 3,
                            Name = "Error"
                        },
                        new
                        {
                            Id = 4,
                            Name = "Abandoned"
                        },
                        new
                        {
                            Id = 5,
                            Name = "PendingInvestigation"
                        },
                        new
                        {
                            Id = 6,
                            Name = "Pending"
                        },
                        new
                        {
                            Id = 7,
                            Name = "Submitted"
                        },
                        new
                        {
                            Id = 8,
                            Name = "Failed"
                        },
                        new
                        {
                            Id = 9,
                            Name = "Returned"
                        },
                        new
                        {
                            Id = 10,
                            Name = "Approved"
                        },
                        new
                        {
                            Id = 11,
                            Name = "Voided"
                        },
                        new
                        {
                            Id = 12,
                            Name = "Invalid"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.TransactionStockItem", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("StockNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("TransactionReference")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("TransactionReference", "StockNumber")
                        .IsUnique();

                    b.ToTable("TransactionStockItem", "FIN");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.TransactionType", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("TransactionType", "FIN");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Description = "Unknown.",
                            Name = "Unknown"
                        },
                        new
                        {
                            Id = 2,
                            Description = "Online Auction transaction e.g. auction deposit.",
                            Name = "Auction"
                        },
                        new
                        {
                            Id = 3,
                            Description = "Buy Now transaction. Stock scheduled for auction.",
                            Name = "BuyNow"
                        },
                        new
                        {
                            Id = 4,
                            Description = "Cash transaction.",
                            Name = "Cash"
                        },
                        new
                        {
                            Id = 5,
                            Description = "Finance transaction.",
                            Name = "Finance"
                        },
                        new
                        {
                            Id = 6,
                            Description = "Refund.",
                            Name = "Refund"
                        },
                        new
                        {
                            Id = 7,
                            Description = "Settlement. Hyphen - Settlement Payment",
                            Name = "Settlement"
                        },
                        new
                        {
                            Id = 8,
                            Description = "Deposit. Hyphen - Deposit Payment",
                            Name = "Deposit"
                        },
                        new
                        {
                            Id = 9,
                            Description = "BalancePayment. Hyphen - Balance Payment",
                            Name = "BalancePayment"
                        },
                        new
                        {
                            Id = 10,
                            Description = "ShortfallPayment. Manual - Shortfall Payment",
                            Name = "ShortfallPayment"
                        },
                        new
                        {
                            Id = 11,
                            Description = "TradeIn. Manual - Partial or full trade-in value.",
                            Name = "TradeIn"
                        },
                        new
                        {
                            Id = 12,
                            Description = "Outstanding Licence Fees. Manual.",
                            Name = "OutstandingLicenceFees"
                        },
                        new
                        {
                            Id = 13,
                            Description = "Client Transportation Cost. Manual.",
                            Name = "ClientTransportationCost"
                        },
                        new
                        {
                            Id = 14,
                            Description = "Unpaid Traffic Fines. Manual.",
                            Name = "UnpaidTrafficFines"
                        },
                        new
                        {
                            Id = 15,
                            Description = "Duplicate NaTIS.",
                            Name = "DuplicateNaTIS"
                        },
                        new
                        {
                            Id = 16,
                            Description = "Uplift NCO Fee. Manual.",
                            Name = "UpliftNcoFee"
                        },
                        new
                        {
                            Id = 17,
                            Description = "Revert Refund",
                            Name = "RevertRefund"
                        },
                        new
                        {
                            Id = 18,
                            Description = "Settlement Admin Fee. Manual.",
                            Name = "SettlementAdminFee"
                        },
                        new
                        {
                            Id = 19,
                            Description = "Outstanding License Admin Fee. Manual.",
                            Name = "OutstandingLicenseAdminFee"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BankAccount", b =>
                {
                    b.HasOne("WeBuyCars.Financial.Core.Entities.BankAccountHolder", "BankAccountHolder")
                        .WithMany()
                        .HasForeignKey("BankAccountHolderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.BankAccountType", "BankAccountType")
                        .WithMany()
                        .HasForeignKey("BankAccountTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BankAccountHolder");

                    b.Navigation("BankAccountType");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BankAccountHolder", b =>
                {
                    b.HasOne("WeBuyCars.Financial.Core.Entities.BankAccountHolderType", "BankAccountHolderType")
                        .WithMany()
                        .HasForeignKey("BankAccountHolderTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BankAccountHolderType");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BankAccountVerification", b =>
                {
                    b.HasOne("WeBuyCars.Financial.Core.Entities.BankAccount", "BankAccount")
                        .WithMany()
                        .HasForeignKey("BankAccountId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.Provider", "Provider")
                        .WithMany()
                        .HasForeignKey("ProviderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BankAccount");

                    b.Navigation("Provider");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BankAccountVerificationBuyLead", b =>
                {
                    b.HasOne("WeBuyCars.Financial.Core.Entities.BankAccountVerification", "BankAccountVerification")
                        .WithMany("BankAccountVerificationBuyLeads")
                        .HasForeignKey("BankAccountVerificationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BankAccountVerification");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BuyLead", b =>
                {
                    b.HasOne("WeBuyCars.Financial.Core.Entities.BuyLeadBuyer", "BuyLeadBuyer")
                        .WithMany("BuyLeads")
                        .HasForeignKey("BuyLeadBuyerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.BuyLeadCompleteStatus", "BuyLeadCompleteStatus")
                        .WithMany()
                        .HasForeignKey("BuyLeadCompleteStatusId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.BuyLeadProcess", "BuyLeadProcess")
                        .WithMany()
                        .HasForeignKey("BuyLeadProcessId");

                    b.HasOne("WeBuyCars.Financial.Core.Entities.Natis", "Natis")
                        .WithMany()
                        .HasForeignKey("NatisId");

                    b.Navigation("BuyLeadBuyer");

                    b.Navigation("BuyLeadCompleteStatus");

                    b.Navigation("BuyLeadProcess");

                    b.Navigation("Natis");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BuyLeadClient", b =>
                {
                    b.HasOne("WeBuyCars.Financial.Core.Entities.BuyLead", "BuyLead")
                        .WithOne("BuyLeadClient")
                        .HasForeignKey("WeBuyCars.Financial.Core.Entities.BuyLeadClient", "BuyLeadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BuyLead");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BuyLeadDocument", b =>
                {
                    b.HasOne("WeBuyCars.Financial.Core.Entities.BuyLeadDocumentDescription", "BuyLeadDocumentDescription")
                        .WithMany("BuyLeadDocuments")
                        .HasForeignKey("BuyLeadDocumentDescriptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.BuyLeadDocumentStatus", "BuyLeadDocumentStatus")
                        .WithMany("BuyLeadDocuments")
                        .HasForeignKey("BuyLeadDocumentStatusId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.BuyLeadDocumentType", "BuyLeadDocumentType")
                        .WithMany("BuyLeadDocuments")
                        .HasForeignKey("BuyLeadDocumentTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.BuyLead", "BuyLead")
                        .WithMany("BuyLeadDocuments")
                        .HasForeignKey("BuyLeadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BuyLead");

                    b.Navigation("BuyLeadDocumentDescription");

                    b.Navigation("BuyLeadDocumentStatus");

                    b.Navigation("BuyLeadDocumentType");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BuyLeadDocumentStatusHistory", b =>
                {
                    b.HasOne("WeBuyCars.Financial.Core.Entities.BuyLeadDocument", "BuyLeadDocument")
                        .WithMany("BuyLeadDocumentStatusHistories")
                        .HasForeignKey("BuyLeadDocumentId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.BuyLeadDocumentStatus", "BuyLeadDocumentStatus")
                        .WithMany()
                        .HasForeignKey("BuyLeadDocumentStatusId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("BuyLeadDocument");

                    b.Navigation("BuyLeadDocumentStatus");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BuyLeadHistory", b =>
                {
                    b.HasOne("WeBuyCars.Financial.Core.Entities.BuyLeadCompleteStatus", "BuyLeadCompleteStatus")
                        .WithMany()
                        .HasForeignKey("BuyLeadCompleteStatusId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.BuyLead", "BuyLead")
                        .WithMany("BuyLeadHistories")
                        .HasForeignKey("BuyLeadId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("BuyLead");

                    b.Navigation("BuyLeadCompleteStatus");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BuyLeadVehicle", b =>
                {
                    b.HasOne("WeBuyCars.Financial.Core.Entities.BuyLead", "BuyLead")
                        .WithOne("BuyLeadVehicle")
                        .HasForeignKey("WeBuyCars.Financial.Core.Entities.BuyLeadVehicle", "BuyLeadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BuyLead");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.Consent", b =>
                {
                    b.HasOne("WeBuyCars.Financial.Core.Entities.Transaction", null)
                        .WithMany("Consents")
                        .HasForeignKey("TransactionId");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.HyphenTransactionDetail", b =>
                {
                    b.HasOne("WeBuyCars.Financial.Core.Entities.Bank", "Bank")
                        .WithMany()
                        .HasForeignKey("BankId");

                    b.HasOne("WeBuyCars.Financial.Core.Entities.BankAccountHolderType", "ClientBankAccountHolderType")
                        .WithMany()
                        .HasForeignKey("ClientBankAccountHolderTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.BankAccountType", "ClientBankAccountType")
                        .WithMany()
                        .HasForeignKey("ClientBankAccountTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.HyphenProcessingOption1", "ProcessingOption1")
                        .WithMany()
                        .HasForeignKey("ProcessingOption1Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.HyphenProcessingOption2", "ProcessingOption2")
                        .WithMany()
                        .HasForeignKey("ProcessingOption2Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.Transaction", "Transaction")
                        .WithMany()
                        .HasForeignKey("TransactionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.HyphenTransactionType", "TransactionType")
                        .WithMany()
                        .HasForeignKey("TransactionTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Bank");

                    b.Navigation("ClientBankAccountHolderType");

                    b.Navigation("ClientBankAccountType");

                    b.Navigation("ProcessingOption1");

                    b.Navigation("ProcessingOption2");

                    b.Navigation("Transaction");

                    b.Navigation("TransactionType");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.HyphenTransactionType", b =>
                {
                    b.HasOne("WeBuyCars.Financial.Core.Entities.Bank", "Bank")
                        .WithMany("HyphenTransactionTypes")
                        .HasForeignKey("BankId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.TransactionType", "TransactionType")
                        .WithMany()
                        .HasForeignKey("TransactionTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Bank");

                    b.Navigation("TransactionType");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.Natis", b =>
                {
                    b.HasOne("WeBuyCars.Financial.Core.Entities.NatisStatus", "NatisStatus")
                        .WithMany()
                        .HasForeignKey("NatisStatusId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("NatisStatus");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.OutstandingPaymentTransactionHistory", b =>
                {
                    b.HasOne("WeBuyCars.Financial.Core.Entities.Account", "Account")
                        .WithMany()
                        .HasForeignKey("AccountId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.AccountType", "AccountType")
                        .WithMany()
                        .HasForeignKey("AccountTypeId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.Provider", "Provider")
                        .WithMany()
                        .HasForeignKey("ProviderId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.Transaction", "Transaction")
                        .WithMany()
                        .HasForeignKey("TransactionId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.TransactionStatus", "TransactionStatus")
                        .WithMany()
                        .HasForeignKey("TransactionStatusId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.TransactionType", "TransactionType")
                        .WithMany()
                        .HasForeignKey("TransactionTypeId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Account");

                    b.Navigation("AccountType");

                    b.Navigation("Provider");

                    b.Navigation("Transaction");

                    b.Navigation("TransactionStatus");

                    b.Navigation("TransactionType");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.PaymentConfiguration", b =>
                {
                    b.HasOne("WeBuyCars.Financial.Core.Entities.Account", "Account")
                        .WithMany()
                        .HasForeignKey("AccountId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.BankAccount", "BankAccount")
                        .WithMany()
                        .HasForeignKey("BankAccountId");

                    b.HasOne("WeBuyCars.Financial.Core.Entities.BuyLead", "BuyLead")
                        .WithMany("PaymentConfigurations")
                        .HasForeignKey("BuyLeadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.FinanceHouse", "FinanceHouse")
                        .WithMany()
                        .HasForeignKey("FinanceHouseId");

                    b.HasOne("WeBuyCars.Financial.Core.Entities.Provider", "Provider")
                        .WithMany()
                        .HasForeignKey("ProviderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.ShortfallBankAccount", "ShortfallBankAccount")
                        .WithMany()
                        .HasForeignKey("ShortfallBankAccountId");

                    b.HasOne("WeBuyCars.Financial.Core.Entities.SourceOfFund", "SourceOfFund")
                        .WithMany("PaymentConfigurations")
                        .HasForeignKey("SourceOfFundId");

                    b.HasOne("WeBuyCars.Financial.Core.Entities.TransactionType", "TransactionType")
                        .WithMany()
                        .HasForeignKey("TransactionTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Account");

                    b.Navigation("BankAccount");

                    b.Navigation("BuyLead");

                    b.Navigation("FinanceHouse");

                    b.Navigation("Provider");

                    b.Navigation("ShortfallBankAccount");

                    b.Navigation("SourceOfFund");

                    b.Navigation("TransactionType");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.Transaction", b =>
                {
                    b.HasOne("WeBuyCars.Financial.Core.Entities.Account", "Account")
                        .WithMany("Transactions")
                        .HasForeignKey("AccountId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.AccountType", "AccountType")
                        .WithMany()
                        .HasForeignKey("AccountTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.Provider", "Provider")
                        .WithMany()
                        .HasForeignKey("ProviderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.RefundReason", "RefundReason")
                        .WithMany()
                        .HasForeignKey("RefundReasonId");

                    b.HasOne("WeBuyCars.Financial.Core.Entities.TransactionCancellationReason", "TransactionCancellationReason")
                        .WithMany()
                        .HasForeignKey("TransactionCancellationReasonId");

                    b.HasOne("WeBuyCars.Financial.Core.Entities.TransactionStatus", "TransactionStatus")
                        .WithMany()
                        .HasForeignKey("TransactionStatusId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.TransactionType", "TransactionType")
                        .WithMany()
                        .HasForeignKey("TransactionTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Account");

                    b.Navigation("AccountType");

                    b.Navigation("Provider");

                    b.Navigation("RefundReason");

                    b.Navigation("TransactionCancellationReason");

                    b.Navigation("TransactionStatus");

                    b.Navigation("TransactionType");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.Account", b =>
                {
                    b.Navigation("Transactions");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.Bank", b =>
                {
                    b.Navigation("HyphenTransactionTypes");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BankAccountVerification", b =>
                {
                    b.Navigation("BankAccountVerificationBuyLeads");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BuyLead", b =>
                {
                    b.Navigation("BuyLeadClient");

                    b.Navigation("BuyLeadDocuments");

                    b.Navigation("BuyLeadHistories");

                    b.Navigation("BuyLeadVehicle");

                    b.Navigation("PaymentConfigurations");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BuyLeadBuyer", b =>
                {
                    b.Navigation("BuyLeads");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BuyLeadDocument", b =>
                {
                    b.Navigation("BuyLeadDocumentStatusHistories");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BuyLeadDocumentDescription", b =>
                {
                    b.Navigation("BuyLeadDocuments");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BuyLeadDocumentStatus", b =>
                {
                    b.Navigation("BuyLeadDocuments");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BuyLeadDocumentType", b =>
                {
                    b.Navigation("BuyLeadDocuments");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.SourceOfFund", b =>
                {
                    b.Navigation("PaymentConfigurations");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.Transaction", b =>
                {
                    b.Navigation("Consents");
                });
#pragma warning restore 612, 618
        }
    }
}
