using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WeBuyCars.Financial.Infrastructure.Data.en_NA.Migrations
{
    public partial class AddShortfallBankAccountHolder : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "AccountHolder",
                schema: "FIN",
                table: "ShortfallBankAccount",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: false,
                defaultValue: "");

            migrationBuilder.UpdateData(
                schema: "FIN",
                table: "ShortfallBankAccount",
                keyColumn: "Id",
                keyValue: 1L,
                column: "AccountHolder",
                value: "We Buy Cars (Pty) Ltd");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AccountHolder",
                schema: "FIN",
                table: "ShortfallBankAccount");
        }
    }
}
