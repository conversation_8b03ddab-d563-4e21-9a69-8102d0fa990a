using WeBuyCars.Financial.Core.Entities;
using WeBuyCars.Financial.Core.SharedKernel;
using WeBuyCars.Financial.Infrastructure.Data.Context;

namespace WeBuyCars.Financial.Infrastructure.Data.Repositories
{
    public class BankAccountVerificationBuyLeadRepository : 
        Repository<BankAccountVerificationBuyLead>, 
        IBankAccountVerificationBuyLeadRepository
    {
        public BankAccountVerificationBuyLeadRepository(FinancialContext context) : base(context)
        {
        }
    }
}