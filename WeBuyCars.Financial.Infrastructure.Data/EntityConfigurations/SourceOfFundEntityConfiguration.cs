using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WeBuyCars.Financial.Core.Entities;

namespace WeBuyCars.Financial.Infrastructure.Data.EntityConfigurations;

public sealed class SourceOfFundEntityConfiguration : IEntityTypeConfiguration<SourceOfFund>
{
    public void Configure(EntityTypeBuilder<SourceOfFund> builder)
    {
        builder.ToTable(nameof(SourceOfFund));
        
        builder.HasKey(i => i.Id);
        
        builder.Property(i => i.Source)
            .HasMaxLength(200)
            .IsRequired();
        
        builder.Property(i => i.Deleted)
            .HasDefaultValue(false);

        builder.Property(i => i.CreatedOn)
            .IsRequired();

        builder.Property(i => i.ModifiedOn)
            .IsRequired(false);

        builder.Property(i => i.CreatedBy)
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(i => i.ModifiedBy)
            .HasMaxLength(200);

        builder.HasQueryFilter(m => EF.Property<bool>(m, "Deleted") == false);
    }
}