using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WeBuyCars.Financial.Core.Entities;
using WeBuyCars.Financial.Core.Extensions;

namespace WeBuyCars.Financial.Infrastructure.Data.EntityConfigurations
{
    public class BankAccountVerificationMatchTypeEntityConfiguration : IEntityTypeConfiguration<BankAccountVerificationMatchType>
    {
        public void Configure(EntityTypeBuilder<BankAccountVerificationMatchType> builder)
        {
            builder.ToTable("BankAccountVerificationMatchType");

            builder.HasKey(i => i.Id);

            builder.Property(i => i.Id)
                .HasConversion<int>()
                .ValueGeneratedNever()
                .IsRequired();

            builder.Property(i => i.Name)
                .HasMaxLength(20)
                .IsRequired();

            builder.Property(i => i.Code)
                .HasMaxLength(2)
                .IsRequired();

            builder.HasData(BankAccountVerificationMatchType.List.Select(i => new { Id = i, Name = i.ToString(), Code = i.GetDescription() }));
        }
    }
}