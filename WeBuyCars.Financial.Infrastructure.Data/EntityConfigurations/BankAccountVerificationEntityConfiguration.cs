using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WeBuyCars.Financial.Core.Entities;

namespace WeBuyCars.Financial.Infrastructure.Data.EntityConfigurations
{
    public class BankAccountVerificationEntityConfiguration : IEntityTypeConfiguration<BankAccountVerification>
    {
        public void Configure(EntityTypeBuilder<BankAccountVerification> builder)
        {
            builder.ToTable("BankAccountVerification");

            builder.HasKey(i => i.Id);

            builder.Property(i => i.BuyLeadReference)
                .HasMaxLength(20)
                .IsRequired(false);

            builder.Property(i => i.RequestDateTime)
                .IsRequired();
            
            builder.Property(i => i.ReRequestDateTime)
                .IsRequired(false);

            builder.Property(i => i.Operator)
                .HasMaxLength(10)
                .IsRequired();

            builder.Property(i => i.UserReference)
                .HasMaxLength(20)
                .IsRequired();

            builder.Property(i => i.ClientReference)
                .HasMaxLength(20)
                .IsRequired();

            builder.Property(i => i.AccountExists)
                .IsRequired(false);

            builder.Property(i => i.AccountIdNumberMatch)
                .IsRequired(false);

            builder.Property(i => i.AccountPassportNumberMatch)
                .IsRequired(false);

            builder.Property(i => i.AccountCompanyRegistrationNumberMatch)
                .IsRequired(false);

            builder.Property(i => i.AccountEstateNumberMatch)
                .IsRequired(false);

            builder.Property(i => i.InitialMatch)
                .IsRequired(false);

            builder.Property(i => i.LastNameMatch)
                .IsRequired(false);

            builder.Property(i => i.CompanyNameMatch)
                .IsRequired(false);

            builder.Property(i => i.EstateNameMatch)
                .IsRequired(false);

            builder.Property(i => i.AccountOpen)
                .IsRequired(false);

            builder.Property(i => i.AccountAcceptsCredits)
                .IsRequired(false);

            builder.Property(i => i.AccountAcceptsDebits)
                .IsRequired(false);

            builder.Property(i => i.AccountOpenGtThreeMonths)
                .IsRequired(false);

            builder.Property(i => i.PhoneValid)
                .IsRequired(false);

            builder.Property(i => i.EmailValid)
                .IsRequired(false);

            builder.Property(i => i.AccountTypeValid)
                .IsRequired(false);

            builder.Property(i => i.TransactionReference)
                .HasMaxLength(30)
                .IsRequired(false);

            builder.Property(i => i.MessageCode)
                .HasMaxLength(10)
                .IsRequired(false);

            builder.Property(i => i.MessageDescription)
                .HasMaxLength(100)
                .IsRequired(false);

            builder.HasOne(i => i.BankAccount)
                .WithMany()
                .HasForeignKey(j => j.BankAccountId)
                .IsRequired();

            builder.HasOne(i => i.Provider)
                .WithMany()
                .HasForeignKey(j => j.ProviderId)
                .IsRequired();

            builder.HasIndex(i => i.UserReference).IsUnique();

            builder.HasIndex(i => i.ClientReference).IsUnique();

            builder.Property(i => i.Deleted)
                .HasDefaultValue(false);

            builder.Property(i => i.CreatedOn).IsRequired();

            builder.Property(i => i.ModifiedOn).IsRequired(false);

            builder.Property(i => i.CreatedBy)
                .HasMaxLength(200)
                .IsRequired();

            builder.Property(i => i.ModifiedBy)
                .HasMaxLength(200);

            builder.HasQueryFilter(m => EF.Property<bool>(m, "Deleted") == false);
        }
    }
}