using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WeBuyCars.Financial.Core.Entities;

namespace WeBuyCars.Financial.Infrastructure.Data.EntityConfigurations
{
    public class BuyLeadDocumentStatusHistoryEntityConfiguration : IEntityTypeConfiguration<BuyLeadDocumentStatusHistory>
    {
        public void Configure(EntityTypeBuilder<BuyLeadDocumentStatusHistory> builder)
        {
            builder.ToTable("BuyLeadDocumentStatusHistory");

            builder.HasKey(i => i.Id);

            builder.Property(i => i.BuyLeadDocumentStatusLastChangedBy)
                .IsRequired(false);

            builder.Property(i => i.BuyLeadDocumentStatusLastChangedOn)
                .IsRequired(false);

            builder.Property(i => i.Deleted)
                .HasDefaultValue(false);

            builder.Property(i => i.CreatedOn).IsRequired();

            builder.Property(i => i.ModifiedOn).IsRequired(false);

            builder.Property(i => i.CreatedBy)
                .HasMaxLength(200)
                .IsRequired();

            builder.Property(i => i.ModifiedBy)
                .HasMaxLength(200);

            builder.HasOne(i => i.BuyLeadDocumentStatus)
                .WithMany()
                .HasForeignKey("BuyLeadDocumentStatusId")
                .IsRequired(false)
                .OnDelete(DeleteBehavior.NoAction);

            builder.HasOne(i => i.BuyLeadDocument)
                .WithMany(j => j.BuyLeadDocumentStatusHistories)
                .HasForeignKey(i => i.BuyLeadDocumentId)
                .IsRequired()
                .OnDelete(DeleteBehavior.NoAction);

            builder.HasQueryFilter(m => EF.Property<bool>(m, "Deleted") == false);
        }
    }
}