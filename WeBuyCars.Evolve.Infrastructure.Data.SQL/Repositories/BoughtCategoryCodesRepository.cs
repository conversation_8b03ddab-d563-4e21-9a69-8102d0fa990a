using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using WeBuyCars.Evolve.Core.Entities;
using WeBuyCars.Evolve.Core.SharedKernel;
using WeBuyCars.Evolve.Infrastructure.Data.SQL.Context;

namespace WeBuyCars.Evolve.Infrastructure.Data.SQL.Repositories;

public sealed class BoughtCategoryCodesRepository(WBCEvolveContext context)
    : Repository<BoughtCategoryCode>(context), IBoughtCategoryCodeRepository
{
    public async Task<List<BoughtCategoryCode>> GetByWarehouseCode(string code, CancellationToken cancellationToken)
    {
        return await _context
            .BoughtCategoryCodes
            .Where(bcc => bcc.WarehouseCode == code)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<BoughtCategoryCode>> GetBoughtCategoryCodes(CancellationToken cancellationToken)
    {
        return await _context
            .BoughtCategoryCodes
            .ToListAsync(cancellationToken);
    }
}