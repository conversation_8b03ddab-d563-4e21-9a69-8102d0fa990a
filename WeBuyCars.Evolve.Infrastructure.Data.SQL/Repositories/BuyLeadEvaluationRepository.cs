using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using WeBuyCars.Evolve.Core.Entities;
using WeBuyCars.Evolve.Core.SharedKernel;
using WeBuyCars.Evolve.Infrastructure.Data.SQL.Context;

namespace WeBuyCars.Evolve.Infrastructure.Data.SQL.Repositories
{
    public sealed class BuyLeadEvaluationRepository : Repository<BuyLeadEvaluation>, IBuyLeadEvaluationRepository
    {
        public BuyLeadEvaluationRepository(WBCEvolveContext context) : base(context)
        {
        }

        public async Task<BuyLeadEvaluation> GetByBuyLeadIdAsync(string buyLeadId)
        {
            var buyLeadEvaluation = await _context.BuyLeadEvaluations.FirstOrDefaultAsync(i => i.BuyLeadId == buyLeadId);
            return buyLeadEvaluation;
        }

    }
}
