using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WeBuyCars.Evolve.Core.Entities;

namespace WeBuyCars.Evolve.Infrastructure.Data.SQL.EntityConfigurations
{
    public sealed class EvolveStockItemEntityConfiguration : IEntityTypeConfiguration<EvolveStockItem>
    {
        public void Configure(EntityTypeBuilder<EvolveStockItem> builder)
        {
            builder.ToTable("EvolveStockItem");

            builder.HasKey(i => i.Id);

            builder.Property(i => i.StockNumber)
                .IsRequired();

            builder.Property(i => i.EvolveCreationSuccess)
                .IsRequired(false);

            builder.Property(i => i.EvolveCreatedDateTime)
                .IsRequired(false);
            
            builder.Property(i => i.FailureExplainReason)
                .IsRequired(false);

            builder.Property(i => i.EvolveInitialFullStockMaintenaceRequest)
                .IsRequired(false);

            builder.Property(i => i.EvolveInitialFullStockMaintenaceResponse)
                .IsRequired(false);

            builder.Property(i => i.CreatedOn)
                .IsRequired();

            builder.Property(i => i.ModifiedOn)
                .IsRequired(false);

            builder.Property(i => i.CreatedBy)
                .HasMaxLength(200)
                .IsRequired();

            builder.Property(i => i.ModifiedBy)
                .HasMaxLength(200)
                .IsRequired(false);

            builder.HasIndex(i => i.StockNumber)
                .IsUnique();

            builder.HasQueryFilter(m => EF.Property<bool>(m, "Deleted") == false);
        }
    }
}