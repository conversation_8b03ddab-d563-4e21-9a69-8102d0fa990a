using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WeBuyCars.Evolve.Core.Entities;

namespace WeBuyCars.Evolve.Infrastructure.Data.SQL.EntityConfigurations;

public sealed class ImsDailySalesSnapshotEntityConfiguration : IEntityTypeConfiguration<ImsDailySalesSnapshot>
{
    #region Public Methods

    public void Configure(EntityTypeBuilder<ImsDailySalesSnapshot> builder)
    {
        builder.ToView(nameof(ImsDailySalesSnapshot));

        builder.HasNoKey();

        builder.Ignore(i => i.Id);
        builder.Ignore(i => i.Deleted);
        builder.Ignore(i => i.CreatedBy);
        builder.Ignore(i => i.CreatedOn);
        builder.Ignore(i => i.ModifiedOn);
        builder.Ignore(i => i.ModifiedBy);
    }

    #endregion
}