using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WeBuyCars.Evolve.Core.Entities;
using WeBuyCars.Evolve.Infrastructure.Data.SQL.Extensions;

namespace WeBuyCars.Evolve.Infrastructure.Data.SQL.EntityConfigurations;

public class BoughtCategoryCodesEntityConfiguration : IEntityTypeConfiguration<BoughtCategoryCode>
{
    public void Configure(EntityTypeBuilder<BoughtCategoryCode> builder)
    {
        builder.ToTable(nameof(BoughtCategoryCode));
        
        builder.AddEntityConfiguration();
        
        builder.Property(i => i.LocationName)
            .HasMaxLength(BoughtCategoryCodesConstants.LocationNameMaxLength);
        
        builder.Property(i => i.BoughtType)
            .HasMaxLength(BoughtCategoryCodesConstants.BoughtTypeMaxLength);
        
        builder.Property(i => i.WarehouseCode)
            .HasMaxLength(BoughtCategoryCodesConstants.WarehouseCodeMaxLength);
        
        builder.Property(i => i.BuyCategoryCode)
            .HasMaxLength(BoughtCategoryCodesConstants.BuyCategoryCodeMaxLength);
        
        builder.HasIndex(i => new { i.WarehouseCode, i.BuyCategoryCode })
            .IsUnique();
        
        builder.HasQueryFilter(m => EF.Property<bool>(m, "Deleted") == false);
    }
}