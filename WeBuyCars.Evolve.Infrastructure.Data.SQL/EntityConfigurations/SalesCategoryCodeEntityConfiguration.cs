using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WeBuyCars.Evolve.Core.Entities;
using WeBuyCars.Evolve.Infrastructure.Data.SQL.Extensions;

namespace WeBuyCars.Evolve.Infrastructure.Data.SQL.EntityConfigurations;

public sealed class SalesCategoryCodeEntityConfiguration : IEntityTypeConfiguration<SalesCategoryCode>
{
    public void Configure(EntityTypeBuilder<SalesCategoryCode> builder)
    {
        builder.ToTable(nameof(SalesCategoryCode));
        
        builder.AddEntityConfiguration();
        
        builder.Property(i => i.SalesType)
            .HasMaxLength(SalesCategoryCodeConstants.SalesTypeMaxLength);
        
        builder.Property(i => i.ClientType)
            .HasMaxLength(SalesCategoryCodeConstants.ClientTypeMaxLength);
        
        builder.Property(i => i.SaleClientCode)
            .HasMaxLength(SalesCategoryCodeConstants.SaleClientCodeMaxLength);
        
        builder.Property(i => i.LocationName)
            .HasMaxLength(SalesCategoryCodeConstants.LocationNameMaxLength);
        
        builder.Property(i => i.LocationCode)
            .HasMaxLength(SalesCategoryCodeConstants.LocationCodeMaxLength);
        
        builder.Property(i => i.WarehouseCode)
            .HasMaxLength(SalesCategoryCodeConstants.WarehouseCodeMaxLength);
        
        builder.Property(i => i.SaleCategoryCode)
            .HasMaxLength(SalesCategoryCodeConstants.SaleCategoryCodeMaxLength);
        
        builder.HasIndex(i => new { i.WarehouseCode, i.SaleCategoryCode })
            .IsUnique();
        
        builder.HasQueryFilter(m => EF.Property<bool>(m, "Deleted") == false);
    }
}