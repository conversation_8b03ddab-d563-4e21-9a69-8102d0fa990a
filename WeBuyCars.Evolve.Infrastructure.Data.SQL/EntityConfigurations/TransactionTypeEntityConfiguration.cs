using System;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WeBuyCars.Evolve.Core.Entities;
using WeBuyCars.Evolve.Core.Enumerations;

namespace WeBuyCars.Evolve.Infrastructure.Data.SQL.EntityConfigurations
{
    public sealed class TransactionTypeEntityConfiguration : IEntityTypeConfiguration<TransactionType>
    {
        public void Configure(EntityTypeBuilder<TransactionType> builder)
        {
            builder.ToTable("TransactionType");

            builder.HasKey(i => i.Id);

            builder.Property(i => i.Id)
                .HasConversion<int>()
                .ValueGeneratedNever()
                .IsRequired();

            builder.Property(i => i.Name)
                .HasMaxLength(20)
                .IsRequired();

            var values = Enum.GetValues(typeof(TransactionEnum)).Cast<TransactionEnum>();
            builder.HasData(values
                .Select(i => 
                    new { Id = i, Name = i.ToString() }));
        }
    }
}
