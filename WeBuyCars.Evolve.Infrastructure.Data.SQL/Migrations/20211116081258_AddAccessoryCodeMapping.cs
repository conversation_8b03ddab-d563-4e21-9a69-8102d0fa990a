using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace WeBuyCars.Evolve.Infrastructure.Data.SQL.Migrations
{
    public partial class AddAccessoryCodeMapping : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AccessoryCode",
                schema: "Evolve",
                columns: table => new
                {
                    Id = table.Column<long>(nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ModifiedOn = table.Column<DateTimeOffset>(nullable: true),
                    ModifiedBy = table.Column<string>(maxLength: 200, nullable: true),
                    Deleted = table.Column<bool>(nullable: false, defaultValue: false),
                    CreatedOn = table.Column<DateTimeOffset>(nullable: false, defaultValueSql: "GETUTCDATE()"),
                    CreatedBy = table.Column<string>(maxLength: 200, nullable: false, defaultValue: "System"),
                    Location = table.Column<string>(maxLength: 3, nullable: false),
                    MinCode = table.Column<string>(maxLength: 4, nullable: false),
                    MaxCode = table.Column<string>(maxLength: 4, nullable: false),
                    LimitValue = table.Column<decimal>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AccessoryCode", x => x.Id);
                });

            migrationBuilder.InsertData(
                schema: "Evolve",
                table: "AccessoryCode",
                columns: new[] { "Id", "LimitValue", "Location", "MaxCode", "MinCode", "ModifiedBy", "ModifiedOn" },
                values: new object[,]
                {
                    { 1L, 20000m, "MID", "1501", "1502", null, null },
                    { 2L, 20000m, "SIL", "2501", "2502", null, null },
                    { 3L, 20000m, "PEL", "6501", "6502", null, null },
                    { 4L, 20000m, "PHU", "7501", "7502", null, null },
                    { 5L, 20000m, "CPT", "4501", "4502", null, null },
                    { 6L, 20000m, "DBN", "3501", "3502", null, null },
                    { 7L, 20000m, "JBS", "5501", "5502", null, null },
                    { 8L, 20000m, "GER", "8501", "8502", null, null }
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AccessoryCode",
                schema: "Evolve");

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 1L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 8, 27, 10, 15, 47, 848, DateTimeKind.Unspecified).AddTicks(9192), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 2L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 8, 27, 10, 15, 47, 849, DateTimeKind.Unspecified).AddTicks(1105), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 3L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 8, 27, 10, 15, 47, 849, DateTimeKind.Unspecified).AddTicks(1157), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 4L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 8, 27, 10, 15, 47, 849, DateTimeKind.Unspecified).AddTicks(1160), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 5L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 8, 27, 10, 15, 47, 849, DateTimeKind.Unspecified).AddTicks(1163), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 6L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 8, 27, 10, 15, 47, 849, DateTimeKind.Unspecified).AddTicks(1171), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 7L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 8, 27, 10, 15, 47, 849, DateTimeKind.Unspecified).AddTicks(1175), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 8L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 8, 27, 10, 15, 47, 849, DateTimeKind.Unspecified).AddTicks(1177), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 9L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 8, 27, 10, 15, 47, 849, DateTimeKind.Unspecified).AddTicks(1179), new TimeSpan(0, 0, 0, 0, 0)) });
        }
    }
}
