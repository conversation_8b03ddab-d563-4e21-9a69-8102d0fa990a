using Microsoft.EntityFrameworkCore.Migrations;

namespace WeBuyCars.Evolve.Infrastructure.Data.SQL.Migrations
{
    public partial class AddPolokwaneBranch : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "MinCode",
                schema: "Evolve",
                table: "AccessoryCode",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(4)",
                oldMaxLength: 4);

            migrationBuilder.AlterColumn<string>(
                name: "MaxCode",
                schema: "Evolve",
                table: "AccessoryCode",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(4)",
                oldMaxLength: 4);

            migrationBuilder.InsertData(
                schema: "Evolve",
                table: "AccessoryCode",
                columns: new[] { "Id", "LimitValue", "Location", "MaxCode", "MinCode", "ModifiedBy", "ModifiedOn" },
                values: new object[] { 10L, 20000m, "POL", "10501", "10502", null, null });

            migrationBuilder.InsertData(
                schema: "Evolve",
                table: "Branch",
                columns: new[] { "Id", "Code", "ModifiedBy", "ModifiedOn", "Name", "SalesLocationCode" },
                values: new object[] { 11L, "POL", null, null, "Polokwane Branch", "L" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 10L);

            migrationBuilder.DeleteData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 11L);

            migrationBuilder.AlterColumn<string>(
                name: "MinCode",
                schema: "Evolve",
                table: "AccessoryCode",
                type: "nvarchar(4)",
                maxLength: 4,
                nullable: false,
                oldClrType: typeof(string));

            migrationBuilder.AlterColumn<string>(
                name: "MaxCode",
                schema: "Evolve",
                table: "AccessoryCode",
                type: "nvarchar(4)",
                maxLength: 4,
                nullable: false,
                oldClrType: typeof(string));
        }
    }
}
