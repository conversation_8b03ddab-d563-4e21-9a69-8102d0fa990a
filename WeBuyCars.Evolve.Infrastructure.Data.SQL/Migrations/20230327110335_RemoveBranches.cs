using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WeBuyCars.Evolve.Infrastructure.Data.SQL.Migrations
{
    public partial class RemoveBranches : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "<PERSON>",
                schema: "Evolve");

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 1L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 3, 27, 11, 3, 34, 815, DateTimeKind.Unspecified).AddTicks(1670), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 2L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 3, 27, 11, 3, 34, 815, DateTimeKind.Unspecified).AddTicks(1690), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 3L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 3, 27, 11, 3, 34, 815, DateTimeKind.Unspecified).AddTicks(1690), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 4L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 3, 27, 11, 3, 34, 815, DateTimeKind.Unspecified).AddTicks(1690), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 5L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 3, 27, 11, 3, 34, 815, DateTimeKind.Unspecified).AddTicks(1690), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 6L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 3, 27, 11, 3, 34, 815, DateTimeKind.Unspecified).AddTicks(1700), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 7L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 3, 27, 11, 3, 34, 815, DateTimeKind.Unspecified).AddTicks(1700), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 8L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 3, 27, 11, 3, 34, 815, DateTimeKind.Unspecified).AddTicks(1700), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 9L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 3, 27, 11, 3, 34, 815, DateTimeKind.Unspecified).AddTicks(1700), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 10L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 3, 27, 11, 3, 34, 815, DateTimeKind.Unspecified).AddTicks(1700), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 11L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 3, 27, 11, 3, 34, 815, DateTimeKind.Unspecified).AddTicks(1700), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 12L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 3, 27, 11, 3, 34, 815, DateTimeKind.Unspecified).AddTicks(1710), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 13L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 3, 27, 11, 3, 34, 815, DateTimeKind.Unspecified).AddTicks(1710), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 14L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 3, 27, 11, 3, 34, 815, DateTimeKind.Unspecified).AddTicks(1710), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 15L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 3, 27, 11, 3, 34, 815, DateTimeKind.Unspecified).AddTicks(1710), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.InsertData(
                schema: "Evolve",
                table: "AccessoryCode",
                columns: new[] { "Id", "CreatedBy", "CreatedOn", "DealerTermsCode", "DeliveryCode", "DocumentationFeeLimitValue", "DocumentationFeeMaxCode", "DocumentationFeeMaxMinCode", "LicenseAndRegistrationCode", "Location", "ModifiedBy", "ModifiedOn", "WarrantyCode" },
                values: new object[] { 16L, "System", new DateTimeOffset(new DateTime(2023, 3, 27, 11, 3, 34, 815, DateTimeKind.Unspecified).AddTicks(1710), new TimeSpan(0, 0, 0, 0, 0)), "18401", "18002", 20000m, "18501", "18502", "18001", "PMB", null, null, "18301" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 16L);

            migrationBuilder.CreateTable(
                name: "Branch",
                schema: "Evolve",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Code = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, defaultValue: "System"),
                    CreatedOn = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    Deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    ModifiedBy = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    ModifiedOn = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    SalesLocationCode = table.Column<string>(type: "nvarchar(1)", maxLength: 1, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Branch", x => x.Id);
                });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 1L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(1878), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 2L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(1888), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 3L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(1889), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 4L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(1891), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 5L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(1893), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 6L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(1895), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 7L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(1896), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 8L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(1898), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 9L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(1899), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 10L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(1901), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 11L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(1902), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 12L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(1903), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 13L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(1905), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 14L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(1906), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 15L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(1907), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.InsertData(
                schema: "Evolve",
                table: "Branch",
                columns: new[] { "Id", "Code", "CreatedBy", "CreatedOn", "Deleted", "ModifiedBy", "ModifiedOn", "Name", "SalesLocationCode" },
                values: new object[,]
                {
                    { 1L, "CPT", "System", new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(3717), new TimeSpan(0, 0, 0, 0, 0)), false, null, null, "Capetown 1 Brackengate Branch", "C" },
                    { 2L, "PHU", "System", new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(3722), new TimeSpan(0, 0, 0, 0, 0)), false, null, null, "Capetown 2 Phumelela Park Branch", "H" },
                    { 3L, "DBN", "System", new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(3723), new TimeSpan(0, 0, 0, 0, 0)), false, null, null, "Durban 2 Branch", "D" },
                    { 4L, "DBN", "System", new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(3724), new TimeSpan(0, 0, 0, 0, 0)), false, null, null, "Durban Branch", "D" },
                    { 5L, "JBS", "System", new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(3725), new TimeSpan(0, 0, 0, 0, 0)), false, null, null, "JoBurg South Branch", "J" },
                    { 6L, "MID", "System", new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(3727), new TimeSpan(0, 0, 0, 0, 0)), false, null, null, "Midstream Branch", "M" },
                    { 7L, "SIL", "System", new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(3728), new TimeSpan(0, 0, 0, 0, 0)), false, null, null, "Silverlakes Branch", "S" },
                    { 8L, "PEL", "System", new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(3728), new TimeSpan(0, 0, 0, 0, 0)), false, null, null, "PortElizabeth Branch", "P" },
                    { 9L, "GER", "System", new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(3729), new TimeSpan(0, 0, 0, 0, 0)), false, null, null, "Germiston Branch", "G" },
                    { 10L, "DOM", "System", new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(3731), new TimeSpan(0, 0, 0, 0, 0)), false, null, null, "Dome Branch", "O" },
                    { 11L, "POL", "System", new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(3732), new TimeSpan(0, 0, 0, 0, 0)), false, null, null, "Polokwane Branch", "L" },
                    { 12L, "NEL", "System", new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(3733), new TimeSpan(0, 0, 0, 0, 0)), false, null, null, "Nelspruit Branch", "N" },
                    { 13L, "RIV", "System", new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(3734), new TimeSpan(0, 0, 0, 0, 0)), false, null, null, "Riverhorse Branch", "V" },
                    { 14L, "EPP", "System", new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(3734), new TimeSpan(0, 0, 0, 0, 0)), false, null, null, "Epping Branch", "I" },
                    { 15L, "RIC", "System", new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(3736), new TimeSpan(0, 0, 0, 0, 0)), false, null, null, "Richmond Branch", "R" },
                    { 16L, "GEO", "System", new DateTimeOffset(new DateTime(2023, 2, 8, 18, 51, 40, 869, DateTimeKind.Unspecified).AddTicks(3736), new TimeSpan(0, 0, 0, 0, 0)), false, null, null, "George Branch", "E" }
                });
        }
    }
}
