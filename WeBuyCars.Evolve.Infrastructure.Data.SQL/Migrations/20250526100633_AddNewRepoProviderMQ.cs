using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WeBuyCars.Evolve.Infrastructure.Data.SQL.Migrations
{
    /// <inheritdoc />
    public partial class AddNewRepoProviderMQ : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 1L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 435, DateTimeKind.Unspecified).AddTicks(1540), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 2L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 435, DateTimeKind.Unspecified).AddTicks(3400), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 3L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 435, DateTimeKind.Unspecified).AddTicks(3400), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 4L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 435, DateTimeKind.Unspecified).AddTicks(3400), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 5L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 435, DateTimeKind.Unspecified).AddTicks(3400), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 6L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 435, DateTimeKind.Unspecified).AddTicks(3400), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 7L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 435, DateTimeKind.Unspecified).AddTicks(3410), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 8L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 435, DateTimeKind.Unspecified).AddTicks(3410), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 9L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 435, DateTimeKind.Unspecified).AddTicks(3410), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 10L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 435, DateTimeKind.Unspecified).AddTicks(3410), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 11L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 435, DateTimeKind.Unspecified).AddTicks(3410), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 12L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 435, DateTimeKind.Unspecified).AddTicks(3410), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 13L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 435, DateTimeKind.Unspecified).AddTicks(3410), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 14L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 435, DateTimeKind.Unspecified).AddTicks(3410), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 15L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 435, DateTimeKind.Unspecified).AddTicks(3420), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 16L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 435, DateTimeKind.Unspecified).AddTicks(3420), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 17L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 435, DateTimeKind.Unspecified).AddTicks(3420), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 18L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 435, DateTimeKind.Unspecified).AddTicks(3420), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepairType",
                keyColumn: "Id",
                keyValue: 10L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 448, DateTimeKind.Unspecified).AddTicks(5680), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepairType",
                keyColumn: "Id",
                keyValue: 20L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 448, DateTimeKind.Unspecified).AddTicks(6050), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepairType",
                keyColumn: "Id",
                keyValue: 30L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 448, DateTimeKind.Unspecified).AddTicks(6050), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepairType",
                keyColumn: "Id",
                keyValue: 40L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 448, DateTimeKind.Unspecified).AddTicks(6050), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepairType",
                keyColumn: "Id",
                keyValue: 50L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 448, DateTimeKind.Unspecified).AddTicks(6050), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepairType",
                keyColumn: "Id",
                keyValue: 60L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 448, DateTimeKind.Unspecified).AddTicks(6050), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepairType",
                keyColumn: "Id",
                keyValue: 90L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 448, DateTimeKind.Unspecified).AddTicks(6050), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepairType",
                keyColumn: "Id",
                keyValue: 100L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 448, DateTimeKind.Unspecified).AddTicks(6050), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepairType",
                keyColumn: "Id",
                keyValue: 110L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 448, DateTimeKind.Unspecified).AddTicks(6050), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepairType",
                keyColumn: "Id",
                keyValue: 120L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 448, DateTimeKind.Unspecified).AddTicks(6050), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepairType",
                keyColumn: "Id",
                keyValue: 130L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 448, DateTimeKind.Unspecified).AddTicks(6060), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepairType",
                keyColumn: "Id",
                keyValue: 140L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 448, DateTimeKind.Unspecified).AddTicks(6060), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepairType",
                keyColumn: "Id",
                keyValue: 150L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 448, DateTimeKind.Unspecified).AddTicks(6060), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepairType",
                keyColumn: "Id",
                keyValue: 160L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 448, DateTimeKind.Unspecified).AddTicks(6060), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 1L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 449, DateTimeKind.Unspecified).AddTicks(7820), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 2L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 449, DateTimeKind.Unspecified).AddTicks(8600), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 3L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 449, DateTimeKind.Unspecified).AddTicks(8600), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 4L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 449, DateTimeKind.Unspecified).AddTicks(8600), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 5L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 449, DateTimeKind.Unspecified).AddTicks(8600), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 6L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 449, DateTimeKind.Unspecified).AddTicks(8600), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 7L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 449, DateTimeKind.Unspecified).AddTicks(8600), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 8L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 449, DateTimeKind.Unspecified).AddTicks(8610), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.InsertData(
                schema: "Evolve",
                table: "RepoProvider",
                columns: new[] { "Id", "AccountNumber", "CommissionEnabled", "CommissionPercent", "CreatedBy", "CreatedOn", "ImsFlagCode", "MaxCommission", "MinCommission", "ModifiedBy", "ModifiedOn", "SalesCodePrefix" },
                values: new object[] { 9L, "1816935", true, 5.00m, "System", new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 449, DateTimeKind.Unspecified).AddTicks(8610), new TimeSpan(0, 0, 0, 0, 0)), "MQRepo", 0m, 0m, null, null, "XP" });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 1L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 456, DateTimeKind.Unspecified).AddTicks(7090), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 2L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 456, DateTimeKind.Unspecified).AddTicks(7310), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 3L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 456, DateTimeKind.Unspecified).AddTicks(7310), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 4L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 456, DateTimeKind.Unspecified).AddTicks(7310), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 5L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 456, DateTimeKind.Unspecified).AddTicks(7320), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 6L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 456, DateTimeKind.Unspecified).AddTicks(7320), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 7L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 456, DateTimeKind.Unspecified).AddTicks(7320), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 8L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 456, DateTimeKind.Unspecified).AddTicks(7320), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 9L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 456, DateTimeKind.Unspecified).AddTicks(7320), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 10L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 456, DateTimeKind.Unspecified).AddTicks(7320), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 11L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 456, DateTimeKind.Unspecified).AddTicks(7320), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 12L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 456, DateTimeKind.Unspecified).AddTicks(7320), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 13L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 456, DateTimeKind.Unspecified).AddTicks(7320), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 14L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 456, DateTimeKind.Unspecified).AddTicks(7320), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 15L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 456, DateTimeKind.Unspecified).AddTicks(7320), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 16L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 456, DateTimeKind.Unspecified).AddTicks(7320), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 17L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 456, DateTimeKind.Unspecified).AddTicks(7320), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 18L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 456, DateTimeKind.Unspecified).AddTicks(7320), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 19L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 456, DateTimeKind.Unspecified).AddTicks(7330), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 20L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 456, DateTimeKind.Unspecified).AddTicks(7330), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 21L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 456, DateTimeKind.Unspecified).AddTicks(7330), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 22L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 456, DateTimeKind.Unspecified).AddTicks(7330), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 23L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 456, DateTimeKind.Unspecified).AddTicks(7330), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 24L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 456, DateTimeKind.Unspecified).AddTicks(7330), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 25L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 456, DateTimeKind.Unspecified).AddTicks(7330), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 26L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 26, 10, 6, 33, 456, DateTimeKind.Unspecified).AddTicks(7330), new TimeSpan(0, 0, 0, 0, 0)));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 9L);

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 1L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 89, DateTimeKind.Unspecified).AddTicks(2580), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 2L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 89, DateTimeKind.Unspecified).AddTicks(4250), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 3L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 89, DateTimeKind.Unspecified).AddTicks(4250), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 4L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 89, DateTimeKind.Unspecified).AddTicks(4250), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 5L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 89, DateTimeKind.Unspecified).AddTicks(4250), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 6L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 89, DateTimeKind.Unspecified).AddTicks(4250), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 7L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 89, DateTimeKind.Unspecified).AddTicks(4250), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 8L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 89, DateTimeKind.Unspecified).AddTicks(4250), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 9L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 89, DateTimeKind.Unspecified).AddTicks(4260), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 10L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 89, DateTimeKind.Unspecified).AddTicks(4260), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 11L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 89, DateTimeKind.Unspecified).AddTicks(4300), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 12L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 89, DateTimeKind.Unspecified).AddTicks(4300), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 13L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 89, DateTimeKind.Unspecified).AddTicks(4300), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 14L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 89, DateTimeKind.Unspecified).AddTicks(4300), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 15L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 89, DateTimeKind.Unspecified).AddTicks(4300), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 16L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 89, DateTimeKind.Unspecified).AddTicks(4300), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 17L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 89, DateTimeKind.Unspecified).AddTicks(4300), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 18L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 89, DateTimeKind.Unspecified).AddTicks(4300), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepairType",
                keyColumn: "Id",
                keyValue: 10L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 101, DateTimeKind.Unspecified).AddTicks(6400), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepairType",
                keyColumn: "Id",
                keyValue: 20L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 101, DateTimeKind.Unspecified).AddTicks(6730), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepairType",
                keyColumn: "Id",
                keyValue: 30L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 101, DateTimeKind.Unspecified).AddTicks(6740), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepairType",
                keyColumn: "Id",
                keyValue: 40L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 101, DateTimeKind.Unspecified).AddTicks(6740), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepairType",
                keyColumn: "Id",
                keyValue: 50L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 101, DateTimeKind.Unspecified).AddTicks(6740), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepairType",
                keyColumn: "Id",
                keyValue: 60L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 101, DateTimeKind.Unspecified).AddTicks(6740), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepairType",
                keyColumn: "Id",
                keyValue: 90L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 101, DateTimeKind.Unspecified).AddTicks(6740), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepairType",
                keyColumn: "Id",
                keyValue: 100L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 101, DateTimeKind.Unspecified).AddTicks(6740), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepairType",
                keyColumn: "Id",
                keyValue: 110L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 101, DateTimeKind.Unspecified).AddTicks(6740), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepairType",
                keyColumn: "Id",
                keyValue: 120L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 101, DateTimeKind.Unspecified).AddTicks(6740), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepairType",
                keyColumn: "Id",
                keyValue: 130L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 101, DateTimeKind.Unspecified).AddTicks(6740), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepairType",
                keyColumn: "Id",
                keyValue: 140L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 101, DateTimeKind.Unspecified).AddTicks(6740), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepairType",
                keyColumn: "Id",
                keyValue: 150L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 101, DateTimeKind.Unspecified).AddTicks(6740), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepairType",
                keyColumn: "Id",
                keyValue: 160L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 101, DateTimeKind.Unspecified).AddTicks(6740), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 1L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 102, DateTimeKind.Unspecified).AddTicks(7340), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 2L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 102, DateTimeKind.Unspecified).AddTicks(8070), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 3L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 102, DateTimeKind.Unspecified).AddTicks(8080), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 4L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 102, DateTimeKind.Unspecified).AddTicks(8080), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 5L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 102, DateTimeKind.Unspecified).AddTicks(8080), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 6L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 102, DateTimeKind.Unspecified).AddTicks(8080), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 7L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 102, DateTimeKind.Unspecified).AddTicks(8080), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 8L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 102, DateTimeKind.Unspecified).AddTicks(8080), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 1L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 109, DateTimeKind.Unspecified).AddTicks(4130), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 2L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 109, DateTimeKind.Unspecified).AddTicks(4360), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 3L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 109, DateTimeKind.Unspecified).AddTicks(4360), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 4L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 109, DateTimeKind.Unspecified).AddTicks(4360), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 5L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 109, DateTimeKind.Unspecified).AddTicks(4360), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 6L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 109, DateTimeKind.Unspecified).AddTicks(4360), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 7L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 109, DateTimeKind.Unspecified).AddTicks(4360), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 8L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 109, DateTimeKind.Unspecified).AddTicks(4360), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 9L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 109, DateTimeKind.Unspecified).AddTicks(4360), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 10L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 109, DateTimeKind.Unspecified).AddTicks(4370), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 11L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 109, DateTimeKind.Unspecified).AddTicks(4370), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 12L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 109, DateTimeKind.Unspecified).AddTicks(4370), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 13L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 109, DateTimeKind.Unspecified).AddTicks(4370), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 14L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 109, DateTimeKind.Unspecified).AddTicks(4370), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 15L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 109, DateTimeKind.Unspecified).AddTicks(4370), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 16L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 109, DateTimeKind.Unspecified).AddTicks(4370), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 17L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 109, DateTimeKind.Unspecified).AddTicks(4370), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 18L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 109, DateTimeKind.Unspecified).AddTicks(4370), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 19L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 109, DateTimeKind.Unspecified).AddTicks(4390), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 20L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 109, DateTimeKind.Unspecified).AddTicks(4390), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 21L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 109, DateTimeKind.Unspecified).AddTicks(4390), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 22L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 109, DateTimeKind.Unspecified).AddTicks(4390), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 23L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 109, DateTimeKind.Unspecified).AddTicks(4390), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 24L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 109, DateTimeKind.Unspecified).AddTicks(4390), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 25L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 109, DateTimeKind.Unspecified).AddTicks(4390), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "VAPSAccessoryCodes",
                keyColumn: "Id",
                keyValue: 26L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 5, 9, 11, 53, 6, 109, DateTimeKind.Unspecified).AddTicks(4390), new TimeSpan(0, 0, 0, 0, 0)));
        }
    }
}
