using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WeBuyCars.Evolve.Infrastructure.Data.SQL.Migrations
{
    public partial class AddNewBranchesELS_RUS : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                schema: "Evolve",
                table: "AccessoryCode",
                columns: new[] { "Id", "CreatedBy", "CreatedOn", "DealerTermsCode", "DeliveryCode", "DocumentationFeeLimitValue", "DocumentationFeeMaxCode", "DocumentationFeeMaxMinCode", "LicenseAndRegistrationCode", "Location", "ModifiedBy", "ModifiedOn", "WarrantyCode" },
                values: new object[,]
                {
                    { 17L, "System", new DateTimeOffset(new DateTime(2024, 5, 13, 13, 19, 14, 213, DateTimeKind.Unspecified).AddTicks(3480), new TimeSpan(0, 0, 0, 0, 0)), "16401", "16002", 17391.3043478261m, "16501", "16502", "16001", "ELS", null, null, "16301" },
                    { 18L, "System", new DateTimeOffset(new DateTime(2024, 5, 13, 13, 19, 14, 213, DateTimeKind.Unspecified).AddTicks(3480), new TimeSpan(0, 0, 0, 0, 0)), "17401", "17002", 17391.3043478261m, "17501", "17502", "17001", "RUS", null, null, "17301" }
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 17L);

            migrationBuilder.DeleteData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 18L);
        }
    }
}
