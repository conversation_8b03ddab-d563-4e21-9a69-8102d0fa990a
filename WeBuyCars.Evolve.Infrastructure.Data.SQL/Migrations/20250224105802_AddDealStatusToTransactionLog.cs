using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WeBuyCars.Evolve.Infrastructure.Data.SQL.Migrations
{
    /// <inheritdoc />
    public partial class AddDealStatusToTransactionLog : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "Description",
                schema: "Evolve",
                table: "TransactionStatus",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Message",
                schema: "Evolve",
                table: "TransactionLog",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DealStatus",
                schema: "Evolve",
                table: "TransactionLog",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "LeadNumber",
                schema: "Evolve",
                table: "TransactionLog",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                defaultValue: "");

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 1L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 24, 10, 58, 2, 92, DateTimeKind.Unspecified).AddTicks(5500), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 2L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 24, 10, 58, 2, 92, DateTimeKind.Unspecified).AddTicks(7010), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 3L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 24, 10, 58, 2, 92, DateTimeKind.Unspecified).AddTicks(7020), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 4L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 24, 10, 58, 2, 92, DateTimeKind.Unspecified).AddTicks(7020), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 5L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 24, 10, 58, 2, 92, DateTimeKind.Unspecified).AddTicks(7020), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 6L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 24, 10, 58, 2, 92, DateTimeKind.Unspecified).AddTicks(7020), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 7L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 24, 10, 58, 2, 92, DateTimeKind.Unspecified).AddTicks(7020), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 8L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 24, 10, 58, 2, 92, DateTimeKind.Unspecified).AddTicks(7020), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 9L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 24, 10, 58, 2, 92, DateTimeKind.Unspecified).AddTicks(7040), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 10L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 24, 10, 58, 2, 92, DateTimeKind.Unspecified).AddTicks(7040), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 11L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 24, 10, 58, 2, 92, DateTimeKind.Unspecified).AddTicks(7040), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 12L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 24, 10, 58, 2, 92, DateTimeKind.Unspecified).AddTicks(7040), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 13L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 24, 10, 58, 2, 92, DateTimeKind.Unspecified).AddTicks(7040), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 14L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 24, 10, 58, 2, 92, DateTimeKind.Unspecified).AddTicks(7040), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 15L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 24, 10, 58, 2, 92, DateTimeKind.Unspecified).AddTicks(7050), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 16L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 24, 10, 58, 2, 92, DateTimeKind.Unspecified).AddTicks(7050), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 17L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 24, 10, 58, 2, 92, DateTimeKind.Unspecified).AddTicks(7050), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 18L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 24, 10, 58, 2, 92, DateTimeKind.Unspecified).AddTicks(7050), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 1L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 24, 10, 58, 2, 104, DateTimeKind.Unspecified).AddTicks(7170), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 2L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 24, 10, 58, 2, 104, DateTimeKind.Unspecified).AddTicks(7910), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 3L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 24, 10, 58, 2, 104, DateTimeKind.Unspecified).AddTicks(7920), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 4L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 24, 10, 58, 2, 104, DateTimeKind.Unspecified).AddTicks(7920), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.CreateIndex(
                name: "IX_TransactionLog_LeadNumber",
                schema: "Evolve",
                table: "TransactionLog",
                column: "LeadNumber");

            migrationBuilder.CreateIndex(
                name: "IX_TransactionLog_TransactionType",
                schema: "Evolve",
                table: "TransactionLog",
                column: "TransactionType");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_TransactionLog_LeadNumber",
                schema: "Evolve",
                table: "TransactionLog");

            migrationBuilder.DropIndex(
                name: "IX_TransactionLog_TransactionType",
                schema: "Evolve",
                table: "TransactionLog");

            migrationBuilder.DropColumn(
                name: "DealStatus",
                schema: "Evolve",
                table: "TransactionLog");

            migrationBuilder.DropColumn(
                name: "LeadNumber",
                schema: "Evolve",
                table: "TransactionLog");

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                schema: "Evolve",
                table: "TransactionStatus",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(50)",
                oldMaxLength: 50);

            migrationBuilder.AlterColumn<string>(
                name: "Message",
                schema: "Evolve",
                table: "TransactionLog",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(500)",
                oldMaxLength: 500,
                oldNullable: true);

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 1L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2024, 11, 26, 7, 55, 25, 149, DateTimeKind.Unspecified).AddTicks(6820), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 2L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2024, 11, 26, 7, 55, 25, 149, DateTimeKind.Unspecified).AddTicks(6830), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 3L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2024, 11, 26, 7, 55, 25, 149, DateTimeKind.Unspecified).AddTicks(6830), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 4L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2024, 11, 26, 7, 55, 25, 149, DateTimeKind.Unspecified).AddTicks(6830), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 5L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2024, 11, 26, 7, 55, 25, 149, DateTimeKind.Unspecified).AddTicks(6830), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 6L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2024, 11, 26, 7, 55, 25, 149, DateTimeKind.Unspecified).AddTicks(6830), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 7L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2024, 11, 26, 7, 55, 25, 149, DateTimeKind.Unspecified).AddTicks(6830), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 8L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2024, 11, 26, 7, 55, 25, 149, DateTimeKind.Unspecified).AddTicks(6840), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 9L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2024, 11, 26, 7, 55, 25, 149, DateTimeKind.Unspecified).AddTicks(6840), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 10L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2024, 11, 26, 7, 55, 25, 149, DateTimeKind.Unspecified).AddTicks(6840), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 11L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2024, 11, 26, 7, 55, 25, 149, DateTimeKind.Unspecified).AddTicks(6840), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 12L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2024, 11, 26, 7, 55, 25, 149, DateTimeKind.Unspecified).AddTicks(6840), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 13L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2024, 11, 26, 7, 55, 25, 149, DateTimeKind.Unspecified).AddTicks(6840), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 14L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2024, 11, 26, 7, 55, 25, 149, DateTimeKind.Unspecified).AddTicks(6840), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 15L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2024, 11, 26, 7, 55, 25, 149, DateTimeKind.Unspecified).AddTicks(6840), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 16L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2024, 11, 26, 7, 55, 25, 149, DateTimeKind.Unspecified).AddTicks(6840), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 17L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2024, 11, 26, 7, 55, 25, 149, DateTimeKind.Unspecified).AddTicks(6840), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 18L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2024, 11, 26, 7, 55, 25, 149, DateTimeKind.Unspecified).AddTicks(6850), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 1L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2024, 11, 26, 7, 55, 25, 150, DateTimeKind.Unspecified).AddTicks(3380), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 2L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2024, 11, 26, 7, 55, 25, 150, DateTimeKind.Unspecified).AddTicks(3390), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 3L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2024, 11, 26, 7, 55, 25, 150, DateTimeKind.Unspecified).AddTicks(3390), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 4L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2024, 11, 26, 7, 55, 25, 150, DateTimeKind.Unspecified).AddTicks(3390), new TimeSpan(0, 0, 0, 0, 0)));
        }
    }
}
