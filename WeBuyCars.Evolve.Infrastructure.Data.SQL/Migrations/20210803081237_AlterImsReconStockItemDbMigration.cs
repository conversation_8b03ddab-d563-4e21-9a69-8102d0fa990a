using System;
using Microsoft.EntityFrameworkCore.Migrations;
using WeBuyCars.Evolve.Infrastructure.Data.SQL.Extensions;

namespace WeBuyCars.Evolve.Infrastructure.Data.SQL.Migrations
{
    public partial class AlterImsReconStockItemDbMigration : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 1L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 8, 3, 8, 12, 37, 401, DateTimeKind.Unspecified).AddTicks(217), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 2L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 8, 3, 8, 12, 37, 401, DateTimeKind.Unspecified).AddTicks(1431), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 3L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 8, 3, 8, 12, 37, 401, DateTimeKind.Unspecified).AddTicks(1459), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 4L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 8, 3, 8, 12, 37, 401, DateTimeKind.Unspecified).AddTicks(1463), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 5L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 8, 3, 8, 12, 37, 401, DateTimeKind.Unspecified).AddTicks(1464), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 6L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 8, 3, 8, 12, 37, 401, DateTimeKind.Unspecified).AddTicks(1469), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 7L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 8, 3, 8, 12, 37, 401, DateTimeKind.Unspecified).AddTicks(1470), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 8L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 8, 3, 8, 12, 37, 401, DateTimeKind.Unspecified).AddTicks(1472), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 9L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 8, 3, 8, 12, 37, 401, DateTimeKind.Unspecified).AddTicks(1473), new TimeSpan(0, 0, 0, 0, 0)) });
                
            migrationBuilder.CreateAlterImsReconStockItem();
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 1L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 6, 24, 10, 25, 10, 180, DateTimeKind.Unspecified).AddTicks(5098), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 2L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 6, 24, 10, 25, 10, 180, DateTimeKind.Unspecified).AddTicks(6500), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 3L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 6, 24, 10, 25, 10, 180, DateTimeKind.Unspecified).AddTicks(6536), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 4L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 6, 24, 10, 25, 10, 180, DateTimeKind.Unspecified).AddTicks(6541), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 5L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 6, 24, 10, 25, 10, 180, DateTimeKind.Unspecified).AddTicks(6543), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 6L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 6, 24, 10, 25, 10, 180, DateTimeKind.Unspecified).AddTicks(6550), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 7L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 6, 24, 10, 25, 10, 180, DateTimeKind.Unspecified).AddTicks(6551), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 8L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 6, 24, 10, 25, 10, 180, DateTimeKind.Unspecified).AddTicks(6552), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 9L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 6, 24, 10, 25, 10, 180, DateTimeKind.Unspecified).AddTicks(6554), new TimeSpan(0, 0, 0, 0, 0)) });
                
            migrationBuilder.DropAlterImsReconStockItem();
        }
    }
}
