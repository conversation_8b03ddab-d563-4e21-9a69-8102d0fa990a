using System;
using Microsoft.EntityFrameworkCore.Migrations;
using WeBuyCars.Evolve.Infrastructure.Data.SQL.Extensions;

namespace WeBuyCars.Evolve.Infrastructure.Data.SQL.Migrations
{
    public partial class SaleLeadViewDbMigration : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 1L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 5, 20, 7, 10, 8, 422, DateTimeKind.Unspecified).AddTicks(9469), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 2L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 5, 20, 7, 10, 8, 423, DateTimeKind.Unspecified).AddTicks(843), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 3L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 5, 20, 7, 10, 8, 423, DateTimeKind.Unspecified).AddTicks(972), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 4L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 5, 20, 7, 10, 8, 423, DateTimeKind.Unspecified).AddTicks(977), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 5L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 5, 20, 7, 10, 8, 423, DateTimeKind.Unspecified).AddTicks(979), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 6L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 5, 20, 7, 10, 8, 423, DateTimeKind.Unspecified).AddTicks(985), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 7L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 5, 20, 7, 10, 8, 423, DateTimeKind.Unspecified).AddTicks(987), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 8L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 5, 20, 7, 10, 8, 423, DateTimeKind.Unspecified).AddTicks(989), new TimeSpan(0, 0, 0, 0, 0)) });
  
            migrationBuilder.CreateSaleLeadsView();
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 1L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 5, 19, 6, 8, 27, 250, DateTimeKind.Unspecified).AddTicks(7121), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 2L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 5, 19, 6, 8, 27, 250, DateTimeKind.Unspecified).AddTicks(8327), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 3L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 5, 19, 6, 8, 27, 250, DateTimeKind.Unspecified).AddTicks(8355), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 4L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 5, 19, 6, 8, 27, 250, DateTimeKind.Unspecified).AddTicks(8358), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 5L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 5, 19, 6, 8, 27, 250, DateTimeKind.Unspecified).AddTicks(8360), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 6L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 5, 19, 6, 8, 27, 250, DateTimeKind.Unspecified).AddTicks(8364), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 7L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 5, 19, 6, 8, 27, 250, DateTimeKind.Unspecified).AddTicks(8366), new TimeSpan(0, 0, 0, 0, 0)) });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 8L,
                columns: new[] { "CreatedBy", "CreatedOn" },
                values: new object[] { "System", new DateTimeOffset(new DateTime(2021, 5, 19, 6, 8, 27, 250, DateTimeKind.Unspecified).AddTicks(8368), new TimeSpan(0, 0, 0, 0, 0)) });
                
            migrationBuilder.DropSaleLeadsView();
        }
    }
}
