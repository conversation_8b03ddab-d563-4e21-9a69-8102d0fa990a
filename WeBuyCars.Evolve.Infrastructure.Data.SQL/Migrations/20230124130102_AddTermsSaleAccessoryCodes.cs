using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WeBuyCars.Evolve.Infrastructure.Data.SQL.Migrations
{
    public partial class AddTermsSaleAccessoryCodes : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "WarrantyCode",
                schema: "Evolve",
                table: "AccessoryCode",
                type: "nvarchar(10)",
                maxLength: 10,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "LicenseAndRegistrationCode",
                schema: "Evolve",
                table: "AccessoryCode",
                type: "nvarchar(10)",
                maxLength: 10,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "DocumentationFeeMaxMinCode",
                schema: "Evolve",
                table: "AccessoryCode",
                type: "nvarchar(10)",
                maxLength: 10,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AlterColumn<string>(
                name: "DocumentationFeeMaxCode",
                schema: "Evolve",
                table: "AccessoryCode",
                type: "nvarchar(10)",
                maxLength: 10,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AlterColumn<string>(
                name: "DeliveryCode",
                schema: "Evolve",
                table: "AccessoryCode",
                type: "nvarchar(10)",
                maxLength: 10,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DealerTermsCode",
                schema: "Evolve",
                table: "AccessoryCode",
                type: "nvarchar(10)",
                maxLength: 10,
                nullable: true);

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 1L,
                columns: new[] { "CreatedOn", "DealerTermsCode" },
                values: new object[] { new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(1980), new TimeSpan(0, 0, 0, 0, 0)), "1401" });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 2L,
                columns: new[] { "CreatedOn", "DealerTermsCode" },
                values: new object[] { new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(1990), new TimeSpan(0, 0, 0, 0, 0)), "2401" });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 3L,
                columns: new[] { "CreatedOn", "DealerTermsCode", "DeliveryCode", "DocumentationFeeMaxCode", "DocumentationFeeMaxMinCode", "LicenseAndRegistrationCode", "Location", "WarrantyCode" },
                values: new object[] { new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(1990), new TimeSpan(0, 0, 0, 0, 0)), "3401", "3002", "3501", "3502", "3001", "DBN", "3301" });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 4L,
                columns: new[] { "CreatedOn", "DealerTermsCode", "DeliveryCode", "DocumentationFeeMaxCode", "DocumentationFeeMaxMinCode", "LicenseAndRegistrationCode", "Location", "WarrantyCode" },
                values: new object[] { new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(1990), new TimeSpan(0, 0, 0, 0, 0)), "4401", "4002", "4501", "4502", "4001", "CPT", "4301" });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 5L,
                columns: new[] { "CreatedOn", "DealerTermsCode", "DeliveryCode", "DocumentationFeeMaxCode", "DocumentationFeeMaxMinCode", "LicenseAndRegistrationCode", "Location", "WarrantyCode" },
                values: new object[] { new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(1990), new TimeSpan(0, 0, 0, 0, 0)), "5401", "5002", "5501", "5502", "5001", "JBS", "5301" });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 6L,
                columns: new[] { "CreatedOn", "DealerTermsCode", "DeliveryCode", "DocumentationFeeMaxCode", "DocumentationFeeMaxMinCode", "LicenseAndRegistrationCode", "Location", "WarrantyCode" },
                values: new object[] { new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(2000), new TimeSpan(0, 0, 0, 0, 0)), "6401", "6002", "6501", "6502", "6001", "PEL", "6301" });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 7L,
                columns: new[] { "CreatedOn", "DealerTermsCode", "DeliveryCode", "DocumentationFeeMaxCode", "DocumentationFeeMaxMinCode", "LicenseAndRegistrationCode", "Location", "WarrantyCode" },
                values: new object[] { new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(2000), new TimeSpan(0, 0, 0, 0, 0)), "7401", "7002", "7501", "7502", "7001", "PHU", "7301" });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 8L,
                columns: new[] { "CreatedOn", "DealerTermsCode" },
                values: new object[] { new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(2000), new TimeSpan(0, 0, 0, 0, 0)), "8401" });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 9L,
                columns: new[] { "CreatedOn", "DealerTermsCode" },
                values: new object[] { new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(2000), new TimeSpan(0, 0, 0, 0, 0)), "9401" });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 10L,
                columns: new[] { "CreatedOn", "DealerTermsCode" },
                values: new object[] { new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(2000), new TimeSpan(0, 0, 0, 0, 0)), "10401" });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 11L,
                columns: new[] { "CreatedOn", "DealerTermsCode" },
                values: new object[] { new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(2000), new TimeSpan(0, 0, 0, 0, 0)), "11401" });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 12L,
                columns: new[] { "CreatedOn", "DealerTermsCode", "DeliveryCode", "DocumentationFeeMaxCode", "DocumentationFeeMaxMinCode", "LicenseAndRegistrationCode", "Location", "WarrantyCode" },
                values: new object[] { new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(2000), new TimeSpan(0, 0, 0, 0, 0)), "12401", "12002", "12501", "12502", "12001", "RIC", "12301" });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 13L,
                columns: new[] { "CreatedOn", "DealerTermsCode", "DeliveryCode", "DocumentationFeeMaxCode", "DocumentationFeeMaxMinCode", "LicenseAndRegistrationCode", "Location", "WarrantyCode" },
                values: new object[] { new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(2010), new TimeSpan(0, 0, 0, 0, 0)), "13401", "13002", "13501", "13502", "13001", "GEO", "13301" });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 14L,
                columns: new[] { "CreatedOn", "DealerTermsCode", "DeliveryCode", "DocumentationFeeMaxCode", "DocumentationFeeMaxMinCode", "LicenseAndRegistrationCode", "Location", "WarrantyCode" },
                values: new object[] { new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(2010), new TimeSpan(0, 0, 0, 0, 0)), "14401", "14002", "14501", "14502", "14001", "RIV", "14301" });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 15L,
                columns: new[] { "CreatedOn", "DealerTermsCode", "DeliveryCode", "DocumentationFeeMaxCode", "DocumentationFeeMaxMinCode", "LicenseAndRegistrationCode", "Location", "WarrantyCode" },
                values: new object[] { new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(2010), new TimeSpan(0, 0, 0, 0, 0)), "15401", "15002", "15501", "15502", "15001", "EPP", "15301" });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 1L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(2890), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 2L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(2890), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 3L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(2890), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 4L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(2890), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 5L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(2890), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 6L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(2900), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 7L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(2900), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 8L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(2900), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 9L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(2900), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 10L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(2900), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 11L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(2900), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 12L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(2900), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 13L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(2900), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 14L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(2900), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 15L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(2900), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 16L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2023, 1, 24, 13, 1, 2, 729, DateTimeKind.Unspecified).AddTicks(2910), new TimeSpan(0, 0, 0, 0, 0)));
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DealerTermsCode",
                schema: "Evolve",
                table: "AccessoryCode");

            migrationBuilder.AlterColumn<string>(
                name: "WarrantyCode",
                schema: "Evolve",
                table: "AccessoryCode",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(10)",
                oldMaxLength: 10,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "LicenseAndRegistrationCode",
                schema: "Evolve",
                table: "AccessoryCode",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(10)",
                oldMaxLength: 10,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "DocumentationFeeMaxMinCode",
                schema: "Evolve",
                table: "AccessoryCode",
                type: "nvarchar(max)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(10)",
                oldMaxLength: 10);

            migrationBuilder.AlterColumn<string>(
                name: "DocumentationFeeMaxCode",
                schema: "Evolve",
                table: "AccessoryCode",
                type: "nvarchar(max)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(10)",
                oldMaxLength: 10);

            migrationBuilder.AlterColumn<string>(
                name: "DeliveryCode",
                schema: "Evolve",
                table: "AccessoryCode",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(10)",
                oldMaxLength: 10,
                oldNullable: true);

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 1L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(5720), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 2L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(5730), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 3L,
                columns: new[] { "CreatedOn", "DeliveryCode", "DocumentationFeeMaxCode", "DocumentationFeeMaxMinCode", "LicenseAndRegistrationCode", "Location", "WarrantyCode" },
                values: new object[] { new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(5730), new TimeSpan(0, 0, 0, 0, 0)), "6002", "6501", "6502", "6001", "PEL", "6301" });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 4L,
                columns: new[] { "CreatedOn", "DeliveryCode", "DocumentationFeeMaxCode", "DocumentationFeeMaxMinCode", "LicenseAndRegistrationCode", "Location", "WarrantyCode" },
                values: new object[] { new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(5730), new TimeSpan(0, 0, 0, 0, 0)), "7002", "7501", "7502", "7001", "PHU", "7301" });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 5L,
                columns: new[] { "CreatedOn", "DeliveryCode", "DocumentationFeeMaxCode", "DocumentationFeeMaxMinCode", "LicenseAndRegistrationCode", "Location", "WarrantyCode" },
                values: new object[] { new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(5730), new TimeSpan(0, 0, 0, 0, 0)), "4002", "4501", "4502", "4001", "CPT", "4301" });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 6L,
                columns: new[] { "CreatedOn", "DeliveryCode", "DocumentationFeeMaxCode", "DocumentationFeeMaxMinCode", "LicenseAndRegistrationCode", "Location", "WarrantyCode" },
                values: new object[] { new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(5750), new TimeSpan(0, 0, 0, 0, 0)), "3002", "3501", "3502", "3001", "DBN", "3301" });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 7L,
                columns: new[] { "CreatedOn", "DeliveryCode", "DocumentationFeeMaxCode", "DocumentationFeeMaxMinCode", "LicenseAndRegistrationCode", "Location", "WarrantyCode" },
                values: new object[] { new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(5760), new TimeSpan(0, 0, 0, 0, 0)), "5002", "5501", "5502", "5001", "JBS", "5301" });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 8L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(5760), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 9L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(5760), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 10L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(5760), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 11L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(5760), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 12L,
                columns: new[] { "CreatedOn", "DeliveryCode", "DocumentationFeeMaxCode", "DocumentationFeeMaxMinCode", "LicenseAndRegistrationCode", "Location", "WarrantyCode" },
                values: new object[] { new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(5760), new TimeSpan(0, 0, 0, 0, 0)), "14002", "14501", "14502", "14001", "RIV", "14301" });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 13L,
                columns: new[] { "CreatedOn", "DeliveryCode", "DocumentationFeeMaxCode", "DocumentationFeeMaxMinCode", "LicenseAndRegistrationCode", "Location", "WarrantyCode" },
                values: new object[] { new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(5760), new TimeSpan(0, 0, 0, 0, 0)), "15002", "15501", "15502", "15001", "EPP", "15301" });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 14L,
                columns: new[] { "CreatedOn", "DeliveryCode", "DocumentationFeeMaxCode", "DocumentationFeeMaxMinCode", "LicenseAndRegistrationCode", "Location", "WarrantyCode" },
                values: new object[] { new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(5760), new TimeSpan(0, 0, 0, 0, 0)), "12002", "12501", "12502", "12001", "RIC", "12301" });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 15L,
                columns: new[] { "CreatedOn", "DeliveryCode", "DocumentationFeeMaxCode", "DocumentationFeeMaxMinCode", "LicenseAndRegistrationCode", "Location", "WarrantyCode" },
                values: new object[] { new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(5770), new TimeSpan(0, 0, 0, 0, 0)), "13002", "13501", "13502", "13001", "GEO", "13301" });

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 1L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(6690), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 2L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(6690), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 3L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(6690), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 4L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(6690), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 5L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(6690), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 6L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(6700), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 7L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(6700), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 8L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(6700), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 9L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(6700), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 10L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(6700), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 11L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(6710), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 12L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(6710), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 13L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(6710), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 14L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(6710), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 15L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(6710), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "Branch",
                keyColumn: "Id",
                keyValue: 16L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2022, 11, 15, 11, 36, 13, 117, DateTimeKind.Unspecified).AddTicks(6710), new TimeSpan(0, 0, 0, 0, 0)));
        }
    }
}
