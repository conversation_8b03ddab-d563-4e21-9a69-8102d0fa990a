using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace WeBuyCars.Evolve.Infrastructure.Data.SQL.Migrations
{
    /// <inheritdoc />
    public partial class AddNewRepoProviders : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_SalesCategoryCode_WarehouseCode_SaleCategoryCode",
                schema: "Evolve",
                table: "SalesCategoryCode");

            migrationBuilder.DropIndex(
                name: "IX_BoughtCategoryCode_WarehouseCode_BuyCategoryCode",
                schema: "Evolve",
                table: "BoughtCategoryCode");

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 1L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 3, 4, 9, 32, 16, 479, DateTimeKind.Unspecified).AddTicks(7810), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 2L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 3, 4, 9, 32, 16, 479, DateTimeKind.Unspecified).AddTicks(9420), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 3L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 3, 4, 9, 32, 16, 479, DateTimeKind.Unspecified).AddTicks(9420), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 4L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 3, 4, 9, 32, 16, 479, DateTimeKind.Unspecified).AddTicks(9420), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 5L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 3, 4, 9, 32, 16, 479, DateTimeKind.Unspecified).AddTicks(9420), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 6L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 3, 4, 9, 32, 16, 479, DateTimeKind.Unspecified).AddTicks(9420), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 7L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 3, 4, 9, 32, 16, 479, DateTimeKind.Unspecified).AddTicks(9420), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 8L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 3, 4, 9, 32, 16, 479, DateTimeKind.Unspecified).AddTicks(9430), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 9L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 3, 4, 9, 32, 16, 479, DateTimeKind.Unspecified).AddTicks(9430), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 10L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 3, 4, 9, 32, 16, 479, DateTimeKind.Unspecified).AddTicks(9430), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 11L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 3, 4, 9, 32, 16, 479, DateTimeKind.Unspecified).AddTicks(9430), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 12L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 3, 4, 9, 32, 16, 479, DateTimeKind.Unspecified).AddTicks(9430), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 13L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 3, 4, 9, 32, 16, 479, DateTimeKind.Unspecified).AddTicks(9430), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 14L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 3, 4, 9, 32, 16, 479, DateTimeKind.Unspecified).AddTicks(9430), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 15L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 3, 4, 9, 32, 16, 479, DateTimeKind.Unspecified).AddTicks(9430), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 16L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 3, 4, 9, 32, 16, 479, DateTimeKind.Unspecified).AddTicks(9430), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 17L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 3, 4, 9, 32, 16, 479, DateTimeKind.Unspecified).AddTicks(9430), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 18L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 3, 4, 9, 32, 16, 479, DateTimeKind.Unspecified).AddTicks(9430), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 1L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 3, 4, 9, 32, 16, 493, DateTimeKind.Unspecified).AddTicks(920), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 2L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 3, 4, 9, 32, 16, 493, DateTimeKind.Unspecified).AddTicks(1710), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 3L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 3, 4, 9, 32, 16, 493, DateTimeKind.Unspecified).AddTicks(1710), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 4L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 3, 4, 9, 32, 16, 493, DateTimeKind.Unspecified).AddTicks(1710), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.InsertData(
                schema: "Evolve",
                table: "RepoProvider",
                columns: new[] { "Id", "AccountNumber", "CommissionEnabled", "CommissionPercent", "CreatedBy", "CreatedOn", "ImsFlagCode", "MaxCommission", "MinCommission", "ModifiedBy", "ModifiedOn", "SalesCodePrefix" },
                values: new object[,]
                {
                    { 5L, "1721243", true, 5.00m, "System", new DateTimeOffset(new DateTime(2025, 3, 4, 9, 32, 16, 493, DateTimeKind.Unspecified).AddTicks(1710), new TimeSpan(0, 0, 0, 0, 0)), "MFCRepo", 17391.30434783m, 6956.52173913m, null, null, "XP" },
                    { 6L, "1795909", true, 5.00m, "System", new DateTimeOffset(new DateTime(2025, 3, 4, 9, 32, 16, 493, DateTimeKind.Unspecified).AddTicks(1720), new TimeSpan(0, 0, 0, 0, 0)), "ABSARepo", 17391.30434783m, 6956.52173913m, null, null, "XP" },
                    { 7L, "1801582", true, 5.00m, "System", new DateTimeOffset(new DateTime(2025, 3, 4, 9, 32, 16, 493, DateTimeKind.Unspecified).AddTicks(1720), new TimeSpan(0, 0, 0, 0, 0)), "WesbankRepo", 17391.30434783m, 6956.52173913m, null, null, "XP" }
                });

            migrationBuilder.CreateIndex(
                name: "IX_SalesCategoryCode_WarehouseCode_SaleCategoryCode",
                schema: "Evolve",
                table: "SalesCategoryCode",
                columns: new[] { "WarehouseCode", "SaleCategoryCode" },
                unique: true,
                filter: "[WarehouseCode] IS NOT NULL AND [SaleCategoryCode] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_BoughtCategoryCode_WarehouseCode_BuyCategoryCode",
                schema: "Evolve",
                table: "BoughtCategoryCode",
                columns: new[] { "WarehouseCode", "BuyCategoryCode" },
                unique: true,
                filter: "[WarehouseCode] IS NOT NULL AND [BuyCategoryCode] IS NOT NULL");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_SalesCategoryCode_WarehouseCode_SaleCategoryCode",
                schema: "Evolve",
                table: "SalesCategoryCode");

            migrationBuilder.DropIndex(
                name: "IX_BoughtCategoryCode_WarehouseCode_BuyCategoryCode",
                schema: "Evolve",
                table: "BoughtCategoryCode");

            migrationBuilder.DeleteData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 5L);

            migrationBuilder.DeleteData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 6L);

            migrationBuilder.DeleteData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 7L);

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 1L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 20, 12, 47, 47, 154, DateTimeKind.Unspecified).AddTicks(1370), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 2L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 20, 12, 47, 47, 154, DateTimeKind.Unspecified).AddTicks(3060), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 3L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 20, 12, 47, 47, 154, DateTimeKind.Unspecified).AddTicks(3070), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 4L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 20, 12, 47, 47, 154, DateTimeKind.Unspecified).AddTicks(3070), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 5L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 20, 12, 47, 47, 154, DateTimeKind.Unspecified).AddTicks(3070), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 6L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 20, 12, 47, 47, 154, DateTimeKind.Unspecified).AddTicks(3070), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 7L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 20, 12, 47, 47, 154, DateTimeKind.Unspecified).AddTicks(3070), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 8L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 20, 12, 47, 47, 154, DateTimeKind.Unspecified).AddTicks(3090), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 9L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 20, 12, 47, 47, 154, DateTimeKind.Unspecified).AddTicks(3090), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 10L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 20, 12, 47, 47, 154, DateTimeKind.Unspecified).AddTicks(3090), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 11L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 20, 12, 47, 47, 154, DateTimeKind.Unspecified).AddTicks(3090), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 12L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 20, 12, 47, 47, 154, DateTimeKind.Unspecified).AddTicks(3090), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 13L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 20, 12, 47, 47, 154, DateTimeKind.Unspecified).AddTicks(3090), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 14L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 20, 12, 47, 47, 154, DateTimeKind.Unspecified).AddTicks(3090), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 15L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 20, 12, 47, 47, 154, DateTimeKind.Unspecified).AddTicks(3090), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 16L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 20, 12, 47, 47, 154, DateTimeKind.Unspecified).AddTicks(3090), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 17L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 20, 12, 47, 47, 154, DateTimeKind.Unspecified).AddTicks(3090), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "AccessoryCode",
                keyColumn: "Id",
                keyValue: 18L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 20, 12, 47, 47, 154, DateTimeKind.Unspecified).AddTicks(3090), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 1L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 20, 12, 47, 47, 168, DateTimeKind.Unspecified).AddTicks(3310), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 2L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 20, 12, 47, 47, 168, DateTimeKind.Unspecified).AddTicks(4120), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 3L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 20, 12, 47, 47, 168, DateTimeKind.Unspecified).AddTicks(4120), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.UpdateData(
                schema: "Evolve",
                table: "RepoProvider",
                keyColumn: "Id",
                keyValue: 4L,
                column: "CreatedOn",
                value: new DateTimeOffset(new DateTime(2025, 2, 20, 12, 47, 47, 168, DateTimeKind.Unspecified).AddTicks(4120), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.CreateIndex(
                name: "IX_SalesCategoryCode_WarehouseCode_SaleCategoryCode",
                schema: "Evolve",
                table: "SalesCategoryCode",
                columns: new[] { "WarehouseCode", "SaleCategoryCode" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_BoughtCategoryCode_WarehouseCode_BuyCategoryCode",
                schema: "Evolve",
                table: "BoughtCategoryCode",
                columns: new[] { "WarehouseCode", "BuyCategoryCode" },
                unique: true);
        }
    }
}
