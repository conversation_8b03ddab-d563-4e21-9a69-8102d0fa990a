// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using WeBuyCars.Evolve.Infrastructure.Data.SQL.Context;

#nullable disable

namespace WeBuyCars.Evolve.Infrastructure.Data.SQL.Migrations
{
    [DbContext(typeof(WBCEvolveContext))]
    [Migration("20241111101301_Update3rdPartyCommission")]
    partial class Update3rdPartyCommission
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("Evolve")
                .HasAnnotation("ProductVersion", "6.0.3")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder, 1L, 1);

            modelBuilder.Entity("WeBuyCars.Evolve.Core.Entities.AccessoryCode", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasDefaultValue("System");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("DealerTermsCode")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("DeliveryCode")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<decimal>("DocumentationFeeLimitValue")
                        .HasColumnType("decimal(18,5)");

                    b.Property<string>("DocumentationFeeMaxCode")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("DocumentationFeeMaxMinCode")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("LicenseAndRegistrationCode")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("WarrantyCode")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.HasKey("Id");

                    b.ToTable("AccessoryCode", "Evolve");

                    b.HasData(
                        new
                        {
                            Id = 1L,
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2024, 11, 11, 10, 13, 1, 37, DateTimeKind.Unspecified).AddTicks(440), new TimeSpan(0, 0, 0, 0, 0)),
                            DealerTermsCode = "1401",
                            Deleted = false,
                            DeliveryCode = "1002",
                            DocumentationFeeLimitValue = 17391.3043478261m,
                            DocumentationFeeMaxCode = "1501",
                            DocumentationFeeMaxMinCode = "1502",
                            LicenseAndRegistrationCode = "1001",
                            Location = "MID",
                            WarrantyCode = "1301"
                        },
                        new
                        {
                            Id = 2L,
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2024, 11, 11, 10, 13, 1, 37, DateTimeKind.Unspecified).AddTicks(440), new TimeSpan(0, 0, 0, 0, 0)),
                            DealerTermsCode = "2401",
                            Deleted = false,
                            DeliveryCode = "2002",
                            DocumentationFeeLimitValue = 17391.3043478261m,
                            DocumentationFeeMaxCode = "2501",
                            DocumentationFeeMaxMinCode = "2502",
                            LicenseAndRegistrationCode = "2001",
                            Location = "SIL",
                            WarrantyCode = "2301"
                        },
                        new
                        {
                            Id = 3L,
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2024, 11, 11, 10, 13, 1, 37, DateTimeKind.Unspecified).AddTicks(440), new TimeSpan(0, 0, 0, 0, 0)),
                            DealerTermsCode = "3401",
                            Deleted = false,
                            DeliveryCode = "3002",
                            DocumentationFeeLimitValue = 17391.3043478261m,
                            DocumentationFeeMaxCode = "3501",
                            DocumentationFeeMaxMinCode = "3502",
                            LicenseAndRegistrationCode = "3001",
                            Location = "DBN",
                            WarrantyCode = "3301"
                        },
                        new
                        {
                            Id = 4L,
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2024, 11, 11, 10, 13, 1, 37, DateTimeKind.Unspecified).AddTicks(440), new TimeSpan(0, 0, 0, 0, 0)),
                            DealerTermsCode = "4401",
                            Deleted = false,
                            DeliveryCode = "4002",
                            DocumentationFeeLimitValue = 17391.3043478261m,
                            DocumentationFeeMaxCode = "4501",
                            DocumentationFeeMaxMinCode = "4502",
                            LicenseAndRegistrationCode = "4001",
                            Location = "CPT",
                            WarrantyCode = "4301"
                        },
                        new
                        {
                            Id = 5L,
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2024, 11, 11, 10, 13, 1, 37, DateTimeKind.Unspecified).AddTicks(440), new TimeSpan(0, 0, 0, 0, 0)),
                            DealerTermsCode = "5401",
                            Deleted = false,
                            DeliveryCode = "5002",
                            DocumentationFeeLimitValue = 17391.3043478261m,
                            DocumentationFeeMaxCode = "5501",
                            DocumentationFeeMaxMinCode = "5502",
                            LicenseAndRegistrationCode = "5001",
                            Location = "JBS",
                            WarrantyCode = "5301"
                        },
                        new
                        {
                            Id = 6L,
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2024, 11, 11, 10, 13, 1, 37, DateTimeKind.Unspecified).AddTicks(450), new TimeSpan(0, 0, 0, 0, 0)),
                            DealerTermsCode = "6401",
                            Deleted = false,
                            DeliveryCode = "6002",
                            DocumentationFeeLimitValue = 17391.3043478261m,
                            DocumentationFeeMaxCode = "6501",
                            DocumentationFeeMaxMinCode = "6502",
                            LicenseAndRegistrationCode = "6001",
                            Location = "PEL",
                            WarrantyCode = "6301"
                        },
                        new
                        {
                            Id = 7L,
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2024, 11, 11, 10, 13, 1, 37, DateTimeKind.Unspecified).AddTicks(450), new TimeSpan(0, 0, 0, 0, 0)),
                            DealerTermsCode = "7401",
                            Deleted = false,
                            DeliveryCode = "7002",
                            DocumentationFeeLimitValue = 17391.3043478261m,
                            DocumentationFeeMaxCode = "7501",
                            DocumentationFeeMaxMinCode = "7502",
                            LicenseAndRegistrationCode = "7001",
                            Location = "PHU",
                            WarrantyCode = "7301"
                        },
                        new
                        {
                            Id = 8L,
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2024, 11, 11, 10, 13, 1, 37, DateTimeKind.Unspecified).AddTicks(450), new TimeSpan(0, 0, 0, 0, 0)),
                            DealerTermsCode = "8401",
                            Deleted = false,
                            DeliveryCode = "8002",
                            DocumentationFeeLimitValue = 17391.3043478261m,
                            DocumentationFeeMaxCode = "8501",
                            DocumentationFeeMaxMinCode = "8502",
                            LicenseAndRegistrationCode = "8001",
                            Location = "GER",
                            WarrantyCode = "8301"
                        },
                        new
                        {
                            Id = 9L,
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2024, 11, 11, 10, 13, 1, 37, DateTimeKind.Unspecified).AddTicks(450), new TimeSpan(0, 0, 0, 0, 0)),
                            DealerTermsCode = "9401",
                            Deleted = false,
                            DeliveryCode = "9002",
                            DocumentationFeeLimitValue = 17391.3043478261m,
                            DocumentationFeeMaxCode = "9501",
                            DocumentationFeeMaxMinCode = "9502",
                            LicenseAndRegistrationCode = "9001",
                            Location = "DOM",
                            WarrantyCode = "9301"
                        },
                        new
                        {
                            Id = 10L,
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2024, 11, 11, 10, 13, 1, 37, DateTimeKind.Unspecified).AddTicks(450), new TimeSpan(0, 0, 0, 0, 0)),
                            DealerTermsCode = "10401",
                            Deleted = false,
                            DeliveryCode = "10002",
                            DocumentationFeeLimitValue = 17391.3043478261m,
                            DocumentationFeeMaxCode = "10501",
                            DocumentationFeeMaxMinCode = "10502",
                            LicenseAndRegistrationCode = "10001",
                            Location = "POL",
                            WarrantyCode = "10301"
                        },
                        new
                        {
                            Id = 11L,
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2024, 11, 11, 10, 13, 1, 37, DateTimeKind.Unspecified).AddTicks(450), new TimeSpan(0, 0, 0, 0, 0)),
                            DealerTermsCode = "11401",
                            Deleted = false,
                            DeliveryCode = "11002",
                            DocumentationFeeLimitValue = 17391.3043478261m,
                            DocumentationFeeMaxCode = "11501",
                            DocumentationFeeMaxMinCode = "11502",
                            LicenseAndRegistrationCode = "11001",
                            Location = "NEL",
                            WarrantyCode = "11301"
                        },
                        new
                        {
                            Id = 12L,
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2024, 11, 11, 10, 13, 1, 37, DateTimeKind.Unspecified).AddTicks(450), new TimeSpan(0, 0, 0, 0, 0)),
                            DealerTermsCode = "12401",
                            Deleted = false,
                            DeliveryCode = "12002",
                            DocumentationFeeLimitValue = 17391.3043478261m,
                            DocumentationFeeMaxCode = "12501",
                            DocumentationFeeMaxMinCode = "12502",
                            LicenseAndRegistrationCode = "12001",
                            Location = "RIC",
                            WarrantyCode = "12301"
                        },
                        new
                        {
                            Id = 13L,
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2024, 11, 11, 10, 13, 1, 37, DateTimeKind.Unspecified).AddTicks(450), new TimeSpan(0, 0, 0, 0, 0)),
                            DealerTermsCode = "13401",
                            Deleted = false,
                            DeliveryCode = "13002",
                            DocumentationFeeLimitValue = 17391.3043478261m,
                            DocumentationFeeMaxCode = "13501",
                            DocumentationFeeMaxMinCode = "13502",
                            LicenseAndRegistrationCode = "13001",
                            Location = "GEO",
                            WarrantyCode = "13301"
                        },
                        new
                        {
                            Id = 14L,
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2024, 11, 11, 10, 13, 1, 37, DateTimeKind.Unspecified).AddTicks(450), new TimeSpan(0, 0, 0, 0, 0)),
                            DealerTermsCode = "14401",
                            Deleted = false,
                            DeliveryCode = "14002",
                            DocumentationFeeLimitValue = 17391.3043478261m,
                            DocumentationFeeMaxCode = "14501",
                            DocumentationFeeMaxMinCode = "14502",
                            LicenseAndRegistrationCode = "14001",
                            Location = "RIV",
                            WarrantyCode = "14301"
                        },
                        new
                        {
                            Id = 15L,
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2024, 11, 11, 10, 13, 1, 37, DateTimeKind.Unspecified).AddTicks(450), new TimeSpan(0, 0, 0, 0, 0)),
                            DealerTermsCode = "15401",
                            Deleted = false,
                            DeliveryCode = "15002",
                            DocumentationFeeLimitValue = 17391.3043478261m,
                            DocumentationFeeMaxCode = "15501",
                            DocumentationFeeMaxMinCode = "15502",
                            LicenseAndRegistrationCode = "15001",
                            Location = "EPP",
                            WarrantyCode = "15301"
                        },
                        new
                        {
                            Id = 16L,
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2024, 11, 11, 10, 13, 1, 37, DateTimeKind.Unspecified).AddTicks(460), new TimeSpan(0, 0, 0, 0, 0)),
                            DealerTermsCode = "18401",
                            Deleted = false,
                            DeliveryCode = "18002",
                            DocumentationFeeLimitValue = 17391.3043478261m,
                            DocumentationFeeMaxCode = "18501",
                            DocumentationFeeMaxMinCode = "18502",
                            LicenseAndRegistrationCode = "18001",
                            Location = "PMB",
                            WarrantyCode = "18301"
                        },
                        new
                        {
                            Id = 17L,
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2024, 11, 11, 10, 13, 1, 37, DateTimeKind.Unspecified).AddTicks(460), new TimeSpan(0, 0, 0, 0, 0)),
                            DealerTermsCode = "16401",
                            Deleted = false,
                            DeliveryCode = "16002",
                            DocumentationFeeLimitValue = 17391.3043478261m,
                            DocumentationFeeMaxCode = "16501",
                            DocumentationFeeMaxMinCode = "16502",
                            LicenseAndRegistrationCode = "16001",
                            Location = "ELS",
                            WarrantyCode = "16301"
                        },
                        new
                        {
                            Id = 18L,
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2024, 11, 11, 10, 13, 1, 37, DateTimeKind.Unspecified).AddTicks(460), new TimeSpan(0, 0, 0, 0, 0)),
                            DealerTermsCode = "17401",
                            Deleted = false,
                            DeliveryCode = "17002",
                            DocumentationFeeLimitValue = 17391.3043478261m,
                            DocumentationFeeMaxCode = "17501",
                            DocumentationFeeMaxMinCode = "17502",
                            LicenseAndRegistrationCode = "17001",
                            Location = "RUS",
                            WarrantyCode = "17301"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Evolve.Core.Entities.BuyLeadEvaluation", b =>
                {
                    b.Property<string>("BuyLeadId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Condition")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("FullServiceHistory")
                        .HasColumnType("bit");

                    b.Property<bool>("WarrantyActive")
                        .HasColumnType("bit");

                    b.HasKey("BuyLeadId");

                    b.ToView("BuyLeadEvaluation");
                });

            modelBuilder.Entity("WeBuyCars.Evolve.Core.Entities.CatalogueItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<string>("BodyType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CatalogueDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CatalogueId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DiscontinuationDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FuelTankSize")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FuelType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IntroductionDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Kilowatts")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MMCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MakeDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModelDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NumberOfGears")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("RegYear")
                        .HasColumnType("int");

                    b.Property<string>("TransmissionType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VariantDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToView("CATCatalogueItem");
                });

            modelBuilder.Entity("WeBuyCars.Evolve.Core.Entities.Customer", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<string>("AccountNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AddressLine1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AddressLine2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BusinessName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BusinessRegistrationNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("City")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyRegistrationNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DealershipCustomer_Url")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EvolveCustomerSequenceId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FirstName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IdNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Initials")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsSuspended")
                        .HasColumnType("bit");

                    b.Property<string>("LandlineNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MobileNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PassportNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PostalCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Province")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("QualifiesForDiscount")
                        .HasColumnType("bit");

                    b.Property<string>("RegisterNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Suburb")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VatNumber")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToView("CRMCustomers");
                });

            modelBuilder.Entity("WeBuyCars.Evolve.Core.Entities.EvolveStockItem", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("EvolveCreatedDateTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool?>("EvolveCreationSuccess")
                        .HasColumnType("bit");

                    b.Property<string>("EvolveInitialFullStockMaintenaceRequest")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EvolveInitialFullStockMaintenaceResponse")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FailureExplainReason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("StockNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("StockNumber")
                        .IsUnique();

                    b.ToTable("EvolveStockItem", "Evolve");
                });

            modelBuilder.Entity("WeBuyCars.Evolve.Core.Entities.ImsDailyBuysSnapshot", b =>
                {
                    b.Property<decimal>("CostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTimeOffset>("PurchaseDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("StockItemId")
                        .HasColumnType("uniqueidentifier");

                    b.ToView("ImsDailyBuysSnapshot");
                });

            modelBuilder.Entity("WeBuyCars.Evolve.Core.Entities.ImsDailySalesSnapshot", b =>
                {
                    b.Property<string>("Branch")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BranchCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("BuyNowPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("CostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTimeOffset>("PurchaseDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTimeOffset>("SoldDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("StockItemId")
                        .HasColumnType("uniqueidentifier");

                    b.ToView("ImsDailySalesSnapshot");
                });

            modelBuilder.Entity("WeBuyCars.Evolve.Core.Entities.MonthlyTransaction", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<bool>("Archived")
                        .HasColumnType("bit");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<int>("TypeId")
                        .HasColumnType("int");

                    b.Property<long>("Value")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TypeId");

                    b.ToTable("MonthlyTransaction", "Evolve");

                    b.HasData(
                        new
                        {
                            Id = 1L,
                            Archived = false,
                            Date = new DateTime(2015, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 636L
                        },
                        new
                        {
                            Id = 2L,
                            Archived = false,
                            Date = new DateTime(2015, 2, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 813L
                        },
                        new
                        {
                            Id = 3L,
                            Archived = false,
                            Date = new DateTime(2015, 3, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 810L
                        },
                        new
                        {
                            Id = 4L,
                            Archived = false,
                            Date = new DateTime(2015, 4, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 662L
                        },
                        new
                        {
                            Id = 5L,
                            Archived = false,
                            Date = new DateTime(2015, 5, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 717L
                        },
                        new
                        {
                            Id = 6L,
                            Archived = false,
                            Date = new DateTime(2015, 6, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 656L
                        },
                        new
                        {
                            Id = 7L,
                            Archived = false,
                            Date = new DateTime(2015, 7, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 817L
                        },
                        new
                        {
                            Id = 8L,
                            Archived = false,
                            Date = new DateTime(2015, 8, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 865L
                        },
                        new
                        {
                            Id = 9L,
                            Archived = false,
                            Date = new DateTime(2015, 9, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 827L
                        },
                        new
                        {
                            Id = 10L,
                            Archived = false,
                            Date = new DateTime(2015, 10, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 927L
                        },
                        new
                        {
                            Id = 11L,
                            Archived = false,
                            Date = new DateTime(2015, 11, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 1025L
                        },
                        new
                        {
                            Id = 12L,
                            Archived = false,
                            Date = new DateTime(2015, 12, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 888L
                        },
                        new
                        {
                            Id = 13L,
                            Archived = false,
                            Date = new DateTime(2016, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 733L
                        },
                        new
                        {
                            Id = 14L,
                            Archived = false,
                            Date = new DateTime(2016, 2, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 1234L
                        },
                        new
                        {
                            Id = 15L,
                            Archived = false,
                            Date = new DateTime(2016, 3, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 1105L
                        },
                        new
                        {
                            Id = 16L,
                            Archived = false,
                            Date = new DateTime(2016, 4, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 1286L
                        },
                        new
                        {
                            Id = 17L,
                            Archived = false,
                            Date = new DateTime(2016, 5, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 1269L
                        },
                        new
                        {
                            Id = 18L,
                            Archived = false,
                            Date = new DateTime(2016, 6, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 1213L
                        },
                        new
                        {
                            Id = 19L,
                            Archived = false,
                            Date = new DateTime(2016, 7, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 1136L
                        },
                        new
                        {
                            Id = 20L,
                            Archived = false,
                            Date = new DateTime(2016, 8, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 1432L
                        },
                        new
                        {
                            Id = 21L,
                            Archived = false,
                            Date = new DateTime(2016, 9, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 1707L
                        },
                        new
                        {
                            Id = 22L,
                            Archived = false,
                            Date = new DateTime(2016, 10, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 1469L
                        },
                        new
                        {
                            Id = 23L,
                            Archived = false,
                            Date = new DateTime(2016, 11, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 1701L
                        },
                        new
                        {
                            Id = 24L,
                            Archived = false,
                            Date = new DateTime(2016, 12, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 1431L
                        },
                        new
                        {
                            Id = 25L,
                            Archived = false,
                            Date = new DateTime(2017, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 1653L
                        },
                        new
                        {
                            Id = 26L,
                            Archived = false,
                            Date = new DateTime(2017, 2, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 1633L
                        },
                        new
                        {
                            Id = 27L,
                            Archived = false,
                            Date = new DateTime(2017, 3, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 1977L
                        },
                        new
                        {
                            Id = 28L,
                            Archived = false,
                            Date = new DateTime(2017, 4, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 1689L
                        },
                        new
                        {
                            Id = 29L,
                            Archived = false,
                            Date = new DateTime(2017, 5, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 2039L
                        },
                        new
                        {
                            Id = 30L,
                            Archived = false,
                            Date = new DateTime(2017, 6, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 2179L
                        },
                        new
                        {
                            Id = 31L,
                            Archived = false,
                            Date = new DateTime(2017, 7, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 2397L
                        },
                        new
                        {
                            Id = 32L,
                            Archived = false,
                            Date = new DateTime(2017, 8, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 2691L
                        },
                        new
                        {
                            Id = 33L,
                            Archived = false,
                            Date = new DateTime(2017, 9, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 2524L
                        },
                        new
                        {
                            Id = 34L,
                            Archived = false,
                            Date = new DateTime(2017, 10, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 2748L
                        },
                        new
                        {
                            Id = 35L,
                            Archived = false,
                            Date = new DateTime(2017, 11, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 3200L
                        },
                        new
                        {
                            Id = 36L,
                            Archived = false,
                            Date = new DateTime(2017, 12, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 2524L
                        },
                        new
                        {
                            Id = 37L,
                            Archived = false,
                            Date = new DateTime(2018, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 3191L
                        },
                        new
                        {
                            Id = 38L,
                            Archived = false,
                            Date = new DateTime(2018, 2, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 3153L
                        },
                        new
                        {
                            Id = 39L,
                            Archived = false,
                            Date = new DateTime(2018, 3, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 3246L
                        },
                        new
                        {
                            Id = 40L,
                            Archived = false,
                            Date = new DateTime(2018, 4, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 3083L
                        },
                        new
                        {
                            Id = 41L,
                            Archived = false,
                            Date = new DateTime(2018, 5, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 3589L
                        },
                        new
                        {
                            Id = 42L,
                            Archived = false,
                            Date = new DateTime(2018, 6, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 3476L
                        },
                        new
                        {
                            Id = 43L,
                            Archived = false,
                            Date = new DateTime(2018, 7, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 3564L
                        },
                        new
                        {
                            Id = 44L,
                            Archived = false,
                            Date = new DateTime(2018, 8, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 3900L
                        },
                        new
                        {
                            Id = 45L,
                            Archived = false,
                            Date = new DateTime(2018, 9, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 3643L
                        },
                        new
                        {
                            Id = 46L,
                            Archived = false,
                            Date = new DateTime(2018, 10, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 4554L
                        },
                        new
                        {
                            Id = 47L,
                            Archived = false,
                            Date = new DateTime(2018, 11, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 4460L
                        },
                        new
                        {
                            Id = 48L,
                            Archived = false,
                            Date = new DateTime(2018, 12, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 3870L
                        },
                        new
                        {
                            Id = 49L,
                            Archived = false,
                            Date = new DateTime(2019, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 4439L
                        },
                        new
                        {
                            Id = 50L,
                            Archived = false,
                            Date = new DateTime(2019, 2, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 4791L
                        },
                        new
                        {
                            Id = 51L,
                            Archived = false,
                            Date = new DateTime(2019, 3, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 4834L
                        },
                        new
                        {
                            Id = 52L,
                            Archived = false,
                            Date = new DateTime(2019, 4, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 5004L
                        },
                        new
                        {
                            Id = 53L,
                            Archived = false,
                            Date = new DateTime(2019, 5, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 1,
                            Value = 5883L
                        },
                        new
                        {
                            Id = 54L,
                            Archived = false,
                            Date = new DateTime(2015, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 406L
                        },
                        new
                        {
                            Id = 55L,
                            Archived = false,
                            Date = new DateTime(2015, 2, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 700L
                        },
                        new
                        {
                            Id = 56L,
                            Archived = false,
                            Date = new DateTime(2015, 3, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 782L
                        },
                        new
                        {
                            Id = 57L,
                            Archived = false,
                            Date = new DateTime(2015, 4, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 639L
                        },
                        new
                        {
                            Id = 58L,
                            Archived = false,
                            Date = new DateTime(2015, 5, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 721L
                        },
                        new
                        {
                            Id = 59L,
                            Archived = false,
                            Date = new DateTime(2015, 6, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 692L
                        },
                        new
                        {
                            Id = 60L,
                            Archived = false,
                            Date = new DateTime(2015, 7, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 720L
                        },
                        new
                        {
                            Id = 61L,
                            Archived = false,
                            Date = new DateTime(2015, 8, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 846L
                        },
                        new
                        {
                            Id = 62L,
                            Archived = false,
                            Date = new DateTime(2015, 9, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 782L
                        },
                        new
                        {
                            Id = 63L,
                            Archived = false,
                            Date = new DateTime(2015, 10, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 895L
                        },
                        new
                        {
                            Id = 64L,
                            Archived = false,
                            Date = new DateTime(2015, 11, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 954L
                        },
                        new
                        {
                            Id = 65L,
                            Archived = false,
                            Date = new DateTime(2015, 12, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 945L
                        },
                        new
                        {
                            Id = 66L,
                            Archived = false,
                            Date = new DateTime(2016, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 1045L
                        },
                        new
                        {
                            Id = 67L,
                            Archived = false,
                            Date = new DateTime(2016, 2, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 918L
                        },
                        new
                        {
                            Id = 68L,
                            Archived = false,
                            Date = new DateTime(2016, 3, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 927L
                        },
                        new
                        {
                            Id = 69L,
                            Archived = false,
                            Date = new DateTime(2016, 4, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 1039L
                        },
                        new
                        {
                            Id = 70L,
                            Archived = false,
                            Date = new DateTime(2016, 5, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 1199L
                        },
                        new
                        {
                            Id = 71L,
                            Archived = false,
                            Date = new DateTime(2016, 6, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 1270L
                        },
                        new
                        {
                            Id = 72L,
                            Archived = false,
                            Date = new DateTime(2016, 7, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 1408L
                        },
                        new
                        {
                            Id = 73L,
                            Archived = false,
                            Date = new DateTime(2016, 8, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 1405L
                        },
                        new
                        {
                            Id = 74L,
                            Archived = false,
                            Date = new DateTime(2016, 9, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 1485L
                        },
                        new
                        {
                            Id = 75L,
                            Archived = false,
                            Date = new DateTime(2016, 10, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 1460L
                        },
                        new
                        {
                            Id = 76L,
                            Archived = false,
                            Date = new DateTime(2016, 11, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 1740L
                        },
                        new
                        {
                            Id = 77L,
                            Archived = false,
                            Date = new DateTime(2016, 12, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 1375L
                        },
                        new
                        {
                            Id = 78L,
                            Archived = false,
                            Date = new DateTime(2017, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 1542L
                        },
                        new
                        {
                            Id = 79L,
                            Archived = false,
                            Date = new DateTime(2017, 2, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 1585L
                        },
                        new
                        {
                            Id = 80L,
                            Archived = false,
                            Date = new DateTime(2017, 3, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 1877L
                        },
                        new
                        {
                            Id = 81L,
                            Archived = false,
                            Date = new DateTime(2017, 4, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 1600L
                        },
                        new
                        {
                            Id = 82L,
                            Archived = false,
                            Date = new DateTime(2017, 5, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 1993L
                        },
                        new
                        {
                            Id = 83L,
                            Archived = false,
                            Date = new DateTime(2017, 6, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 2031L
                        },
                        new
                        {
                            Id = 84L,
                            Archived = false,
                            Date = new DateTime(2017, 7, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 2294L
                        },
                        new
                        {
                            Id = 85L,
                            Archived = false,
                            Date = new DateTime(2017, 8, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 2598L
                        },
                        new
                        {
                            Id = 86L,
                            Archived = false,
                            Date = new DateTime(2017, 9, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 2200L
                        },
                        new
                        {
                            Id = 87L,
                            Archived = false,
                            Date = new DateTime(2017, 10, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 2728L
                        },
                        new
                        {
                            Id = 88L,
                            Archived = false,
                            Date = new DateTime(2017, 11, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 3135L
                        },
                        new
                        {
                            Id = 89L,
                            Archived = false,
                            Date = new DateTime(2017, 12, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 2482L
                        },
                        new
                        {
                            Id = 90L,
                            Archived = false,
                            Date = new DateTime(2018, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 3233L
                        },
                        new
                        {
                            Id = 91L,
                            Archived = false,
                            Date = new DateTime(2018, 2, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 2974L
                        },
                        new
                        {
                            Id = 92L,
                            Archived = false,
                            Date = new DateTime(2018, 3, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 3277L
                        },
                        new
                        {
                            Id = 93L,
                            Archived = false,
                            Date = new DateTime(2018, 4, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 3051L
                        },
                        new
                        {
                            Id = 94L,
                            Archived = false,
                            Date = new DateTime(2018, 5, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 3580L
                        },
                        new
                        {
                            Id = 95L,
                            Archived = false,
                            Date = new DateTime(2018, 6, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 3254L
                        },
                        new
                        {
                            Id = 96L,
                            Archived = false,
                            Date = new DateTime(2018, 7, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 3777L
                        },
                        new
                        {
                            Id = 97L,
                            Archived = false,
                            Date = new DateTime(2018, 8, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 3747L
                        },
                        new
                        {
                            Id = 98L,
                            Archived = false,
                            Date = new DateTime(2018, 9, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 3624L
                        },
                        new
                        {
                            Id = 99L,
                            Archived = false,
                            Date = new DateTime(2018, 10, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 4307L
                        },
                        new
                        {
                            Id = 100L,
                            Archived = false,
                            Date = new DateTime(2018, 11, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 4372L
                        },
                        new
                        {
                            Id = 101L,
                            Archived = false,
                            Date = new DateTime(2018, 12, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 3407L
                        },
                        new
                        {
                            Id = 102L,
                            Archived = false,
                            Date = new DateTime(2019, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 4937L
                        },
                        new
                        {
                            Id = 103L,
                            Archived = false,
                            Date = new DateTime(2019, 2, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 4529L
                        },
                        new
                        {
                            Id = 104L,
                            Archived = false,
                            Date = new DateTime(2019, 3, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 4838L
                        },
                        new
                        {
                            Id = 105L,
                            Archived = false,
                            Date = new DateTime(2019, 4, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 4539L
                        },
                        new
                        {
                            Id = 106L,
                            Archived = false,
                            Date = new DateTime(2019, 5, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            TypeId = 2,
                            Value = 5500L
                        });
                });

            modelBuilder.Entity("WeBuyCars.Evolve.Core.Entities.RepoProvider", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<string>("AccountNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("CommissionEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<decimal>("CommissionPercent")
                        .HasColumnType("decimal(5,2)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasDefaultValue("System");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("ImsFlagCode")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal>("MaxCommission")
                        .HasColumnType("decimal(8,2)");

                    b.Property<decimal>("MinCommission")
                        .HasColumnType("decimal(8,2)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("SalesCodePrefix")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.HasKey("Id");

                    b.ToTable("RepoProvider", "Evolve");

                    b.HasData(
                        new
                        {
                            Id = 1L,
                            AccountNumber = "971566",
                            CommissionEnabled = true,
                            CommissionPercent = 4.00m,
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2024, 11, 11, 10, 13, 1, 37, DateTimeKind.Unspecified).AddTicks(5400), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            ImsFlagCode = "GomoRepo",
                            MaxCommission = 10434.7826087m,
                            MinCommission = 5217.39130435m,
                            SalesCodePrefix = "XP"
                        },
                        new
                        {
                            Id = 2L,
                            AccountNumber = "971566",
                            CommissionEnabled = true,
                            CommissionPercent = 4.00m,
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2024, 11, 11, 10, 13, 1, 37, DateTimeKind.Unspecified).AddTicks(5400), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            ImsFlagCode = "StandardBankRepo",
                            MaxCommission = 10434.7826087m,
                            MinCommission = 5217.39130435m,
                            SalesCodePrefix = "XP"
                        },
                        new
                        {
                            Id = 3L,
                            AccountNumber = "1559660",
                            CommissionEnabled = true,
                            CommissionPercent = 0m,
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2024, 11, 11, 10, 13, 1, 37, DateTimeKind.Unspecified).AddTicks(5400), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            ImsFlagCode = "FlexiDriveConsignment",
                            MaxCommission = 8695.65217391m,
                            MinCommission = 8695.65217391m,
                            SalesCodePrefix = "XP"
                        },
                        new
                        {
                            Id = 4L,
                            AccountNumber = "1594567",
                            CommissionEnabled = true,
                            CommissionPercent = 4.25m,
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2024, 11, 11, 10, 13, 1, 37, DateTimeKind.Unspecified).AddTicks(5400), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            ImsFlagCode = "TruckStoreConsignment",
                            MaxCommission = 32000.00m,
                            MinCommission = 8500.00m,
                            SalesCodePrefix = "XP"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Evolve.Core.Entities.SaleLead", b =>
                {
                    b.Property<string>("AccountNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EvolveSaleCategoryCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LeadCategoryName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LeadCreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LeadNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LeadSourceName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SalesCategoryCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StockNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransactionType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WarehouseCode")
                        .HasColumnType("nvarchar(max)");

                    b.ToView("SaleLeads");
                });

            modelBuilder.Entity("WeBuyCars.Evolve.Core.Entities.SalesActuals", b =>
                {
                    b.Property<string>("ArAccountNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BranchCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BranchName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DealStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DealType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("FlatGp")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("InvoiceAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTimeOffset?>("InvoiceDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("InvoiceNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("RegistrationYear")
                        .HasColumnType("int");

                    b.Property<decimal?>("RetailAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("SalesExecName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StockNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("Units")
                        .HasColumnType("int");

                    b.Property<string>("VehicleDescription")
                        .HasColumnType("nvarchar(max)");

                    b.ToView("SalesActuals");
                });

            modelBuilder.Entity("WeBuyCars.Evolve.Core.Entities.StockItemPriceRecon", b =>
                {
                    b.Property<string>("StockNumber")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ActivityStatus")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("AdjustedCostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("StockStatus")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("StockNumber");

                    b.ToView("ImsReconStockItemPrices");
                });

            modelBuilder.Entity("WeBuyCars.Evolve.Core.Entities.StockItemRecon", b =>
                {
                    b.Property<string>("StockNumber")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ActivityStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CatalgueIdMMcode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CatalogueId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("CatalogueIdRegistrationYear")
                        .HasColumnType("int");

                    b.Property<string>("MmCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("RegistrationYear")
                        .HasColumnType("int");

                    b.Property<string>("StockStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WarehouseCode")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("StockNumber");

                    b.ToView("ImsReconStockItem");
                });

            modelBuilder.Entity("WeBuyCars.Evolve.Core.Entities.TransactionLog", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<string>("AccountNumber")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("Message")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("Reference")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("StatusId")
                        .HasColumnType("int");

                    b.Property<string>("StockNumber")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("TargetSystem")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("TransactionType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("StatusId");

                    b.ToTable("TransactionLog", "Evolve");
                });

            modelBuilder.Entity("WeBuyCars.Evolve.Core.Entities.TransactionStatus", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("Id");

                    b.ToTable("TransactionStatus", "Evolve");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Description = "Unknown",
                            Name = "Unknown"
                        },
                        new
                        {
                            Id = 2,
                            Description = "The process is pending.",
                            Name = "Pending"
                        },
                        new
                        {
                            Id = 3,
                            Description = "The process has completed.",
                            Name = "Completed"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Evolve.Core.Entities.TransactionType", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("Id");

                    b.ToTable("TransactionType", "Evolve");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Name = "Bought"
                        },
                        new
                        {
                            Id = 2,
                            Name = "Sold"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Evolve.Core.Entities.MonthlyTransaction", b =>
                {
                    b.HasOne("WeBuyCars.Evolve.Core.Entities.TransactionType", "Type")
                        .WithMany()
                        .HasForeignKey("TypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Type");
                });

            modelBuilder.Entity("WeBuyCars.Evolve.Core.Entities.TransactionLog", b =>
                {
                    b.HasOne("WeBuyCars.Evolve.Core.Entities.TransactionStatus", "Status")
                        .WithMany()
                        .HasForeignKey("StatusId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Status");
                });
#pragma warning restore 612, 618
        }
    }
}
