using Newtonsoft.Json;

namespace WeBuyCars.Evolve.Infrastructure.BLMS.Models.Subscriptions;

public sealed record LeadFlagCreatedDto
{
    public string Originator { get; set; } = string.Empty;
    public string EventType { get; set; } = string.Empty;
    public DateTimeOffset DateTime { get; set; }
    public LeadFlagCreatedDataDto Data { get; set; } = new();

    public override string ToString() => JsonConvert.SerializeObject(this);
}