using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Extensions;
using WeBuyCars.Core.Infrastructure.ActiveDirectory.Authentication.Extensions;
using WeBuyCars.eNatis.RTMC.Infrastructure.Data.Context;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.RTMC;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Data.Repositories;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.Shared;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.ArchivedInformation;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.Settings;
using Microsoft.AspNetCore.Hosting;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.Network;
using WeBuyCars.eNatis.RTMC.Api.Configurations;
using MassTransit;
using System;
using WeBuyCars.Core.Infrastructure.Swagger.Extensions;
using Serilog;

[assembly: ApiConventionType(typeof(DefaultApiConventions))]
namespace WeBuyCars.eNatis.RTMC.Api
{
    public class Startup
    {
        public Startup(IConfiguration configuration, IWebHostEnvironment env)
        {
            Configuration = configuration;
            Environment = env;
        }

        public IConfiguration Configuration { get; }
        public IWebHostEnvironment Environment { get; private set; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {

            ConfigureMvc(services);

            services.AddApplicationInsightsTelemetry(Configuration);

            ConfigureSwagger(services);

            ConfigureDbContext(services);

            ConfigureAuthentication(services);
            ConfigureAuthorization(services);

            ConfigureHealthChecks(services);
            ConfigureMassTransit(services);
            ConfigureAutoMapper(services);
            ConfigureIntegrations(services);
            ConfigureRepositories(services);
            ConfigureNatisIntegrationServices(services);
            ConfigureServiceExtensions(services);
            ConfigureEnatis(services);
            ConfigureEncryptionServices(services);
            ConfigureVPNCheck(services);

            // CORS
            services.AddCors(options => options.AddPolicy("WBCPolicy", builder => builder.AllowAnyOrigin()
                .AllowAnyHeader()
                .AllowAnyMethod()));

            ConfigureApplicationInsights(services);

            services.AddResponseCompression();

            Log.Error($"RTMC API started.");

        }

        void ConfigureSwagger(IServiceCollection services)
        {
            services.AddSwaggerDocumentation();
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IHostEnvironment env)
        {
            app.UseExceptionHandler("/error");
            app.UseSwaggerDocumentation();
            app.UseRouting();
            app.UseAuthentication();
            app.UseAuthorization();
            app.UseResponseCompression();

            // app.UseForFeature(FeatureFlags.USE_CORS, appBuilder => app.UseCors("WBCPolicy"));
            if (env.IsEnvironment("Local"))
            {
                app.UseCors("WBCPolicy");
            }

            //Enable App Insight Logging
            app.UseRequestBodyLogging();

            //Check for environment
            var serverEnvironmentOptions = new ServerEnvironmentOptions();
            Configuration.GetSection("ServerEnvironment").Bind(serverEnvironmentOptions);

            if (serverEnvironmentOptions.ServerType == "SOAP")
            {
                app.UseHealthChecks("/SOAPhealth");
            }
            else
            {
                app.UseHealthChecks("/RESThealth");
            }

            app.UseHealthChecks("/health");

            app.UseEndpoints(endpoints =>
                {
                    endpoints.MapControllers();
                });

        }

        void ConfigureHealthChecks(IServiceCollection services)
        {
            var hcBuilder = services.AddHealthChecks();
            hcBuilder.AddCheck("self", () => HealthCheckResult.Healthy());
            //services.AddHealthChecks().AddCheck<RTMCVPNHealthCheck>("vpn_health");
        }

        #region Internal Methods

        static void ConfigureMvc(IServiceCollection services)
        {
            services.AddControllers();
            services.AddMvc(options =>
                {
                    options.EnableEndpointRouting = false;
                    var policy = new AuthorizationPolicyBuilder().RequireAuthenticatedUser().Build();
                    options.Filters.Add(new AuthorizeFilter(policy));
                }

            ).AddNewtonsoftJson(options =>
                {
                    options.SerializerSettings.Converters.Add(new Newtonsoft.Json.Converters.StringEnumConverter());
                    options.SerializerSettings.NullValueHandling = NullValueHandling.Ignore;
                    options.SerializerSettings.ReferenceLoopHandling = ReferenceLoopHandling.Ignore;
                    options.SerializerSettings.DateParseHandling = DateParseHandling.DateTimeOffset;
                    options.SerializerSettings.DateTimeZoneHandling = DateTimeZoneHandling.RoundtripKind;
                }
            );
        }

        void ConfigureAuthentication(IServiceCollection services)
        {
            // Add Active Directory
            services.AddAuthenticationSchemes(Configuration);
            Microsoft.IdentityModel.Logging.IdentityModelEventSource.ShowPII = false;
        }

        void ConfigureDbContext(IServiceCollection services)
        {

            services.AddDbContext<RTMCContext>(options =>
            {
                options.UseSqlServer(Configuration.GetConnectionString("WBCDB01") + "Application Name=RTMC;", sqlOptions =>
                    {
                        sqlOptions.MigrationsAssembly("WeBuyCars.eNatis.RTMC.Infrastructure.Data");
                        sqlOptions.MigrationsHistoryTable("__EFMigrationsHistory", RTMCContext.DEFAULT_SCHEMA);
                    });
            });
        }

        static void ConfigureAuthorization(IServiceCollection services)
        {
            services.AddControllers(config =>
            {
                var policy = new AuthorizationPolicyBuilder().RequireAuthenticatedUser().Build();
                config.Filters.Add(new AuthorizeFilter(policy));
            });

            services.AddAuthorization(options =>
            {
                // administrators can perform all actions.
                // options.AddPolicy("CanCreate", policy => policy.RequireClaim("RTMC_Natis_Create"));
                // options.AddPolicy("CanRead", policy => policy.RequireClaim("RTMC_Natis_CanRead"));

                // Add active directory
                // options.ConfigureAuthorization(true, _azureSettings);

                // Creators can only perform create/post actions.
                options.AddPolicy("GetVehicle", policy =>
                    policy.RequireAssertion(context =>
                        context.User.HasClaim(c => c.Type == "rtmc_admin" || c.Value == "rtmc_admin") ||
                        context.User.HasClaim(c => c.Type == "rtmc_getvehicle" || c.Value == "rtmc_getvehicle")
                    )
                );

                // Readers can only perform read/get actions.
                options.AddPolicy("GetDriver", policy =>
                    policy.RequireAssertion(context =>
                        context.User.HasClaim(c => c.Type == "rtmc_admin" || c.Value == "rtmc_admin") ||
                        context.User.HasClaim(c => c.Type == "rtmc_getdriver" || c.Value == "rtmc_getdriver")
                    )
                );

                // Settings Administrator Users can only perform create/post actions.
                options.AddPolicy("SettingsAdminRead", policy =>
                    policy.RequireAssertion(context =>
                        context.User.HasClaim(c => c.Type == "rtmc_admin" || c.Value == "rtmc_admin") ||
                        context.User.HasClaim(c => c.Type == "rtmc_settings_admin_read" || c.Value == "rtmc_settings_admin_read")
                    )
                );

                // Settings Administrators can only perform read/get actions.
                options.AddPolicy("SettingsAdminModify", policy =>
                    policy.RequireAssertion(context =>
                        context.User.HasClaim(c => c.Type == "rtmc_admin" || c.Value == "rtmc_admin") ||
                        context.User.HasClaim(c => c.Type == "rtmc_settings_admin_create" || c.Value == "rtmc_settings_admin_create") ||
                        context.User.HasClaim(c => c.Type == "rtmc_settings_admin_edit" || c.Value == "rtmc_settings_admin_edit")
                    )
                );

                // Settings Administrators can only perform Delete actions.
                options.AddPolicy("SettingsAdminDelete", policy =>
                    policy.RequireAssertion(context =>
                        context.User.HasClaim(c => c.Type == "rtmc_admin" || c.Value == "rtmc_admin") ||
                        context.User.HasClaim(c => c.Type == "rtmc_settings_admin_delete" || c.Value == "rtmc_settings_admin_delete")
                    )
                );

                // Verify Vehicle Owner Verification can only perform actions.
                options.AddPolicy("CanVerifyVehicleOwner", policy =>
                    policy.RequireAssertion(context =>
                        context.User.HasClaim(c => c.Type == "rtmc_admin" || c.Value == "rtmc_admin") ||
                        context.User.HasClaim(c => c.Type == "rtmc_vehicleownerverification" || c.Value == "rtmc_vehicleownerverification")
                    )
                );

                // Verify Motor Vehicle Fee Verification can only perform actions.
                options.AddPolicy("CanCalculateVehicleFees", policy =>
                    policy.RequireAssertion(context =>
                        context.User.HasClaim(c => c.Type == "rtmc_admin" || c.Value == "rtmc_admin") ||
                        context.User.HasClaim(c => c.Type == "rtmc_vehiclefeecalculate" || c.Value == "rtmc_vehiclefeecalculate")
                    )
                );

                // Online Change of Ownership
                options.AddPolicy("CanOnlineNCO", policy =>
                    policy.RequireAssertion(context =>
                        context.User.HasClaim(c => c.Type == "rtmc_admin" || c.Value == "rtmc_admin") ||
                        context.User.HasClaim(c => c.Type == "rtmc_onlinechangeofownership" || c.Value == "rtmc_onlinechangeofownership")
                    )
                );

                // Settings Vehicle Owner Registration can only perform actions.
                options.AddPolicy("CanRegisterVehicleOwner", policy =>
                    policy.RequireAssertion(context =>
                        context.User.HasClaim(c => c.Type == "rtmc_admin" || c.Value == "rtmc_admin") ||
                        context.User.HasClaim(c => c.Type == "rtmc_vehicleownerregistration" || c.Value == "rtmc_vehicleownerregistration")
                    )
                );

                // DeCrypt the Control Number
                options.AddPolicy("CanDecryptControlNumber", policy =>
                    policy.RequireAssertion(context =>
                        context.User.HasClaim(c => c.Type == "rtmc_admin" || c.Value == "rtmc_admin") ||
                        context.User.HasClaim(c => c.Type == "rtmc_decryptcontrolnumber" || c.Value == "rtmc_decryptcontrolnumber")
                    )
                );

                // Retrieve DeCrypted Online Change of Ownership Information
                options.AddPolicy("CanRetrieveDecryptedOnlineNCO", policy =>
                    policy.RequireAssertion(context =>
                        context.User.HasClaim(c => c.Type == "rtmc_admin" || c.Value == "rtmc_admin") ||
                        context.User.HasClaim(c => c.Type == "rtmc_decryptonlinechangeofownership" || c.Value == "rtmc_decryptonlinechangeofownership")
                    )
                );

                // Retrieve Vehicle Registration Information
                options.AddPolicy("CanRetrieveVehicleRegistration", policy =>
                    policy.RequireAssertion(context =>
                        context.User.HasClaim(c => c.Type == "rtmc_admin" || c.Value == "rtmc_admin") ||
                        context.User.HasClaim(c => c.Type == "rtmc_getvehicleownerregistration" || c.Value == "rtmc_getvehicleownerregistration")
                    )
                );

                // Retrieve Vehicle Registration Information
                options.AddPolicy("GetReportData", policy =>
                    policy.RequireAssertion(context =>
                        context.User.HasClaim(c => c.Type == "rtmc_admin" || c.Value == "rtmc_admin") ||
                        context.User.HasClaim(c => c.Type == "rtmc_getreportdata" || c.Value == "rtmc_getreportdata")
                    )
                );
                
                // Vehicle License Renewal can only perform actions.
                options.AddPolicy("CanRenewVehicleLicense", policy =>
                    policy.RequireAssertion(context =>
                        context.User.HasClaim(c => c.Type == "rtmc_admin" || c.Value == "rtmc_admin") ||
                        context.User.HasClaim(c => c.Type == "rtmc_vehiclerenewal" || c.Value == "rtmc_vehiclerenewal")
                    )
                );
            });
        }

        static void ConfigureVPNCheck(IServiceCollection services)
        {
            services.AddSingleton<RTMCVPNStatus>();
        }

        static void ConfigureAutoMapper(IServiceCollection services)
        {
            services.AddAutoMapper(typeof(Startup));
        }

        static void ConfigureIntegrations(IServiceCollection services)
        {
            services.AddHttpContextAccessor();
            services.AddScoped<RTMCVehicleInformationService>();
            services.AddScoped<RTMCVehicleDetailedInformationService>();
            services.AddScoped<RTMCVehicleOwnerVerificationService>();
            services.AddScoped<RTMCDriverService>();
            services.AddScoped<RTMCVehicleOwnerRegistrationService>();
            services.AddScoped<RTMCOwnershipHistoryService>();
            services.AddScoped<RTMCSettingsService>();
            services.AddScoped<RTMCControlNumberVerificationService>();
            services.AddScoped<RTMCLicenceFeeCalculatorService>();
            services.AddScoped<RTMCOnlineNCOService>();
            services.AddScoped<RTMCVehicleLicenseRenewalService>();

            services.AddScoped<ArchivedInformationService>();
            services.AddScoped<ArchivedVehicleHistoryService>();
            services.AddScoped<ArchivedOwnerVehicleHistoryService>();
            services.AddScoped<SharedServices>();
            services.AddScoped<NatisSharedServices>();

        }

        static void ConfigureApplicationInsights(IServiceCollection services)
        {
            services.AddTransient<RequestBodyLoggingMiddleware>();
            services.AddTransient<ResponseBodyLoggingMiddleware>();
        }

        private void ConfigureMassTransit(IServiceCollection services)
        {
            services.AddMassTransit(busConfigurator =>
            {
                busConfigurator.SetKebabCaseEndpointNameFormatter();

                busConfigurator.UsingAzureServiceBus((context, cfg) =>
                {
                    cfg.Host(Configuration.GetValue<string>("ConnectionStrings:ServiceBusses:WeBuyCars"));

                    cfg.UseMessageRetry(retryConfigurator =>
                    {
                        retryConfigurator.Exponential(30, TimeSpan.FromMilliseconds(200),
                            TimeSpan.FromHours(1),
                            TimeSpan.FromMilliseconds(200));
                    });

                    cfg.ConfigureEndpoints(context);
                });
            });

        }

        static void ConfigureNatisIntegrationServices(IServiceCollection services)
        {
            services.AddScoped<INatisIntegrationService, NatisIntegrationService>();
            services.AddScoped<IDriverIntegrationService, DriverIntegrationService>();
            services.AddScoped<IVehicleIntegrationService, VehicleIntegrationService>();
            services.AddScoped<IVehicleDetailIntegrationService, VehicleDetailIntegrationService>();

            services.AddScoped<IVehicleOwnerVerificationIntegrationService, VehicleOwnerVerificationIntegrationService>();
            services.AddScoped<IVehicleOwnerRegistrationIntegrationService, VehicleOwnerRegistrationIntegrationService>();
            services.AddScoped<IOnlineNCOIntegrationService, OnlineNCOIntegrationService>();

            services.AddScoped<ITestIntegration, TestIntegration>();

            services.AddScoped<INatisWRIntegrationService, NatisWRIntegrationService>();
        }

        static void ConfigureRepositories(IServiceCollection services)
        {
            services.AddScoped<IRTMCRequestRepository, RTMCRequestRepository>();
            services.AddScoped<IRTMCResponseRepository, RTMCResponseRepository>();
            services.AddScoped<IArchiveRequestRepository, ArchiveRequestRepository>();
            services.AddScoped<IArchiveResponseRepository, ArchiveResponseRepository>();
            services.AddScoped<IRTMCVehicleDetailRepository, RTMCVehicleDetailRepository>();
            services.AddScoped<IRTMCVehicleOwnerVerificationDetailRepository, RTMCVehicleOwnerVerificationDetailRepository>();
            services.AddScoped<IRTMCDriverInformationDetailRepository, RTMCDriverInformationDetailRepository>();
            services.AddScoped<IRTMCSettingsRepository, RTMCSettingsRepository>();
            services.AddScoped<IRTMCVehicleOwnerRegistrationDetailRepository, RTMCVehicleOwnerRegistrationDetailRepository>();
            services.AddScoped<IRTMCVehicleOwnerOnlineNCORepository, RTMCVehicleOwnerOnlineNCORepository>();
            services.AddScoped<IRTMCOwnershipHistoryDetailRepository, RTMCOwnershipHistoryDetailRepository>();
            services.AddScoped<IReportRepository, ReportRepository>();
            services.AddScoped<IRTMCVehicleLicenceRenewalRepository, RTMCVehicleLicenceRenewalRepository>();
            services.AddScoped<IRTMCVehicleLicenceRenewalVehicleRepository, RTMCVehicleLicenceRenewalVehicleRepository>();
            services.AddScoped<IRTMCVehicleLicenceRenewalOwnerRepository, RTMCVehicleLicenceRenewalOwnerRepository>();
            services.AddScoped<IRTMCVehicleLicenceRenewalAddressRepository, RTMCVehicleLicenceRenewalAddressRepository>();
            services.AddScoped<IRTMCVehicleLicenceRenewalContactPersonRepository, RTMCVehicleLicenceRenewalContactPersonRepository>();

        }

        void ConfigureServiceExtensions(IServiceCollection services)
        {
            services.AddServiceExtensions(Configuration);
        }

        void ConfigureEnatis(IServiceCollection services)
        {
            services.AddEnatis(Configuration, Environment);
        }

        static void ConfigureEncryptionServices(IServiceCollection services)
        {
            services.AddScoped<IEncryptionService, EncryptionService>();
            services.AddScoped<IDecryptionService, DecryptionService>();
            services.AddScoped<IRSACryptoService, RSAEncryptDecryptService>();
            services.AddScoped<IAesEncryptionDecryptionService, AesEncryptionDecryptionService>();
        }

        #endregion

    }
}





