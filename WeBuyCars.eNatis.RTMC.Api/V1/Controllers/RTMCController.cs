using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WeBuyCars.eNatis.RTMC.Api.Configurations;
using WeBuyCars.eNatis.RTMC.Api.V1.Models;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.ControlNumberVerificationRequest;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.ControlNumberVerificationResponse;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.VehicleLicenceRenewal.CompleteVehicleLicenceRenewal;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.VehicleLicenceRenewal.GetVehiclesAndLicenceExpiryDates;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.VehicleLicenceRenewal.RenewVehicleLicence;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.VehicleLicenceRenewal.VehiclesLicenceRenewalQuotation;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.Network;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.RTMC;
using WeBuyCars.eNatis.RTMC.Core.Exceptions;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Extensions;
using InfrastructureException = WeBuyCars.Core.Exceptions.InfrastructureException;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Controllers
{
    [ApiController]
    [Produces("application/json")]
    [Route("v{version:apiVersion}/[controller]")]
    //[AllowAnonymous]
    [Authorize] 
    public class RTMCController : ControllerBase
    {
        readonly RTMCVehicleInformationService _rTMCVehicleInformationService;
        readonly RTMCVehicleDetailedInformationService _rTMCVehicleDetailedInformationService;
        readonly RTMCVehicleOwnerVerificationService _rtmcVehicleOwnerVerificationService;
        readonly RTMCDriverService _rTMCDriverService;
        readonly RTMCVehicleOwnerRegistrationService _rTMCVehicleOwnerRegistrationService;
        readonly RTMCOnlineNCOService _rTMCOnlineNCOService;        
        readonly RTMCOwnershipHistoryService _rTMCOwnershipHistoryService;
        readonly RTMCControlNumberVerificationService _rTMCControlNumberVerificationService;
        readonly RTMCLicenceFeeCalculatorService _rTMCLicenceFeeCalculatorService;
        readonly RTMCVPNStatus _rTMCVPNStatus;
        private readonly RTMCVehicleLicenseRenewalService _rtmcVehicleLicenseRenewalService;
        readonly EnableEndPointOptions _enableEndPointOptions;
        readonly ServerEnvironmentOptions _serverEnvironmentOptions;
        readonly ILogger<RTMCController> _logger;
        private readonly ILogger _loggerFactory;
        private readonly IWebHostEnvironment _environment;
        private readonly string _environmentName;

        #region Constructors
        public RTMCController(
            RTMCVehicleInformationService rTMCVehicleInformationService,
            RTMCVehicleDetailedInformationService rTMCVehicleDetailedInformationService,
            RTMCVehicleOwnerVerificationService rtmcVehicleOwnerVerificationService,
            RTMCDriverService rTMCDriverService,
            RTMCVehicleOwnerRegistrationService rTMCVehicleOwnerRegistrationService,
            RTMCOwnershipHistoryService rTMCOwnershipHistoryService,
            RTMCOnlineNCOService rTMCOnlineNCOService,
            ILogger<RTMCController> logger,
            ILoggerFactory loggerFactory,
            RTMCVPNStatus rTMCVPNStatus,
            RTMCControlNumberVerificationService rTMCControlNumberVerificationService,
            RTMCLicenceFeeCalculatorService rTMCLicenceFeeCalculatorService,
            RTMCVehicleLicenseRenewalService rtmcVehicleLicenseRenewalService,
            IOptionsMonitor<EnableEndPointOptions> enableEndPointOptions,
            IOptionsMonitor<ServerEnvironmentOptions> serverEnvironmentOptions,
            IWebHostEnvironment environment
            )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _rTMCVehicleInformationService = rTMCVehicleInformationService ?? throw new System.ArgumentNullException(nameof(rTMCVehicleInformationService));
            _rTMCVehicleDetailedInformationService = rTMCVehicleDetailedInformationService ?? throw new System.ArgumentNullException(nameof(rTMCVehicleDetailedInformationService));
            _rtmcVehicleOwnerVerificationService = rtmcVehicleOwnerVerificationService ?? throw new System.ArgumentNullException(nameof(rtmcVehicleOwnerVerificationService));
            _rTMCDriverService = rTMCDriverService ?? throw new System.ArgumentNullException(nameof(rTMCDriverService));
            _rTMCVehicleOwnerRegistrationService = rTMCVehicleOwnerRegistrationService ?? throw new System.ArgumentNullException(nameof(rTMCVehicleOwnerRegistrationService));
            _rTMCOnlineNCOService = rTMCOnlineNCOService ?? throw new System.ArgumentNullException(nameof(rTMCOnlineNCOService));
            _rTMCOwnershipHistoryService = rTMCOwnershipHistoryService ?? throw new System.ArgumentNullException(nameof(rTMCOwnershipHistoryService));
            _rTMCVPNStatus = rTMCVPNStatus ?? throw new ArgumentNullException(nameof(rTMCVPNStatus));
            _rTMCControlNumberVerificationService = rTMCControlNumberVerificationService ?? throw new ArgumentNullException(nameof(rTMCControlNumberVerificationService));
            _rTMCLicenceFeeCalculatorService = rTMCLicenceFeeCalculatorService ?? throw new ArgumentNullException(nameof(rTMCLicenceFeeCalculatorService));
            _rtmcVehicleLicenseRenewalService = rtmcVehicleLicenseRenewalService ?? throw new ArgumentNullException(nameof(rtmcVehicleLicenseRenewalService));
            _enableEndPointOptions = enableEndPointOptions?.CurrentValue ?? throw new ArgumentNullException(nameof(enableEndPointOptions));
            _serverEnvironmentOptions = serverEnvironmentOptions?.CurrentValue ?? throw new ArgumentNullException(nameof(serverEnvironmentOptions));
            this._loggerFactory = loggerFactory.CreateLogger<RTMCController>();
            _environment = environment ?? throw new ArgumentNullException(nameof(environment));

            _environmentName = _environment.EnvironmentName;
        }
        #endregion

        #region Public Methods

        /// <summary>
        /// Retrieve RTMC Vehicle Information
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///POST /api/v1/RTMC/GetVehicle
        ///{
        ///  "vin": "string",
        ///  "registerNumber": "string",
        ///  "engineNumber": "string",
        ///  "licenceNumber": "string",
        ///  "user": "string",
        ///  "workStation": "string",
        ///  "locality": "string",
        ///  "networkAddress": "string"
        ///}
        ///
        /// </remarks>
        /// <returns></returns>
        /// <response code="201">The request was successful.</response>
        /// <response code="400">Request validation exception.</response>
        /// <response code="401">Unauthorized. Invalid token.</response>
        /// <response code="403">Forbidden. Insufficient scope or claim.</response>
        /// <response code="500">A server error occurred.</response>
        [HttpPost("GetVehicle")]
        [ProducesResponseType(typeof(VehicleDetailResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status503ServiceUnavailable)]
        [Authorize(Policy = "GetVehicle")]
        public async Task<ActionResult> GetVehicle(VehicleDetailRequest vehicleRequest)
        {
            try
            {
                if(_enableEndPointOptions.EnabledGetVehicle)
                {
                    if (_rTMCVPNStatus.VPNOnline)
                    {
                        vehicleRequest.NetworkAddress = IPCheck(vehicleRequest.NetworkAddress);

                        vehicleRequest.MessageId = Guid.NewGuid();

                        _logger.LogWarning("GUID : " + vehicleRequest.MessageId + " : Environment : " + _serverEnvironmentOptions.ServerName + " | Server Type : " + _serverEnvironmentOptions.ServerType + " | RTMCController : GetVehicle | vehicleRequest : " + vehicleRequest.ToString());


                        return Ok(await _rTMCVehicleInformationService.GetVehicleAsync(vehicleRequest, _environmentName));
                    }else
                    {
                        return StatusCode(StatusCodes.Status503ServiceUnavailable, new VehicleDetailResponse(){ BaseResponse = DefaultVPNDownResponse()});
                    }
                }else
                {
                    _logger.LogError("Environment : " + _serverEnvironmentOptions.ServerName + " | Server Type : " + _serverEnvironmentOptions.ServerType + " | RTMCController : GetVehicle | The following request was made while the Endpoint was flagged to be Disabled :" + vehicleRequest.ToString());
                    return StatusCode(StatusCodes.Status503ServiceUnavailable, new VehicleDetailResponse(){ BaseResponse = DefaultFailedResponse("Get Vehicle End Point have been disabled in AppSettings")});
                }
            }
            catch (DomainException ex)
            {
                 _logger.LogError("Environment : " + _serverEnvironmentOptions.ServerName + " | Server Type : " + _serverEnvironmentOptions.ServerType + " | RTMCController : Error : Exception in GetVehicle Controller : Exception :" + ex.ToString());
                return StatusCode(StatusCodes.Status503ServiceUnavailable, new VehicleDetailResponse(){ BaseResponse = DefaultFailedResponse("Failed to Retrieve Vehicle Information")});
                
            }

        }

        /// <summary>
        /// Retrieve RTMC Vehicle Detailed Information
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///POST /api/v1/RTMC/GetVehicleDetailedInformation
        ///{
        ///  "vin": "string",
        ///  "registerNumber": "string",
        ///  "engineNumber": "string",
        ///  "licenceNumber": "string",
        ///  "user": "string",
        ///  "workStation": "string",
        ///  "locality": "string",
        ///  "networkAddress": "string"
        ///}
        ///
        /// </remarks>
        /// <returns></returns>
        /// <response code="201">The request was successful.</response>
        /// <response code="400">Request validation exception.</response>
        /// <response code="401">Unauthorized. Invalid token.</response>
        /// <response code="403">Forbidden. Insufficient scope or claim.</response>
        /// <response code="500">A server error occurred.</response>
        [HttpPost("GetVehicleDetailedInformation")]
        [ProducesResponseType(typeof(VehicleDetailResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status503ServiceUnavailable)]
        [Authorize(Policy = "GetVehicle")]
        public async Task<ActionResult> GetVehicleDetailedInformation(VehicleDetailRequest vehicleRequest)
        {
            try
            {
                
                if (_enableEndPointOptions.EnabledGetVehicleDetailedInformation)
                {
                    if (_rTMCVPNStatus.VPNOnline)
                    {
                        vehicleRequest.NetworkAddress = IPCheck(vehicleRequest.NetworkAddress);

                        vehicleRequest.MessageId = Guid.NewGuid();

                        _logger.LogWarning("GUID : " + vehicleRequest.MessageId + " : Environment : " + _serverEnvironmentOptions.ServerName + " | Server Type : " + _serverEnvironmentOptions.ServerType + " | RTMCController : GetVehicleDetailedInformation | vehicleRequest : " + vehicleRequest.ToString());

                        return Ok(await _rTMCVehicleDetailedInformationService.GetVehicleDetailInformationAsync(vehicleRequest, _environmentName));

                    }
                    else
                    {
                        return StatusCode(StatusCodes.Status503ServiceUnavailable, new VehicleDetailResponse() { BaseResponse = DefaultVPNDownResponse() });
                    }
                }
                else
                {
                    _logger.LogError("Environment : " + _serverEnvironmentOptions.ServerName + " | Server Type : " + _serverEnvironmentOptions.ServerType + " | RTMCController : GetVehicleDetailedInformation | The following request was made while the Endpoint was flagged to be Disabled :" + vehicleRequest.ToString());
                    return StatusCode(StatusCodes.Status503ServiceUnavailable, new VehicleDetailResponse() { BaseResponse = DefaultFailedResponse("Get Vehicle Detail End Point have been disabled in AppSettings") });
                }
            }
            catch (DomainException ex)
            {
                 _logger.LogError("Environment : " + _serverEnvironmentOptions.ServerName + " | Server Type : " + _serverEnvironmentOptions.ServerType + " | RTMCController : Error : Exception in GetVehicleDetailedInformation Controller : Exception :" + ex.ToString());
                return StatusCode(StatusCodes.Status503ServiceUnavailable, new VehicleDetailResponse(){ BaseResponse = DefaultFailedResponse("Failed to Retrieve Detailed Vehicle Information")});
            }
            catch(Exception ex)
            {
                _logger.LogError(ex,$"Environment : { _serverEnvironmentOptions.ServerName} | Server Type : {_serverEnvironmentOptions.ServerType} | RTMCController method GetVehicleDetailedInformation | {vehicleRequest.ToString()}");
                return StatusCode(StatusCodes.Status409Conflict, new VehicleDetailResponse(){ BaseResponse = DefaultFailedResponse($"Failed to Retrieve Detailed Vehicle Information: {ex.Message}")});
            }
        }

        /// <summary>
        /// Confirm Title or Owner Information
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///POST /api/v1/RTMC/GetOwnerTitleHolderConfirmation/
        ///{
        ///  "documentTypeCode": "string",
        ///  "documentNumber": "string",
        ///  "vin": "string",
        ///  "registerNumber": "string",
        ///  "licenseNumber": "string",
        ///  "user": "string",
        ///  "workStation": "string",
        ///  "locality": "string",
        ///  "networkAddress": "string"
        ///}
        ///
        /// </remarks>
        /// <returns></returns>
        /// <response code="201">The request was successful.</response>
        /// <response code="400">Request validation exception.</response>
        /// <response code="401">Unauthorized. Invalid token.</response>
        /// <response code="403">Forbidden. Insufficient scope or claim.</response>
        /// <response code="500">A server error occurred.</response>
        [HttpPost("GetOwnerTitleHolderConfirmation")]
        [ProducesResponseType(typeof(VehicleOwnerVerificationResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status503ServiceUnavailable)]
        [Authorize(Policy = "CanVerifyVehicleOwner")]
        public async Task<ActionResult> GetOwnerTitleHolderConfirmation(VehicleOwnerVerificationRequest ownerConfirmationRequest)
        {

            try
            {
                if(_enableEndPointOptions.EnabledGetOwnerTitleHolderConfirmation){
                    
                    if (_rTMCVPNStatus.VPNOnline)
                    {
                        ownerConfirmationRequest.NetworkAddress = IPCheck(ownerConfirmationRequest.NetworkAddress);

                        ownerConfirmationRequest.MessageId = Guid.NewGuid();

                        _logger.LogWarning("GUID : " + ownerConfirmationRequest.MessageId + " : Environment : " + _serverEnvironmentOptions.ServerName + " | Server Type : " + _serverEnvironmentOptions.ServerType + " | RTMCController : GetOwnerTitleHolderConfirmation | ownerConfirmationRequest : " + ownerConfirmationRequest.ToString());

                        return Ok(await _rtmcVehicleOwnerVerificationService.GetOwnerTitleHolderConfirmationAsync(ownerConfirmationRequest, _environmentName));
                    }else
                    {
                        return StatusCode(StatusCodes.Status503ServiceUnavailable, new VehicleOwnerVerificationResponse(){ BaseResponse = DefaultVPNDownResponse()});
                    }
                }else
                {
                     _logger.LogError("Environment : " + _serverEnvironmentOptions.ServerName + " | Server Type : " + _serverEnvironmentOptions.ServerType + " | RTMCController : GetOwnerTitleHolderConfirmation | The following request was made while the Endpoint was flagged to be Disabled :" + ownerConfirmationRequest.ToString());
                    return StatusCode(StatusCodes.Status503ServiceUnavailable, new VehicleOwnerVerificationResponse(){ BaseResponse = DefaultFailedResponse("Get Owner Title Holder Confirmation End Point have been disabled in AppSettings")});
                }
            }
            catch (DomainException ex)
            {
                 _logger.LogError("Environment : " + _serverEnvironmentOptions.ServerName + " | Server Type : " + _serverEnvironmentOptions.ServerType + " | RTMCController : Error : Exception in GetOwnerTitleHolderConfirmation Controller : Exception :" + ex.ToString());
                return StatusCode(StatusCodes.Status503ServiceUnavailable, new VehicleOwnerVerificationResponse(){ BaseResponse = DefaultFailedResponse("Failed to Retrieve Owner Titleholder Information")});
            }

        }

        /// <summary>
        /// Driver Information
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///POST /api/v1/RTMC/GetDriver/
        ///{
        ///  "documentTypeCode": "string",
        ///  "documentNumber": "string",
        ///  "user": "string",
        ///  "workStation": "string",
        ///  "locality": "string",
        ///  "networkAddress": "string"
        ///}
        ///
        /// </remarks>
        /// <returns></returns>
        /// <response code="201">The request was successful.</response>
        /// <response code="400">Request validation exception.</response>
        /// <response code="401">Unauthorized. Invalid token.</response>
        /// <response code="403">Forbidden. Insufficient scope or claim.</response>
        /// <response code="500">A server error occurred.</response>
        [HttpPost("GetDriver")]
        [ProducesResponseType(typeof(DriverInformationResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status503ServiceUnavailable)]
        [Authorize(Policy = "GetDriver")]
        public async Task<ActionResult> GetDriver(DriverInformationRequest driverInformationRequest)
        {
            try
            {
                if(_enableEndPointOptions.EnabledGetDriver)
                {
                    if (_rTMCVPNStatus.VPNOnline)
                    {
                        driverInformationRequest.NetworkAddress = IPCheck(driverInformationRequest.NetworkAddress);

                        driverInformationRequest.MessageId = Guid.NewGuid();

                        _logger.LogWarning("GUID : " + driverInformationRequest.MessageId + " : Environment : " + _serverEnvironmentOptions.ServerName + " | Server Type : " + _serverEnvironmentOptions.ServerType + " | RTMCController : GetDriver | driverInformationRequest : " + driverInformationRequest.ToString());

                        return Ok(await _rTMCDriverService.GetDriverInformationAsync(driverInformationRequest, _environmentName));
                    }else
                    {
                        return StatusCode(StatusCodes.Status503ServiceUnavailable, new DriverInformationResponse(){ BaseResponse = DefaultVPNDownResponse()});
                    }
                }else
                {
                     _logger.LogError("Environment : " + _serverEnvironmentOptions.ServerName + " | Server Type : " + _serverEnvironmentOptions.ServerType + " | RTMCController : GetDriver | The following request was made while the Endpoint was flagged to be Disabled :" + driverInformationRequest.ToString());
                    return StatusCode(StatusCodes.Status503ServiceUnavailable, new DriverInformationResponse(){ BaseResponse = DefaultFailedResponse("Get Driver End Point have been disabled in AppSettings")});
                }
            }
            catch (DomainException ex)
            {
                 _logger.LogError("Environment : " + _serverEnvironmentOptions.ServerName + " | Server Type : " + _serverEnvironmentOptions.ServerType + " | RTMCController : Error : Exception in GetDriver Controller : Exception :" + ex.ToString());
                return StatusCode(StatusCodes.Status503ServiceUnavailable, new DriverInformationResponse(){ BaseResponse = DefaultFailedResponse("Failed to Retrieve Driver Information")});
            }
        }

        /// <summary>
        /// Ownership History
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///POST /api/v1/RTMC/OwnershipHistory/
        ///{
        ///  "documentTypeCode": "string",
        ///  "documentNumber": "string",
        ///  "user": "string",
        ///  "workStation": "string",
        ///  "locality": "string",
        ///  "networkAddress": "string"
        ///}
        ///
        /// </remarks>
        /// <returns></returns>
        /// <response code="201">The request was successful.</response>
        /// <response code="400">Request validation exception.</response>
        /// <response code="401">Unauthorized. Invalid token.</response>
        /// <response code="403">Forbidden. Insufficient scope or claim.</response>
        /// <response code="500">A server error occurred.</response>
        [HttpPost("VehicleOwnershipHistory")]
        [ProducesResponseType(typeof(OwnershipHistoryDataResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status503ServiceUnavailable)]
        [Authorize(Policy = "CanVerifyVehicleOwner")]
        public async Task<ActionResult> VehicleOwnershipHistory(OwnershipHistoryDataRequest ownershipHistoryRequest)
        {
            try
            {
                if(_enableEndPointOptions.EnabledGetAllOwners){
                    
                    if (_rTMCVPNStatus.VPNOnline)
                    {
                        ownershipHistoryRequest.NetworkAddress = IPCheck(ownershipHistoryRequest.NetworkAddress);
                        _logger.LogWarning("Environment : " + _serverEnvironmentOptions.ServerName + " | Server Type : " + _serverEnvironmentOptions.ServerType + " | RTMCController : OwnershipHistory | ownershipHistoryRequest : " + ownershipHistoryRequest.ToString());

                        return Ok(await _rTMCOwnershipHistoryService.GetVehicleOwnershipHistoryAsync(ownershipHistoryRequest, _environmentName));
                    }else
                    {
                        return StatusCode(StatusCodes.Status503ServiceUnavailable, new OwnershipHistoryDataResponse(){ });
                    }
                }else
                {
                     _logger.LogError("Environment : " + _serverEnvironmentOptions.ServerName + " | Server Type : " + _serverEnvironmentOptions.ServerType + " | RTMCController : VehicleOwnershipHistory | The following request was made while the Endpoint was flagged to be Disabled :" + ownershipHistoryRequest.ToString());
                    return StatusCode(StatusCodes.Status503ServiceUnavailable, new OwnershipHistoryDataResponse(){ BaseResponse = DefaultFailedResponse("Vehicle Ownership History End Point have been disabled in AppSettings")});
                }
            }
            catch (DomainException ex)
            {
                 _logger.LogError("Environment : " + _serverEnvironmentOptions.ServerName + " | Server Type : " + _serverEnvironmentOptions.ServerType + " | RTMCController : Error : Exception in OwnershipHistory Controller : Exception :" + ex.ToString());
                return StatusCode(StatusCodes.Status503ServiceUnavailable, new OwnershipHistoryDataResponse(){ BaseResponse = DefaultFailedResponse("Failed to Retrieve Ownership History Information")});
            }
        }


        /// <summary>
        /// Ownership History Verification
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///POST /api/v1/RTMC/OwnershipHistoryVerification/
        ///{
        ///  "documentTypeCode": "string",
        ///  "documentNumber": "string",
        ///  "user": "string",
        ///  "workStation": "string",
        ///  "locality": "string",
        ///  "networkAddress": "string"
        ///}
        ///
        /// </remarks>
        /// <returns></returns>
        /// <response code="201">The request was successful.</response>
        /// <response code="400">Request validation exception.</response>
        /// <response code="401">Unauthorized. Invalid token.</response>
        /// <response code="403">Forbidden. Insufficient scope or claim.</response>
        /// <response code="500">A server error occurred.</response>
        [HttpPost("VehicleOwnershipHistoryVerification")]
        [ProducesResponseType(typeof(OwnershipHistoryVerificationResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status503ServiceUnavailable)]
        [Authorize(Policy = "CanVerifyVehicleOwner")]
        public async Task<ActionResult> VehicleOwnershipHistoryVerification(OwnershipHistoryVerificationRequest ownershipHistoryVerificationRequest)
        {
            try
            {
                if(_enableEndPointOptions.EnabledGetAllOwners)
                {
                    
                    if (_rTMCVPNStatus.VPNOnline)
                    {
                        ownershipHistoryVerificationRequest.NetworkAddress = IPCheck(ownershipHistoryVerificationRequest.NetworkAddress);
                        _logger.LogWarning("Environment : " + _serverEnvironmentOptions.ServerName + " | Server Type : " + _serverEnvironmentOptions.ServerType + " | RTMCController : OwnershipHistoryVerification | ownershipHistoryRequest : " + ownershipHistoryVerificationRequest.ToString());

                        return Ok(await _rTMCOwnershipHistoryService.GetVehicleOwnershipHistoryVerificationAsync(ownershipHistoryVerificationRequest, _environmentName));
                    }else
                    {
                        return StatusCode(StatusCodes.Status503ServiceUnavailable, new OwnershipHistoryDataResponse(){ });
                    }
                }else
                {
                     _logger.LogError("Environment : " + _serverEnvironmentOptions.ServerName + " | Server Type : " + _serverEnvironmentOptions.ServerType + " | RTMCController : VehicleOwnershipHistoryVerification | The following request was made while the Endpoint was flagged to be Disabled :" + ownershipHistoryVerificationRequest.ToString());
                    return StatusCode(StatusCodes.Status503ServiceUnavailable, new OwnershipHistoryDataResponse(){ BaseResponse = DefaultFailedResponse("Vehicle Ownership History End Point have been disabled in AppSettings")});
                }
            }
            catch (DomainException ex)
            {
                 _logger.LogError("Environment : " + _serverEnvironmentOptions.ServerName + " | Server Type : " + _serverEnvironmentOptions.ServerType + " | RTMCController : Error : Exception in OwnershipHistoryVerification Controller : Exception :" + ex.ToString());
                return StatusCode(StatusCodes.Status503ServiceUnavailable, new OwnershipHistoryDataResponse(){ BaseResponse = DefaultFailedResponse("Failed to Retrieve Ownership Verification History Information")});
            }
        }

        /// <summary>
        /// Control Number Verification
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///POST /api/v1/RTMC/ControlNumberVerification/
        ///{
        ///  "documentTypeCode": "string",
        ///  "documentNumber": "string",
        ///  "user": "string",
        ///  "workStation": "string",
        ///  "locality": "string",
        ///  "networkAddress": "string"
        ///}
        ///
        /// </remarks>
        /// <returns></returns>
        /// <response code="201">The request was successful.</response>
        /// <response code="400">Request validation exception.</response>
        /// <response code="401">Unauthorized. Invalid token.</response>
        /// <response code="403">Forbidden. Insufficient scope or claim.</response>
        /// <response code="500">A server error occurred.</response>
        [HttpPost("ControlNumberVerification")]
        [ProducesResponseType(typeof(ControlNumberVerificationResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status503ServiceUnavailable)]
        [Authorize(Policy = "CanVerifyVehicleOwner")]
        public async Task<ActionResult> ControlNumberVerification(ControlNumberVerificationRequest controlNumberVerificationRequest)
        {
            try
            {
                if(_enableEndPointOptions.EnabledControlNumberVerification)
                {
                    if (_rTMCVPNStatus.VPNOnline)
                    {
                        controlNumberVerificationRequest.NetworkAddress = IPCheck(controlNumberVerificationRequest.NetworkAddress);
                        _logger.LogWarning("Environment : " + _serverEnvironmentOptions.ServerName + " | Server Type : " + _serverEnvironmentOptions.ServerType + " | RTMCController : ControlNumberVerification | controlNumberVerificationRequest : " + controlNumberVerificationRequest.ToString());

                        return Ok(await _rTMCControlNumberVerificationService.GetControlNumberVerificationAsync(controlNumberVerificationRequest, _environmentName));
                    }else
                    {
                        return StatusCode(StatusCodes.Status503ServiceUnavailable, new ControlNumberVerificationResponse(){ });
                    }
                }else
                {
                     _logger.LogError("Environment : " + _serverEnvironmentOptions.ServerName + " | Server Type : " + _serverEnvironmentOptions.ServerType + " | RTMCController : ControlNumberVerification | The following request was made while the Endpoint was flagged to be Disabled :" + controlNumberVerificationRequest.ToString());
                    return StatusCode(StatusCodes.Status503ServiceUnavailable, new ControlNumberVerificationResponse(){ BaseResponse = DefaultFailedResponse("Control Number Verification End Point have been disabled in AppSettings")});
                }
            }
            catch (DomainException ex)
            {
                 _logger.LogError("Environment : " + _serverEnvironmentOptions.ServerName + " | Server Type : " + _serverEnvironmentOptions.ServerType + " | RTMCController : Error : Exception in ControlNumberVerification Controller : Exception :" + ex.ToString());
                return StatusCode(StatusCodes.Status503ServiceUnavailable, new ControlNumberVerificationResponse(){ BaseResponse = DefaultFailedResponse("Failed to Retrieve Control Number Verification Information")});
            }
        }


        /// <summary>
        /// Licence Fee Calculator
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///POST /api/v1/RTMC/LicenceFeeCalculator/
        ///{
        ///  "documentTypeCode": "string",
        ///  "documentNumber": "string",
        ///  "user": "string",
        ///  "workStation": "string",
        ///  "locality": "string",
        ///  "networkAddress": "string"
        ///}
        ///
        /// </remarks>
        /// <returns></returns>
        /// <response code="201">The request was successful.</response>
        /// <response code="400">Request validation exception.</response>
        /// <response code="401">Unauthorized. Invalid token.</response>
        /// <response code="403">Forbidden. Insufficient scope or claim.</response>
        /// <response code="500">A server error occurred.</response>
        [HttpPost("LicenceFeeCalculator")]
        [ProducesResponseType(typeof(FeeCalculationResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status503ServiceUnavailable)]
        [Authorize(Policy = "CanCalculateVehicleFees")]
        public async Task<ActionResult> LicenceFeeCalculator(FeeCalculationRequest feeCalculationRequest)
        {
            try
            {
                if(_enableEndPointOptions.EnabledFeeCalculator)
                {
                    if (_rTMCVPNStatus.VPNOnline)
                    {
                        feeCalculationRequest.NetworkAddress = IPCheck(feeCalculationRequest.NetworkAddress);
                        _logger.LogWarning("Environment : " + _serverEnvironmentOptions.ServerName + " | Server Type : " + _serverEnvironmentOptions.ServerType + " | RTMCController : LicenceFeeCalculator | FeeCalculationRequest : " + feeCalculationRequest.ToString());

                        return Ok(await _rTMCLicenceFeeCalculatorService.CalculateVehicleLicenceFeeAsync(feeCalculationRequest, _environmentName));
                    }else
                    {
                        return StatusCode(StatusCodes.Status503ServiceUnavailable, new FeeCalculationResponse(){ });
                    }
                }else
                {
                     _logger.LogError("Environment : " + _serverEnvironmentOptions.ServerName + " | Server Type : " + _serverEnvironmentOptions.ServerType + " | RTMCController : LicenceFeeCalculator | The following request was made while the Endpoint was flagged to be Disabled :" + feeCalculationRequest.ToString());
                    return StatusCode(StatusCodes.Status503ServiceUnavailable, new FeeCalculationResponse(){ BaseResponse = DefaultFailedResponse("Fee Calculation End Point have been disabled in AppSettings")});
                }
            }
            catch (DomainException ex)
            {
                 _logger.LogError("Environment : " + _serverEnvironmentOptions.ServerName + " | Server Type : " + _serverEnvironmentOptions.ServerType + " | RTMCController : Error : Exception in LicenceFeeCalculator Controller : Exception :" + ex.ToString());
                return StatusCode(StatusCodes.Status503ServiceUnavailable, new FeeCalculationResponse(){ BaseResponse = DefaultFailedResponse("Failed to Retrieve Licence Fee Calculator Information")});
            }
        }

        /// <summary>
        /// Vehicle Ownership Registration
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///POST /api/v1/RTMC/VehicleOwnerRegistration/
        ///{
        ///  "documentTypeCode": "string",
        ///  "documentNumber": "string",
        ///  "user": "string",
        ///  "workStation": "string",
        ///  "locality": "string",
        ///  "networkAddress": "string"
        ///}
        ///
        /// </remarks>
        /// <returns></returns>
        /// <response code="201">The request was successful.</response>
        /// <response code="400">Request validation exception.</response>
        /// <response code="401">Unauthorized. Invalid token.</response>
        /// <response code="403">Forbidden. Insufficient scope or claim.</response>
        /// <response code="500">A server error occurred.</response>
        [HttpPost("VehicleOwnerRegistration")]
        [ProducesResponseType(typeof(VehicleOwnerRegistrationResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status503ServiceUnavailable)]
        [Authorize(Policy = "CanRegisterVehicleOwner")]
        [TimeframeAccess("05:00:00", "17:00:00", "This end point is only available between 07:00 and 19:00")]
        public async Task<ActionResult> VehicleOwnerRegistration(VehicleOwnerRegistrationRequest vehicleOwnerRegistrationRequest)
        {
            try
            {

                if(_enableEndPointOptions.EnabledVehicleOwnerRegistration)
                {
                    if (_rTMCVPNStatus.VPNOnline)
                    {
                        vehicleOwnerRegistrationRequest.NetworkAddress = IPCheck(vehicleOwnerRegistrationRequest.NetworkAddress);

                        vehicleOwnerRegistrationRequest.MessageId = Guid.NewGuid();

                        _logger.LogWarning("GUID : " + vehicleOwnerRegistrationRequest.MessageId + " : Environment : " + _serverEnvironmentOptions.ServerName + " | Server Type : " + _serverEnvironmentOptions.ServerType + " | RTMCController : VehicleOwnerRegistration | vehicleOwnerRegistrationRequest : " + vehicleOwnerRegistrationRequest.ToString());

                        return Ok(await _rTMCVehicleOwnerRegistrationService.VehicleOwnerRegistrationAsync(vehicleOwnerRegistrationRequest, _environmentName));
                    }else
                    {
                        return StatusCode(StatusCodes.Status503ServiceUnavailable, new VehicleOwnerRegistrationResponse(){ BaseResponse = DefaultVPNDownResponse()});
                    }
                }else
                {
                     _logger.LogError("Environment : " + _serverEnvironmentOptions.ServerName + " | Server Type : " + _serverEnvironmentOptions.ServerType + " | RTMCController : VehicleOwnerRegistration | The following request was made while the Endpoint was flagged to be Disabled :" + vehicleOwnerRegistrationRequest.ToString());
                    return StatusCode(StatusCodes.Status503ServiceUnavailable, new VehicleOwnerRegistrationResponse(){ BaseResponse = DefaultFailedResponse("Vehicle Registration End Point have been disabled in AppSettings")});
                }
            }
            catch (DomainException ex)
            {
                 _logger.LogError("Environment : " + _serverEnvironmentOptions.ServerName + " | Server Type : " + _serverEnvironmentOptions.ServerType + " | RTMCController : Error : Exception in VehicleOwnerRegistration Controller : Exception :" + ex.ToString());
                return StatusCode(StatusCodes.Status503ServiceUnavailable, new VehicleOwnerRegistrationResponse(){ BaseResponse = DefaultFailedResponse("Failed to Register Vehicle")});
            }
        }


        /// <summary>
        /// Online NCO
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///POST /api/v1/RTMC/OnlineNCO/
        ///{
        ///  "documentTypeCode": "string",
        ///  "documentNumber": "string",
        ///  "user": "string",
        ///  "workStation": "string",
        ///  "locality": "string",
        ///  "networkAddress": "string"
        ///}
        ///
        /// </remarks>
        /// <returns></returns>
        /// <response code="201">The request was successful.</response>
        /// <response code="400">Request validation exception.</response>
        /// <response code="401">Unauthorized. Invalid token.</response>
        /// <response code="403">Forbidden. Insufficient scope or claim.</response>
        /// <response code="500">A server error occurred.</response>
        [HttpPost("OnlineNCO")]
        [ProducesResponseType(typeof(VehicleOwnerOnlineNCOResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status503ServiceUnavailable)]
        [Authorize(Policy = "CanOnlineNCO")]
        [TimeframeAccess("05:00:00", "17:00:00", "This end point is only available between 07:00 and 19:00")]
        public async Task<ActionResult> OnlineNCO(VehicleOwnerOnlineNCORequest onlineNCORequest)
        {
            try
            {

                if(_enableEndPointOptions.EnabledOnlineNCO)
                {
                    if (_rTMCVPNStatus.VPNOnline)
                    {
                        onlineNCORequest.NetworkAddress = IPCheck(onlineNCORequest.NetworkAddress);

                        onlineNCORequest.MessageId = Guid.NewGuid();

                        _logger.LogWarning("GUID : " + onlineNCORequest.MessageId + " : Environment : " + _serverEnvironmentOptions.ServerName + " | Server Type : " + _serverEnvironmentOptions.ServerType + " | RTMCController : OnlineNCO | onlineNCORequest : " + onlineNCORequest.ToString());

                        return Ok(await _rTMCOnlineNCOService.OnlineNCOAsync(onlineNCORequest, _environmentName));
                    }else
                    {
                        return StatusCode(StatusCodes.Status503ServiceUnavailable, new VehicleOwnerOnlineNCOResponse(){ BaseResponse = DefaultVPNDownResponse()});
                    }
                }else
                {
                     _logger.LogError("Environment : " + _serverEnvironmentOptions.ServerName + " | Server Type : " + _serverEnvironmentOptions.ServerType + " | RTMCController : OnlineNCO | The following request was made while the Endpoint was flagged to be Disabled :" + onlineNCORequest.ToString());
                    return StatusCode(StatusCodes.Status503ServiceUnavailable, new VehicleOwnerOnlineNCOResponse(){ BaseResponse = DefaultFailedResponse("Online NCO End Point have been disabled in AppSettings")});
                }
            }
            catch (DomainException ex)
            {
                 _logger.LogError("Environment : " + _serverEnvironmentOptions.ServerName + " | Server Type : " + _serverEnvironmentOptions.ServerType + " | RTMCController : Error : Exception in OnlineNCO Controller : Exception :" + ex.ToString());
                return StatusCode(StatusCodes.Status503ServiceUnavailable, new VehicleOwnerOnlineNCOResponse(){ BaseResponse = DefaultFailedResponse("Failed to Nominate a Change of Owner")});
            }
        }

        /// <summary>
        ///  Get Vehicles And License Expiry Dates
        /// </summary>
        /// <remarks>
        /// Sample Request:
        /// POST /api/v1/RTMC/GetVehiclesDueForRenewal
        /// {
        ///   "identificationType": "02",
        ///   "identificationNumber": "7102230932086"
        /// }
        /// </remarks>
        /// <returns></returns>
        [HttpPost("GetVehiclesDueForRenewal")]
        [ProducesResponseType(typeof(GetVehiclesAndLicenceExpiryDatesResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status412PreconditionFailed)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status503ServiceUnavailable)]
        [Authorize(Policy = "CanRenewVehicleLicense")]
        public async Task<ActionResult> GetVehiclesAndLicenseExpiryDates(GetVehiclesAndLicenceExpiryDatesRequest getVehiclesAndsAndLicenceExpiryDatesDatesRequest)
        {
            try
            {
                if (_enableEndPointOptions.EnabledVehicleLicenseRenewal)
                {
                    // VPN Online Check
                    if (!_rTMCVPNStatus.VPNOnline)
                        return StatusCode(StatusCodes.Status503ServiceUnavailable, new GetVehiclesAndLicenceExpiryDatesResponse());

                    // IP Check
                    getVehiclesAndsAndLicenceExpiryDatesDatesRequest.NetworkAddress = IPCheck(getVehiclesAndsAndLicenceExpiryDatesDatesRequest.NetworkAddress);

                    // Request to RTMC Service
                    return Ok(await _rtmcVehicleLicenseRenewalService.GetVehicleLicenseRenewalsAsync(getVehiclesAndsAndLicenceExpiryDatesDatesRequest));
                }

                _logger.LogError("Vehicle Licence Renewal Feature is not enabled.");
                return StatusCode(StatusCodes.Status503ServiceUnavailable,
                    new GetVehiclesAndLicenceExpiryDatesResponse
                    {
                        BaseResponse = DefaultFailedResponse("Vehicle Licence Renewal Feature is not enabled.")
                    });
            }
            catch (DomainException ex)
            {
                _logger.LogError("GetVehiclesAndLicenceExpiryDates Failed: {Exception}", ex);
                return StatusCode(StatusCodes.Status503ServiceUnavailable,
                    new GetVehiclesAndLicenceExpiryDatesResponse
                    {
                        BaseResponse = DefaultFailedResponse("Failed to Retrieve Vehicles and License Expiry Dates Information")
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError("GetVehiclesAndLicenceExpiryDates Failed: {Exception}", ex);
                throw new InfrastructureException("An error occurred while processing the Get Vehicles And License Expiry Dates request: ", ex);
            }
        }
        
        /// <summary>
        ///  Get Vehicle License Renewal Quotation
        /// </summary>
        /// <remarks>
        /// Sample Request:
        /// POST /api/v1/RTMC/GetVehicleLicenseRenewalQuotation
        /// {
        ///   "identificationType": "02",
        ///   "identificationNumber": "7102230932086",
        ///   "vehicles": [
        ///     {
        ///       "vinOrChassis": "ABJK67HNR4E064202",
        ///       "licenseNumber": "CA641839",
        ///       "registerNumber": "CZS798S"
        ///     },
        ///     {
        ///       "vinOrChassis": "CDEF123456GH789012",
        ///       "licenseNumber": "CA123456",
        ///       "registerNumber": "XYZ123A"
        ///     }
        ///   ]
        /// }
        /// </remarks>
        /// <returns></returns>
        [HttpPost("GetVehiclesLicenceRenewalQuotationRequest")]
        [ProducesResponseType(typeof(GetVehiclesAndLicenceExpiryDatesResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status412PreconditionFailed)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status503ServiceUnavailable)]
        [Authorize(Policy = "CanRenewVehicleLicense")]
        public async Task<ActionResult> GetVehiclesLicenceRenewalQuotationRequest(VehiclesLicenceRenewalQuotationRequest vehiclesLicenceRenewalQuotationRequest)
        {
            try
            {
                if (_enableEndPointOptions.EnabledVehicleLicenseRenewal)
                {
                    // VPN Online Check
                    if (!_rTMCVPNStatus.VPNOnline)
                        return StatusCode(StatusCodes.Status503ServiceUnavailable, new VehiclesLicenceRenewalQuotationResponse());

                    // IP Check
                    vehiclesLicenceRenewalQuotationRequest.NetworkAddress = IPCheck(vehiclesLicenceRenewalQuotationRequest.NetworkAddress);
                    
                    // Request to RTMC Service
                    return Ok(await _rtmcVehicleLicenseRenewalService.GetVehicleLicenseRenewalQuotationsAsync(vehiclesLicenceRenewalQuotationRequest));
                }

                _logger.LogError("Vehicle Licence Renewal Feature is not enabled.");
                return StatusCode(StatusCodes.Status503ServiceUnavailable,
                    new VehiclesLicenceRenewalQuotationResponse
                    {
                        BaseResponse = DefaultFailedResponse("Vehicle Licence Renewal Feature is not enabled.")
                    });
            }
            catch (DomainException ex)
            {
                _logger.LogError("GetVehiclesLicenceRenewalQuotationRequest Failed: {Exception}", ex);
                return StatusCode(StatusCodes.Status503ServiceUnavailable,
                    new VehiclesLicenceRenewalQuotationResponse
                    {
                        BaseResponse = DefaultFailedResponse("Failed to Retrieve Vehicles Licence Renewal Quotation Information")
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError("GetVehiclesLicenceRenewalQuotationRequest Failed: {Exception}", ex);
               throw new InfrastructureException("An error occurred while processing the request for get vehicle license renewal quotation: ", ex);
            }
        }
        
        /// <summary>
        /// Initiate Vehicle Licence Renewal
        /// </summary>
        /// <remarks>
        /// POST  /api/v1/RTMC/InitiateVehicleLicenceRenewal
        /// {
        ///   "owner": {
        ///     "identificationType": "02",
        ///     "identificationNumber": "710******2086",
        ///     "deliveryAddress": {
        ///       "addressType": "COMPLEX",
        ///       "address1": "01 Crest Estate",
        ///       "address2": "123 Joe's Street",
        ///       "address3": "Clayville",
        ///       "address4": "Johannesburg",
        ///       "address5": "Gauteng",
        ///       "postalCode": "6252"
        ///     },
        ///     "contactPerson": {
        ///       "contactName": "Thomas Jow",
        ///       "contactNumber": "**********",
        ///       "alternativeContactNumber": "**********"
        ///     }
        ///   },
        ///   "renewalInformation": {
        ///     "totalRenewalAmount": 14794.90,
        ///     "totalDeliveryAmount": 99.00,
        ///     "totalPaidAmount": 14893.90,
        ///     "renewalReference": "550e8400-e29b-41d4-a716-446655440000",
        ///     "renewableVehicles": [
        ///       {
        ///         "vinOrChassis": "ABJK67HNR4E064202",
        ///         "licenseNumber": "CA641839",
        ///         "registerNumber": "CZS798S",
        ///         "amount": 14794.90
        ///       }
        ///     ]
        ///   }
        /// }
        /// </remarks>
        /// <returns></returns>
        [HttpPost("InitiateVehicleLicenceRenewal")]
        [ProducesResponseType(typeof(RenewVehicleLicenceResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status412PreconditionFailed)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status503ServiceUnavailable)]
        [Authorize(Policy = "CanRenewVehicleLicense")]
        public async Task<ActionResult> InitiateVehicleLicenceRenewal(RenewVehicleLicenceRequest renewVehicleLicenceRequest)
        {
            try
            {
                if (_enableEndPointOptions.EnabledVehicleLicenseRenewal)
                {
                    // VPN Online Check
                    if (!_rTMCVPNStatus.VPNOnline)
                        return StatusCode(StatusCodes.Status503ServiceUnavailable, new RenewVehicleLicenceResponse());

                    // IP Check
                    renewVehicleLicenceRequest.NetworkAddress = IPCheck(renewVehicleLicenceRequest.NetworkAddress);

                    // Request to RTMC Service
                    return Ok(await _rtmcVehicleLicenseRenewalService.InitiateVehicleLicenceRenewalAsync(renewVehicleLicenceRequest));
                }

                _logger.LogError("Vehicle Licence Renewal Feature is not enabled.");
                return StatusCode(StatusCodes.Status503ServiceUnavailable,
                    new RenewVehicleLicenceResponse
                    {
                        BaseResponse = DefaultFailedResponse("Vehicle Licence Renewal Feature is not enabled.")
                    });
            }
            catch (DomainException ex)
            {
                _logger.LogError("InitiateVehicleLicenceRenewal Failed: {Exception}", ex);
                return StatusCode(StatusCodes.Status503ServiceUnavailable,
                    new RenewVehicleLicenceResponse
                    {
                        BaseResponse = DefaultFailedResponse("Failed to Initiate Vehicles Licence Renewal")
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError("InitiateVehicleLicenceRenewal Failed: {Exception}", ex);
                throw new InfrastructureException("An error occurred while processing the initiate vehicle licence request: ", ex);
            }
        }
        
        /// <summary>
        /// Complete Vehicle Licence Renewal
        /// </summary>
        /// <remarks>
        /// Sample Request:
        /// POST /api/v1/RTMC/CompleteVehicleLicenceRenewal
        /// {
        ///   "owner": {
        ///     "identificationType": "02",
        ///     "identificationNumber": "7102230932086",
        ///     "deliveryAddress": {
        ///       "addressType": "COMPLEX",
        ///       "address1": "01 Crest Estate",
        ///       "address2": "123 Joe's Street",
        ///       "address3": "Clayville",
        ///       "address4": "Johannesburg",
        ///       "address5": "Gauteng",
        ///       "postalCode": "6252"
        ///     },
        ///     "contactPerson": {
        ///       "contactName": "Thomas Jow",
        ///       "contactNumber": "**********",
        ///       "alternativeContactNumber": "**********"
        ///     }
        ///   },
        ///   "renewalInformation": {
        ///     "totalRenewalAmount": 14794.90,
        ///     "totalDeliveryAmount": 99.00,
        ///     "totalPaidAmount": 14893.90,
        ///     "renewalReference": "550e8400-e29b-41d4-a716-446655440000",
        ///     "renewableVehicles": [
        ///       {
        ///         "vinOrChassis": "ABJK67HNR4E064202",
        ///         "licenseNumber": "CA641839",
        ///         "registerNumber": "CZS798S",
        ///         "amount": 14794.90
        ///       }
        ///     ]
        ///   }
        /// }
        /// </remarks>
        /// <returns></returns>
        [HttpPost("CompleteVehicleLicenceRenewal")]
        [ProducesResponseType(typeof(RenewVehicleLicenceResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status412PreconditionFailed)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status503ServiceUnavailable)]
        [Authorize(Policy = "CanRenewVehicleLicense")]
        public async Task<ActionResult> CompleteVehicleLicenceRenewal(CompleteVehicleLicenceRenewalRequest completeVehicleLicenceRenewalRequest)
        {
            try
            {
                if (_enableEndPointOptions.EnabledVehicleLicenseRenewal)
                {
                    // VPN Online Check
                    if (!_rTMCVPNStatus.VPNOnline)
                        return StatusCode(StatusCodes.Status503ServiceUnavailable, new CompleteVehicleLicenceRenewalResponse());

                    // IP Check
                    completeVehicleLicenceRenewalRequest.NetworkAddress = IPCheck(completeVehicleLicenceRenewalRequest.NetworkAddress);

                    // Request to RTMC Service
                    return Ok(await _rtmcVehicleLicenseRenewalService.CompleteVehicleLicenseRenewalAsync(completeVehicleLicenceRenewalRequest));
                }

                _logger.LogError("Vehicle Licence Renewal Feature is not enabled.");
                return StatusCode(StatusCodes.Status503ServiceUnavailable,
                    new CompleteVehicleLicenceRenewalResponse
                    {
                        BaseResponse = DefaultFailedResponse("Vehicle Licence Renewal Feature is not enabled.")
                    });
            }
            catch (DomainException ex)
            {
                _logger.LogError("Failed to CompleteVehicleLicenceRenewal : {Exception}", ex);
                return StatusCode(StatusCodes.Status503ServiceUnavailable,
                    new CompleteVehicleLicenceRenewalResponse
                    {
                        BaseResponse = DefaultFailedResponse("Failed to Complete Vehicle Licence Renewal")
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to CompleteVehicleLicenceRenewal : {Exception}", ex);
                throw new InfrastructureException("An error occurred while processing the request for complete vehicle license renewal: ", ex);
            }
        }
        
        #endregion

        #region Private Methods

        private string IPCheck(string ip)
        {

            var ipResult = "";
            if (String.IsNullOrEmpty(ip))
            {
                ipResult = this.HttpContext.Request.Host.Host;
            }
            else
            {
                ipResult = ip;
            }

            return ipResult;

        }

        private BaseResponse DefaultVPNDownResponse (){
            BaseResponse baseResponse = new BaseResponse()
                {
                    Successful = false,
                    Message = "RTMC VPN is down",
                    Reference = new Guid()
                };
            return baseResponse;
        }

        private BaseResponse DefaultFailedResponse (string ResponseErrorMessage){
            BaseResponse baseResponse = new BaseResponse()
                {
                    Successful = false,
                    Message = ResponseErrorMessage,
                    Reference = new Guid()
                };
            return baseResponse;
        }


        #endregion

    }
}