using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.VehicleLicenceRenewal.CompleteVehicleLicenceRenewal;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.VehicleLicenceRenewal.GetVehiclesAndLicenceExpiryDates;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.VehicleLicenceRenewal.RenewVehicleLicence;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.VehicleLicenceRenewal.VehiclesLicenceRenewalQuotation;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleLicenceRenewal.CompleteRenewal;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleLicenceRenewal.GetVehiclesQuotation;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleLicenceRenewal.InitiateRenewal;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleLicenceRenewal.GetVehiclesDueForRenewal;
using Vehicle = WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleLicenceRenewal.Vehicle;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Mappers;

public class VehicleLicenceRenewalMapper : Profile
{
    public VehicleLicenceRenewalMapper()
    {
        // Get Vehicles and License Expiry Dates
        CreateMap<RTMCRequest, GetVehiclesAndLicenceExpiryDatesRequest>().ReverseMap();
        
        CreateMap<GetVehiclesAndLicenceExpiryDatesRequest, NatisGetVehiclesAndLicenceExpiryDatesRequest>()
            .ForMember(dest => dest.IdentificationType, opt => opt.MapFrom(src => src.IdentificationTypeCode))
            .ForMember(dest => dest.IdentificationNumber, opt => opt.MapFrom(src => src.IdentificationNumber));

        CreateMap<Vehicle, GetVehiclesAndLicenceExpiryDatesResponse>()
            .ForMember(dest => dest.Vehicles, opt => opt.MapFrom(src =>
                new List<Models.VehicleLicenceRenewal.Shared.Vehicle>
                {
                new()
                {
                    VehicleRegisterNumber = src.VehicleRegisterNumber,
                    LicenceNumber = src.LicenceNumber,
                    EngineNumber = src.EngineNumber,
                    VinOrChassisNumber = src.VinOrChassis,
                    LicenseExpiryDate = src.MvLicExpryD,
                    Roadworthy = src.Roadworthy,
                    Make = src.MvMake,
                    Model = src.MvModel,
                    SeriesName = src.MvSeries,
                    Colour = src.MvColour,
                    Tare = src.Tare
                }
            }));

        CreateMap<IEnumerable<Vehicle>, List<GetVehiclesAndLicenceExpiryDatesResponse>>()
            .ConvertUsing((src, dest, context) => src.Select(item => context.Mapper.Map<GetVehiclesAndLicenceExpiryDatesResponse>(item)).ToList());

        
        // Vehicle License Renewal Quotation
        CreateMap<RTMCRequest, VehiclesLicenceRenewalQuotationRequest>().ReverseMap();
        
        CreateMap<VehiclesLicenceRenewalQuotationRequest, NatisGetVehiclesQuotationRequest>()
            .ForMember(dest => dest.Vehicles, opt => opt.MapFrom(src => src.Vehicles));

        CreateMap<VehiclesQuotationData, RenewVehicleLicenceResponse>();
        
            
        // Initiate Vehicle License Renewal
        CreateMap<RTMCRequest, RenewVehicleLicenceRequest>().ReverseMap();

        CreateMap<RenewVehicleLicenceRequest, NatisInitiateRenewalRequest>()
            .ForMember(dest => dest.Owner, opt => opt.MapFrom(src => src.Owner))
            .ForMember(dest => dest.RenewalInformation, opt => opt.MapFrom(src => src.RenewalInformation));
    
        CreateMap<VehiclesLicenceRenewalQuotationRequest, NatisGetVehiclesQuotationRequest>()
            .ForMember(dest => dest.Vehicles, opt => opt.MapFrom(src => src.Vehicles));
        
        
        // Complete Vehicle License Renewal
        CreateMap<RTMCRequest, CompleteVehicleLicenceRenewalRequest>().ReverseMap();

        CreateMap<CompleteVehicleLicenceRenewalRequest, NatisCompleteRenewalRequest>();
        
        CreateMap<CompleteRenewalData, CompleteVehicleLicenceRenewalResponse>()
            .ForMember(dest => dest.Vehicles, opt => opt.MapFrom(src => src.data.Vehicles));
    }
}

