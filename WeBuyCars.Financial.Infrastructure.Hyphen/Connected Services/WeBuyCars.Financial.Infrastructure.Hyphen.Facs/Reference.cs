//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.ServiceModel;

namespace WeBuyCars.Financial.Infrastructure.Hyphen.Facs
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="https://ws.hyphen.co.za/facs", ConfigurationName="WeBuyCars.Financial.Infrastructure.Hyphen.Facs.WebService_FacsPortType")]
    public interface WebService_FacsPortType
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="https://qaws.hyphen.co.za/webservice/facs/submitTransaction", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(Style=System.ServiceModel.OperationFormatStyle.Rpc, SupportFaults=true, Use=System.ServiceModel.OperationFormatUse.Encoded)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="return")]
        WeBuyCars.Financial.Infrastructure.Hyphen.Facs.WebService_Facs_TransactionResult submitTransaction(string userProfile, string requestToken, WeBuyCars.Financial.Infrastructure.Hyphen.Facs.WebService_Facs_TransactionRequest transactionRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="https://qaws.hyphen.co.za/webservice/facs/submitTransaction", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="return")]
        System.Threading.Tasks.Task<WeBuyCars.Financial.Infrastructure.Hyphen.Facs.WebService_Facs_TransactionResult> submitTransactionAsync(string userProfile, string requestToken, WeBuyCars.Financial.Infrastructure.Hyphen.Facs.WebService_Facs_TransactionRequest transactionRequest);
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.SoapTypeAttribute(Namespace="https://ws.hyphen.co.za/facs")]
    public partial class WebService_Facs_TransactionRequest
    {
        
        private string transactionTypeField;
        
        private string documentTypeField;
        
        private string reference1Field;
        
        private string reference2Field;
        
        private string code1Field;
        
        private string code2Field;
        
        private string amountField;
        
        private processingOption1Type processingOption1Field;
        
        private processingOption2Type processingOption2Field;
        
        private string clientNameField;
        
        private string clientBankAccountNumberField;
        
        private string clientBankAccountTypeField;
        
        private string clientBranchCodeField;
        
        private string bankAccountNumberField;
        
        private string actionDateField;
        
        private fediIndicatorType fediIndicatorField;
        
        private string checkSumField;
        
        /// <remarks/>
        public string transactionType
        {
            get
            {
                return this.transactionTypeField;
            }
            set
            {
                this.transactionTypeField = value;
            }
        }
        
        /// <remarks/>
        public string documentType
        {
            get
            {
                return this.documentTypeField;
            }
            set
            {
                this.documentTypeField = value;
            }
        }
        
        /// <remarks/>
        public string reference1
        {
            get
            {
                return this.reference1Field;
            }
            set
            {
                this.reference1Field = value;
            }
        }
        
        /// <remarks/>
        public string reference2
        {
            get
            {
                return this.reference2Field;
            }
            set
            {
                this.reference2Field = value;
            }
        }
        
        /// <remarks/>
        public string code1
        {
            get
            {
                return this.code1Field;
            }
            set
            {
                this.code1Field = value;
            }
        }
        
        /// <remarks/>
        public string code2
        {
            get
            {
                return this.code2Field;
            }
            set
            {
                this.code2Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.SoapElementAttribute(DataType="integer")]
        public string amount
        {
            get
            {
                return this.amountField;
            }
            set
            {
                this.amountField = value;
            }
        }
        
        /// <remarks/>
        public processingOption1Type processingOption1
        {
            get
            {
                return this.processingOption1Field;
            }
            set
            {
                this.processingOption1Field = value;
            }
        }
        
        /// <remarks/>
        public processingOption2Type processingOption2
        {
            get
            {
                return this.processingOption2Field;
            }
            set
            {
                this.processingOption2Field = value;
            }
        }
        
        /// <remarks/>
        public string clientName
        {
            get
            {
                return this.clientNameField;
            }
            set
            {
                this.clientNameField = value;
            }
        }
        
        /// <remarks/>
        public string clientBankAccountNumber
        {
            get
            {
                return this.clientBankAccountNumberField;
            }
            set
            {
                this.clientBankAccountNumberField = value;
            }
        }
        
        /// <remarks/>
        public string clientBankAccountType
        {
            get
            {
                return this.clientBankAccountTypeField;
            }
            set
            {
                this.clientBankAccountTypeField = value;
            }
        }
        
        /// <remarks/>
        public string clientBranchCode
        {
            get
            {
                return this.clientBranchCodeField;
            }
            set
            {
                this.clientBranchCodeField = value;
            }
        }
        
        /// <remarks/>
        public string bankAccountNumber
        {
            get
            {
                return this.bankAccountNumberField;
            }
            set
            {
                this.bankAccountNumberField = value;
            }
        }
        
        /// <remarks/>
        public string actionDate
        {
            get
            {
                return this.actionDateField;
            }
            set
            {
                this.actionDateField = value;
            }
        }
        
        /// <remarks/>
        public fediIndicatorType fediIndicator
        {
            get
            {
                return this.fediIndicatorField;
            }
            set
            {
                this.fediIndicatorField = value;
            }
        }
        
        /// <remarks/>
        public string checkSum
        {
            get
            {
                return this.checkSumField;
            }
            set
            {
                this.checkSumField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Xml.Serialization.SoapTypeAttribute(Namespace="https://ws.hyphen.co.za/facs")]
    public enum processingOption1Type
    {
        
        /// <remarks/>
        I,
        
        /// <remarks/>
        G,
        
        /// <remarks/>
        V,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Xml.Serialization.SoapTypeAttribute(Namespace="https://ws.hyphen.co.za/facs")]
    public enum processingOption2Type
    {
        
        /// <remarks/>
        S,
        
        /// <remarks/>
        V,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Xml.Serialization.SoapTypeAttribute(Namespace="https://ws.hyphen.co.za/facs")]
    public enum fediIndicatorType
    {
        
        /// <remarks/>
        Y,
        
        /// <remarks/>
        N,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.SoapTypeAttribute(Namespace="https://ws.hyphen.co.za/facs")]
    public partial class WebService_Facs_TransactionResult
    {
        
        private string transactionTypeField;
        
        private string documentTypeField;
        
        private string reference1Field;
        
        private string reference2Field;
        
        private string amountField;
        
        private string actionDateField;
        
        private string requisitionNumberField;
        
        private string documentNumberField;
        
        private string agencyPrefixField;
        
        private string agencyNumberField;
        
        private string depositTypeField;
        
        private string chequeClearanceCodeField;
        
        private string chequeNumberField;
        
        private string bankAccountNumberField;
        
        private string uniqueUserCodeField;
        
        private string errorCodeField;
        
        private string checkSumField;
        
        /// <remarks/>
        public string transactionType
        {
            get
            {
                return this.transactionTypeField;
            }
            set
            {
                this.transactionTypeField = value;
            }
        }
        
        /// <remarks/>
        public string documentType
        {
            get
            {
                return this.documentTypeField;
            }
            set
            {
                this.documentTypeField = value;
            }
        }
        
        /// <remarks/>
        public string reference1
        {
            get
            {
                return this.reference1Field;
            }
            set
            {
                this.reference1Field = value;
            }
        }
        
        /// <remarks/>
        public string reference2
        {
            get
            {
                return this.reference2Field;
            }
            set
            {
                this.reference2Field = value;
            }
        }
        
        /// <remarks/>
        public string amount
        {
            get
            {
                return this.amountField;
            }
            set
            {
                this.amountField = value;
            }
        }
        
        /// <remarks/>
        public string actionDate
        {
            get
            {
                return this.actionDateField;
            }
            set
            {
                this.actionDateField = value;
            }
        }
        
        /// <remarks/>
        public string requisitionNumber
        {
            get
            {
                return this.requisitionNumberField;
            }
            set
            {
                this.requisitionNumberField = value;
            }
        }
        
        /// <remarks/>
        public string documentNumber
        {
            get
            {
                return this.documentNumberField;
            }
            set
            {
                this.documentNumberField = value;
            }
        }
        
        /// <remarks/>
        public string agencyPrefix
        {
            get
            {
                return this.agencyPrefixField;
            }
            set
            {
                this.agencyPrefixField = value;
            }
        }
        
        /// <remarks/>
        public string agencyNumber
        {
            get
            {
                return this.agencyNumberField;
            }
            set
            {
                this.agencyNumberField = value;
            }
        }
        
        /// <remarks/>
        public string depositType
        {
            get
            {
                return this.depositTypeField;
            }
            set
            {
                this.depositTypeField = value;
            }
        }
        
        /// <remarks/>
        public string chequeClearanceCode
        {
            get
            {
                return this.chequeClearanceCodeField;
            }
            set
            {
                this.chequeClearanceCodeField = value;
            }
        }
        
        /// <remarks/>
        public string chequeNumber
        {
            get
            {
                return this.chequeNumberField;
            }
            set
            {
                this.chequeNumberField = value;
            }
        }
        
        /// <remarks/>
        public string bankAccountNumber
        {
            get
            {
                return this.bankAccountNumberField;
            }
            set
            {
                this.bankAccountNumberField = value;
            }
        }
        
        /// <remarks/>
        public string uniqueUserCode
        {
            get
            {
                return this.uniqueUserCodeField;
            }
            set
            {
                this.uniqueUserCodeField = value;
            }
        }
        
        /// <remarks/>
        public string errorCode
        {
            get
            {
                return this.errorCodeField;
            }
            set
            {
                this.errorCodeField = value;
            }
        }
        
        /// <remarks/>
        public string checkSum
        {
            get
            {
                return this.checkSumField;
            }
            set
            {
                this.checkSumField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    public interface WebService_FacsPortTypeChannel : WeBuyCars.Financial.Infrastructure.Hyphen.Facs.WebService_FacsPortType, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
    public partial class WebService_FacsPortTypeClient : System.ServiceModel.ClientBase<WeBuyCars.Financial.Infrastructure.Hyphen.Facs.WebService_FacsPortType>, WeBuyCars.Financial.Infrastructure.Hyphen.Facs.WebService_FacsPortType, IDisposable
    {
        
        /// <summary>
        /// Implement this partial method to configure the service endpoint.
        /// </summary>
        /// <param name="serviceEndpoint">The endpoint to configure</param>
        /// <param name="clientCredentials">The client credentials</param>
        static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);
        
        public WebService_FacsPortTypeClient() : 
                base(WebService_FacsPortTypeClient.GetDefaultBinding(), WebService_FacsPortTypeClient.GetDefaultEndpointAddress())
        {
            this.Endpoint.Name = EndpointConfiguration.WebService_FacsPort.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public WebService_FacsPortTypeClient(EndpointConfiguration endpointConfiguration) : 
                base(WebService_FacsPortTypeClient.GetBindingForEndpoint(endpointConfiguration), WebService_FacsPortTypeClient.GetEndpointAddress(endpointConfiguration))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public WebService_FacsPortTypeClient(EndpointConfiguration endpointConfiguration, string remoteAddress) : 
                base(WebService_FacsPortTypeClient.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public WebService_FacsPortTypeClient(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(WebService_FacsPortTypeClient.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public WebService_FacsPortTypeClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        public WeBuyCars.Financial.Infrastructure.Hyphen.Facs.WebService_Facs_TransactionResult submitTransaction(string userProfile, string requestToken, WeBuyCars.Financial.Infrastructure.Hyphen.Facs.WebService_Facs_TransactionRequest transactionRequest)
        {
            return base.Channel.submitTransaction(userProfile, requestToken, transactionRequest);
        }
        
        public System.Threading.Tasks.Task<WeBuyCars.Financial.Infrastructure.Hyphen.Facs.WebService_Facs_TransactionResult> submitTransactionAsync(string userProfile, string requestToken, WeBuyCars.Financial.Infrastructure.Hyphen.Facs.WebService_Facs_TransactionRequest transactionRequest)
        {
            return base.Channel.submitTransactionAsync(userProfile, requestToken, transactionRequest);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
        
        public virtual System.Threading.Tasks.Task CloseAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginClose(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndClose));
        }
        
        private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.WebService_FacsPort))
            {
                System.ServiceModel.BasicHttpBinding result = new System.ServiceModel.BasicHttpBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                result.Security.Mode = System.ServiceModel.BasicHttpSecurityMode.Transport;
                return result;
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.WebService_FacsPort))
            {
                return new System.ServiceModel.EndpointAddress("https://qaws.hyphen.co.za/webservice/facs");
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.Channels.Binding GetDefaultBinding()
        {
            return WebService_FacsPortTypeClient.GetBindingForEndpoint(EndpointConfiguration.WebService_FacsPort);
        }
        
        private static System.ServiceModel.EndpointAddress GetDefaultEndpointAddress()
        {
            return WebService_FacsPortTypeClient.GetEndpointAddress(EndpointConfiguration.WebService_FacsPort);
        }
        
        public enum EndpointConfiguration
        {
            
            WebService_FacsPort,
        }

        #region IDisposable implementation

        /// <summary>
        /// IDisposable.Dispose implementation, calls Dispose(true).
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
        }

        /// <summary>
        /// Dispose worker method. Handles graceful shutdown of the
        /// client even if it is an faulted state.
        /// </summary>
        /// <param name="disposing">Are we disposing (alternative
        /// is to be finalizing)</param>
        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                try
                {
                    if (State != CommunicationState.Faulted)
                    {
                        Close();
                    }
                }
                finally
                {
                    if (State != CommunicationState.Closed)
                    {
                        Abort();
                    }
                }
            }
        }

        /// <summary>
        /// Finalizer.
        /// </summary>
        ~WebService_FacsPortTypeClient()
        {
            Dispose(false);
        }

        #endregion
    }
}
