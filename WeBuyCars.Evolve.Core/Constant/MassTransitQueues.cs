namespace WeBuyCars.Evolve.Core.Constant;

public abstract class MassTransitQueues
{
    // BLMS
    public const string EvolveBlmsFlagsCreated = "evolve-blms-flags-created";
    
    // Evolve
    public const string EvolveRcAccountBalance = "evolve-rc-account-balance";
    public const string EvolveMonthlyTransactions = "evolve-monthly-transactions";
    public const string EvolveSyncAdjCostPriceBatch = "evolve-sync-adj-costprice-batch";
    public const string EvolveSyncAdjCostPrice = "evolve-sync-adj-costprice";
    public const string EvolveRemoveInvoice = "evolve-remove-invoice";
    public const string EvolveSyncInvoices = "evolve-sync-invoices";
    public const string EvolveBackgroundSync = "evolve-background-sync";
    public const string EvolveRefundPayments = "evolve-refund-payments";
    public const string EvolveDepositPayments = "evolve-deposit-payments";
    public const string EvolveSettlementPayments = "evolve-settlement-payments";
    public const string EvolveStockStatusUpdates = "evolve-stock-status-updates";
    public const string EvolveVehicleStockMaintenance = "evolve-vehicle-stock-maintenance";
    public const string EvolveSundryPosting = "evolve-sundry-posting";
    public const string EvolveVehiclePosting = "evolve-vehicle-posting";
    public const string EvolveOutworkPosting = "evolve-outwork-posting";
    public const string EvolveAdminOrderStatus = "evolve-admin-order-status";
    
    // IMS
    public const string EvolveStockFlagCreated = "evolve-stock-flag-created";
    public const string EvolveStockLocationUpdates = "evolve-stock-location-updates";
    public const string EvolveFullStockMaintenance = "evolve-full-stock-maintenance";
    public const string EvolveAdminOrderVehicleOutwork = "evolve-admin-order-vehicle-outwork";
    
    // SLMS
    public const string EvolveSalesInitialisations = "evolve-sales-initialisations";
    
    // Org
    public const string OrgLocationCategoryCodes = "evolve-location-change-events";
}