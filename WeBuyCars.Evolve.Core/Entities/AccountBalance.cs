namespace WeBuyCars.Evolve.Core.Entities
{
    public sealed class AccountBalance
    {

        #region Constructors

        public AccountBalance()
        {
        }

        #endregion

        #region Properties

        public string AccountNumber { get; set; }

        public decimal Balance { get; set; }

        public decimal Current { get; set; }

        public decimal Days30 { get; set; }

        public decimal Days60 { get; set; }

        public decimal Days90 { get; set; }

        public decimal Days120 { get; set; }

        public decimal Days150 { get; set; }

        #endregion
    }
}