using WeBuyCars.Evolve.Core.SharedKernel;

namespace WeBuyCars.Evolve.Core.Entities;

public sealed class AccessoryCode : Entity
{
    public string Location { get; set; }
    public string DocumentationFeeMaxMinCode { get; set; }
    public string DocumentationFeeMaxCode { get; set; }
    public string DeliveryCode { get; set; }
    public string LicenseAndRegistrationCode { get; set; }
    public string WarrantyCode { get; set; }
    public string DealerTermsCode { get; set; }
    public string ServicePartsCode { get; set; }
    public string ServicePartsGLAccount { get; set; }
    public string BatteryCode { get; set; }
    public string BatteryCodeGLAccount { get; set; }
    public string TyresCode { get; set; }
    public string TyresCodeGLAccount { get; set; }
    public string WindscreensCode { get; set; }
    public string WindscreensCodeGLAccount { get; set; }
    public string CustomerRegFeeCode { get; set; }
    public string PeaceOfMindProtectorCode { get; set; }
}

public static class AccessoryCodeConstants
{
    public const int LocationMaxLength = 3;
    public const int CodeMaxLength = 10;
    public const int GLAccountMaxLength = 16;
}