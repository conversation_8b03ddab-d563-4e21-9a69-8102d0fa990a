using WeBuyCars.Evolve.Core.SharedKernel;

namespace WeBuyCars.Evolve.Core.Entities;

public sealed class RepairType : Entity
{
    public string Type { get; set; }
    public string Category { get; set; }
    public string EvolveRepairCode { get; set; }
}

public static class RepairTypeConstants
{
    public const int TypeMaxLength = 100;
    public const int CategoryMaxLength = 50;
    public const int EvolveRepairCodeMaxLength = 5;
}