using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using WeBuyCars.Evolve.Core.Entities;

namespace WeBuyCars.Evolve.Core.SharedKernel;

public interface ISalesCategoryCodeRepository : IRepository
{
    Task<List<SalesCategoryCode>> GetByWarehouseCode(string code, CancellationToken cancellationToken);
    Task<List<SalesCategoryCode>> GetSalesCategoryCodes(CancellationToken cancellationToken);
    SalesCategoryCode Add(SalesCategoryCode entity);
}
