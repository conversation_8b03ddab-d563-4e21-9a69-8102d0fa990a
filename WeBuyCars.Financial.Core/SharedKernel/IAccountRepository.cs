using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using Kendo.Mvc.UI;
using WeBuyCars.Core.Infrastructure.DynamicLinq.DynamicFilter;
using WeBuyCars.Financial.Core.Entities;
using WeBuyCars.Financial.Core.Enumerations;

namespace WeBuyCars.Financial.Core.SharedKernel
{
    public interface IAccountRepository : IRepository
    {
        Account Add(Account entity);

        Task<bool> ExistsAsync(Expression<Func<Account, bool>> predicate);

        Task<List<Account>> FindByAsync(Expression<Func<Account, bool>> predicate);

        // TODO: Remove this when removing Kendo
        Task<DataSourceResult> GetAllAsync(DataSourceRequest request);

        Task<DynamicLinqResult> GetAllAsync(DynamicLinqRequest request, CancellationToken cancellationToken);

        Task<Account> GetAsync(long id);

        Task<Account> GetByAccountNumberAsync(string accountNumber);

        Task<DynamicLinqResult> GetByAccountNumberAsync(string accountNumber, DynamicLinqRequest request);

        Task<Account> GetByAccountNumberAsync(string accountNumber, int paymentCutoff, PaymentCutoffType cutoffType);

        Task<Account> GetByTransactionReferenceAsync(string reference);

        void Remove(Account entity);

        void Update(Account entity);
    }
}