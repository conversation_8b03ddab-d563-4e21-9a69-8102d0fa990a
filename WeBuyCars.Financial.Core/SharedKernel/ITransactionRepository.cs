using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using WeBuyCars.Financial.Core.Entities;

namespace WeBuyCars.Financial.Core.SharedKernel
{
    public interface ITransactionRepository : IRepository
    {
        Transaction Add(Transaction entity);

        Task<bool> ExistsAsync(Expression<Func<Transaction, bool>> predicate);

        Task<List<Transaction>> FindByAsync(Expression<Func<Transaction, bool>> predicate);

        Task<List<Transaction>> GetAllAsync();

        Task<Transaction> GetAsync(long id);

        void Remove(Transaction entity);

        void Update(Transaction entity);
        Task<Transaction> FirstOrDefaultAsync(Expression<Func<Transaction, bool>> predicate);

        Task<Transaction> GetOzowDepositByProviderReferenceAsync(string reference);
        
        Task<Transaction> GetOzowRefundByProviderReferenceAsync(string reference);

        Task<Transaction> GetWalletDocDepositByProviderReferenceAsync(string reference);

        Task<Transaction> GetWalletDocRefundByProviderReferenceAsync(string reference);
        
    }
}