using System.Collections.Generic;
using System.Linq;
using WeBuyCars.Financial.Core.Enumerations;

namespace WeBuyCars.Financial.Core.Entities
{
    public class Bank 
    {

        readonly HashSet<HyphenTransactionType> _hyphenTransactionTypes;

        public IReadOnlyCollection<HyphenTransactionType> HyphenTransactionTypes => _hyphenTransactionTypes?.ToList();
        
        public BankEnumeration Id { get; set; }

        public string Name { get; set; }

        public Bank()
        {
            _hyphenTransactionTypes = new HashSet<HyphenTransactionType>();
        }
    }
}