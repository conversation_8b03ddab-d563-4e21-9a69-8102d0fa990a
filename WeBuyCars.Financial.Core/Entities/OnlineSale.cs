using System;
using WeBuyCars.Core.SharedKernel;

namespace WeBuyCars.Financial.Core.Entities
{
    public sealed class OnlineSale: Entity
    {
        public string AccountNumber { get; set; }

        public string Email { get; set; }

        public string MobileNumber { get; set; }

        public string CustomerType { get; set; }

        public string CustomerName { get; set; }

        public string TransactionReference { get; set; }
        
        public decimal Amount { get; set; }

        public string TransactionStatus { get; set; }

        public string TransactionType { get; set; }
        
        public string Provider { get; set; }

        public DateTimeOffset? PaymentDate { get; set; }

        public string StockNumber { get; set; }

        public string StockDescription { get; set; }

        public string Consent { get; set; }
    }
}
