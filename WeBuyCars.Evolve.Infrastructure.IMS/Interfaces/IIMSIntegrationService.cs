using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using WeBuyCars.Core.Infrastructure.ActiveDirectory.Authentication.Extensions;
using WeBuyCars.Evolve.Infrastructure.IMS.Models;

namespace WeBuyCars.Evolve.Infrastructure.IMS
{
    public interface IImsIntegrationService : IAbstractIntegrationService
    {
        Task<StockItemResponse> GetStockItem(string stockNumber);
        Task<StockItemResponse> UpdateStockItem(StockItemUpdateRequest request);
        Task<StockItemResponse> UpdateStockItemEvolve(StockItemEvolveRequest request);
        Task UpdateStockStatusSold(string stockNumber, string location, decimal soldAmount,
            string employeeNumber = "<EMAIL>");
        Task ScanInReconGatePass(string qrCode, string location, CancellationToken cancellationToken);
        Task<List<FlagDto>> GetFlags(string stockNumber, FlagType flagType, CancellationToken cancellationToken);
        Task AddStockComment(string stockNumber, CommentDto commentDto, CancellationToken cancellationToken);
        
        public new static string  AppSettingsTag => "inventory-management-api"; 
    }
}