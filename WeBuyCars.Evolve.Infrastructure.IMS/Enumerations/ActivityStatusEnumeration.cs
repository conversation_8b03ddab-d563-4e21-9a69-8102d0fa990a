using System.ComponentModel;

namespace WeBuyCars.Evolve.Infrastructure.IMS.Enumerations
{
    public enum ActivityStatusEnumeration
    {
        [Description("Bought")]
        Bought = 1,

        [Description("To be priced")]
        ToBePriced = 2,

        [Description("For sale")]
        ForSale = 3,

        [Description("Online")]
        Online = 4,

        [Description("Reserved")]
        Reserved = 5,

        [Description("Sold")]
        Sold = 6,

        [Description("Released")]
        Released = 7,

        [Description("InTransit-Branch")]
        InTransitBranch = 8,

        [Description("InTransit-Client")]
        InTransitClient = 9,

        [Description("NFS-Admin")]
        NotForSaleAdmin = 10,

        [Description("NFS-CompanyUse")]
        NotForSaleCompanyUse = 11,

        [Description("NFS-Docs")]
        NotForSaleDocs = 12,

        [Description("Repair")]
        Repair = 13,

        [Description("Cancelled")]
        Cancelled = 14,

        [Description("Stolen")]
        Stolen = 15,
        
        [Description("Recon")]
        Recon = 16
    }
}