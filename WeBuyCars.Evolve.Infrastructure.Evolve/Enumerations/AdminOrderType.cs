using System;
using System.ComponentModel;

namespace WeBuyCars.Evolve.Infrastructure.Evolve.Enumerations;

public enum AdminOrderType
{
    [Description("Admin Order GL Order")]
    GLOrder,
    [Description("Admin Order Accounts Payable")]
    AccountsPayable,
    [Description("Admin Order Vehicle Outwork")]
    VehicleOutwork,
    [Description("Admin Order Vehicle Internal")]
    VehicleInternal,
    [Description("Admin Order Service Outwork")]
    ServiceOutwork,
    [Description("Admin Order Service Internal")]
    ServiceInternal,
    [Description("Admin Order Fixed Asset Intercompany")]
    FixedAssetIntercompany,
    [Description("Admin Order Fixed Asset")]
    FixedAsset
}

[Flags]
public enum AdminOrderInternalType
{
    [Description("")]
    None = 0,
    [Description("Parts Department")]
    PartsDepartment = 1,
    [Description("Service Department")]
    ServiceDepartment = 2,
    [Description("Forecourt Department")]
    ForecourtDepartment = 4
}

public static class OrderTypeExtensions
{
    public static AdminOrderInternalType GetAllowedInternalTypes(this AdminOrderType orderType)
    {
        return orderType switch
        {
            AdminOrderType.GLOrder => AdminOrderInternalType.PartsDepartment | AdminOrderInternalType.ServiceDepartment | AdminOrderInternalType.ForecourtDepartment,
            AdminOrderType.VehicleInternal => AdminOrderInternalType.PartsDepartment | AdminOrderInternalType.ServiceDepartment | AdminOrderInternalType.ForecourtDepartment,
            AdminOrderType.ServiceInternal => AdminOrderInternalType.ForecourtDepartment,
            _ => AdminOrderInternalType.None
        };
    }

    public static char GetCode(this AdminOrderType orderType)
    {
        return orderType switch
        {
            AdminOrderType.GLOrder => 'G',
            AdminOrderType.AccountsPayable => 'A',
            AdminOrderType.VehicleOutwork => 'U',
            AdminOrderType.VehicleInternal => 'I',
            AdminOrderType.ServiceOutwork => 'V',
            AdminOrderType.ServiceInternal => 'S',
            AdminOrderType.FixedAssetIntercompany => 'F',
            AdminOrderType.FixedAsset => 'X',
            _ => throw new ArgumentException("Invalid order type")
        };
    }

    public static AdminOrderType GetOrderTypeFromCode(char code)
    {
        return code switch
        {
            'G' => AdminOrderType.GLOrder,
            'A' => AdminOrderType.AccountsPayable,
            'U' => AdminOrderType.VehicleOutwork,
            'I' => AdminOrderType.VehicleInternal,
            'V' => AdminOrderType.ServiceOutwork,
            'S' => AdminOrderType.ServiceInternal,
            'F' => AdminOrderType.FixedAssetIntercompany,
            'X' => AdminOrderType.FixedAsset,
            _ => throw new ArgumentException("Invalid order type code")
        };
    }
}