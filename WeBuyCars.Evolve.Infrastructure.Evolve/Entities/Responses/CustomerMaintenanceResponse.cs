using System.Xml.Serialization;

namespace WeBuyCars.Evolve.Infrastructure.Evolve.Entities.Responses
{
    [XmlRoot(ElementName = "RowDetails")]
    public sealed class CustomerMaintenanceResponse
    {
        [XmlElement(ElementName = "RowID")]
        public object Id { get; set; }

        [XmlElement(ElementName = "RowStatus")]
        public string RowStatus { get; set; }

        [XmlElement(ElementName = "RowDetailedMessage")]
        public string RowDetailedMessage { get; set; }

        [XmlElement(ElementName = "CRMReferenceNo")]
        public string CRMReferenceNo { get; set; }

        [XmlElement(ElementName = "DMSReferenceNo")]
        public string DMSReferenceNo { get; set; }
    }
}
