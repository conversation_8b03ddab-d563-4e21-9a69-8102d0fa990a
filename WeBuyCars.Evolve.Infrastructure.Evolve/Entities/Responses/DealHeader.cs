using System;
using System.Globalization;
using System.Xml.Serialization;

namespace WeBuyCars.Evolve.Infrastructure.Evolve.Entities.Responses
{
    public sealed class DealHeader
    {
        #region Properties

        public string DealNumber { get; set; }

        public string DealStatus { get; set; }

        public string DealType { get; set; }

        public string CustomerName { get; set; }

        public string CustSequenceID { get; set; }

        public string ARAccount1 { get; set; }

        public string VehStockNo { get; set; }

        public string VehVinNumber { get; set; }

        public string VehType { get; set; }

        [XmlElement(ElementName = "DateCreated")]
        public string DateCreatedString { get; set; }

        [XmlIgnore]
        public DateTime DateCreated
        {
            get
            {
                DateTime.TryParseExact(DateCreatedString, "dd/MM/yy", CultureInfo.InvariantCulture, DateTimeStyles.None, out var value);
                return value;
            }
        }

        [XmlElement(ElementName = "DateAmended")]
        public string DateAmendedString { get; set; }

        [XmlIgnore]
        public DateTime DateAmended
        {
            get
            {
                DateTime.TryParseExact(DateAmendedString, "dd/MM/yy", CultureInfo.InvariantCulture, DateTimeStyles.None, out var value);
                return value;
            }
        }

        public string AccountMonth { get; set; }

        [XmlElement(ElementName = "InvoiceDate")]
        public string InvoiceDateString { get; set; }

        [XmlIgnore]
        public DateTime? InvoiceDate
        {
            get
            {
                DateTime.TryParseExact(InvoiceDateString, "dd/MM/yy", CultureInfo.InvariantCulture, DateTimeStyles.None, out var value);
                return value;
            }
        }

        public string InvoiceNumber { get; set; }

        public int RepNumber { get; set; }

        public string DocumentType { get; set; }

        public string OriginalInvoice { get; set; }

        public string CreditNotes { get; set; }

        public string InvoiceTo { get; set; }

        public string InvoiceToAddress1 { get; set; }

        public string InvoiceToAddress2 { get; set; }

        public string InvoiceToAddress3 { get; set; }

        public string InvoiceToCity { get; set; }

        public string InvoiceToPostal { get; set; }

        public string DeliverTo { get; set; }

        public string DeliverToAddress1 { get; set; }

        public string DeliverToAddress2 { get; set; }

        public string DeliverToAddress3 { get; set; }

        public string DeliverToCity { get; set; }

        public string DeliverToPostal { get; set; }

        public string SalesRep { get; set; }

        public string OTPNumber { get; set; }

        public string VehCatCode { get; set; }

        #endregion
    }
}