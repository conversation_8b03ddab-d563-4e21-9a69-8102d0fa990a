using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text.Json.Serialization;
using System.Xml.Serialization;

namespace WeBuyCars.Evolve.Infrastructure.Evolve.Entities.Responses.VehicleSalesSummaries;

[XmlRoot(ElementName = "Summary")]
public sealed class VehicleSalesSummary : Summary
{
    #region Fields

    [XmlElement(ElementName = "VehicleSalesCount")]
    [JsonIgnore]
    public string m_VehicleSalesCount;

    [XmlElement(ElementName = "VehicleSalesTotalValue")]
    [JsonIgnore]
    public string m_VehicleSalesTotalValue;

    [XmlElement(ElementName = "QueryDateTime")]
    [JsonIgnore]
    public string m_QueryDateTime;
    
    #endregion
    
    #region Properties

    [XmlIgnore]
    public long? VehicleSalesCount
    {
        get
        {
            long.TryParse(m_VehicleSalesCount, NumberStyles.Any, CultureInfo.InvariantCulture, out var count);
            return count;
        }
        set => m_VehicleSalesCount = value.ToString();
    }

    [XmlIgnore]
    public decimal? VehicleSalesTotalValue
    {
        get
        {
            decimal.TryParse(m_VehicleSalesTotalValue, NumberStyles.Any, CultureInfo.InvariantCulture, out var value);
            return value;
        }
        set => m_VehicleSalesTotalValue = value.ToString();
    }

    [XmlElement(ElementName = "VehicleCategorySummary")]
    public List<VehicleCategorySummary> VehicleCategorySummaries { get; set; }

    [XmlIgnore]
    public DateTime QueryDateTime
    {
        get
        {
            DateTime.TryParse(m_QueryDateTime, out var date);
            return date;
        }
        set => m_QueryDateTime = value.ToString("G");
    }

    #endregion
}