using System.Xml.Serialization;

namespace WeBuyCars.Evolve.Infrastructure.Evolve.Entities.Responses.VehicleSalesSummaries;

[XmlRoot(ElementName = "VehicleCategorySummary")]
public sealed class VehicleCategorySummary
{
    [XmlElement(ElementName = "VehicleCategory")]
    public string VehicleCategory { get; set; }

    [XmlElement(ElementName = "VehicleCategoryCount")]
    public int VehicleCategoryCount { get; set; }

    [XmlElement(ElementName = "VehicleCategoryValue")]
    public decimal VehicleCategoryValue { get; set; }
    
    [XmlElement(ElementName = "VehicleCategoryGP")]
    public decimal VehicleCategoryGp { get; set; }
}