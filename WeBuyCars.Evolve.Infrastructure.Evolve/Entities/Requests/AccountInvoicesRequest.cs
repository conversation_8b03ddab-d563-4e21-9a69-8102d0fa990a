namespace WeBuyCars.Evolve.Infrastructure.Evolve.Entities.Requests
{
    public sealed class AccountInvoicesRequest
    {
        #region Constructors

        public AccountInvoicesRequest()
        {
        }

        public AccountInvoicesRequest(string accountNumber)
        {
            AccountNumber = accountNumber;
        }

        public AccountInvoicesRequest(string coNumber, string accountNumber) : this(accountNumber)
        {
            CoNumber = coNumber;
        }

        #endregion

        #region Properties

        public string CoNumber { get; set; } = "10WC";

        public string AccountNumber { get; set; }

        #endregion
    }
}
