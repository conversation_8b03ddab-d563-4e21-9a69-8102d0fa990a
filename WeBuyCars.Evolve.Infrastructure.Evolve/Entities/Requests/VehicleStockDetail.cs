using System.Xml.Serialization;

namespace WeBuyCars.Evolve.Infrastructure.Evolve.Entities.Requests
{
    [XmlRoot(ElementName = "VehicleStockDetail")]
	public sealed class VehicleStockDetail
	{
		[XmlElement(ElementName = "VehStockNo")]
		public string VehStockNo { get; set; }

		[XmlElement(ElementName = "VehCatCode")]
		public string VehCatCode { get; set; }

		[XmlElement(ElementName = "VehicleLocation")]
		public string VehicleLocation { get; set; }
	}
}
