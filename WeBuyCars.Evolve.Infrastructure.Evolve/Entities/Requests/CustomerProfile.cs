using System.Xml.Serialization;

namespace WeBuyCars.Evolve.Infrastructure.Evolve.Entities.Requests
{
    [XmlRoot(ElementName = "CustomerProfile")]
    public sealed class CustomerProfile
    {
        #region Constructors

        public CustomerProfile()
        {
        }

        #endregion

        #region Properties

        [XmlElement(ElementName = "Occupation")]
        public string Occupation { get; set; }

        [XmlElement(ElementName = "ReceiveEmail")]
        public string ReceiveEmail { get; set; } = "Yes";

        [XmlElement(ElementName = "ReceiveSMS")]
        public string ReceiveSMS { get; set; }

        [XmlElement(ElementName = "ReceivePost")]
        public string ReceivePost { get; set; }

        [XmlElement(ElementName = "ReceiveTelemarketing")]
        public string ReceiveTelemarketing { get; set; }

        [XmlElement(ElementName = "PrimaryContact")]
        public string PrimaryContact { get; set; }

        [XmlElement(ElementName = "SecondaryContact")]
        public string SecondaryContact { get; set; }

        [XmlElement(ElementName = "ReceiveMarketingAll")]
        public string ReceiveMarketingAll { get; set; }

        [XmlElement(ElementName = "ReceiveMarketingVehicle")]
        public string ReceiveMarketingVehicle { get; set; }

        [XmlElement(ElementName = "ReceiveMarketingService")]
        public string ReceiveMarketingService { get; set; }

        [XmlElement(ElementName = "ReceiveMarketingParts")]
        public string ReceiveMarketingParts { get; set; }

        #endregion
    }
}
