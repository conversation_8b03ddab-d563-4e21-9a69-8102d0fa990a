using System.Xml.Serialization;
using WeBuyCars.Evolve.Infrastructure.Evolve.Enumerations;

namespace WeBuyCars.Evolve.Infrastructure.Evolve.Entities.Requests;

[XmlRoot(ElementName = "OrderHeader")]
public sealed class OrderHeader
{
    [XmlElement(ElementName = "CompanyNumber")]
    public string CompanyNumber { get; set; }

    [XmlElement(ElementName = "ImportRequisitionNumber")]
    public string ImportRequisitionNumber { get; set; }

    [XmlElement(ElementName = "OrderType")]
    public AdminOrderType OrderType { get; set; }

    [XmlElement(ElementName = "AccountNo")]
    public string AccountNo { get; set; }
    
    [XmlElement(ElementName = "GLAccountNo")]
    public string GLAccountNo { get; set; }

    [XmlElement(ElementName = "StockNumber")]
    public string StockNumber { get; set; }
    
    [XmlElement(ElementName = "RONumber")]
    public string RepairOrderNumber { get; set; }
    
    [XmlElement(ElementName = "JobNumber")]
    public string JobNumber { get; set; }
    
    [XmlElement(ElementName = "InternalType")]
    public AdminOrderInternalType? InternalType { get; set; }

    [XmlElement(ElementName = "Description")]
    public string Description { get; set; }

    [XmlElement(ElementName = "BaseOrForeignCurr")]
    public string BaseOrForeignCurr { get; set; }

    [XmlElement(ElementName = "ExchangeRate")]
    public decimal ExchangeRate { get; set; } = 1;
    
    [XmlElement(ElementName = "Category")]
    public string Category { get; set; }

    public static OrderHeader Create(OrderHeader orderHeader, string companyCode, string currencyCode)
    {
        return new OrderHeader
        {
            CompanyNumber = companyCode,
            ImportRequisitionNumber = orderHeader.ImportRequisitionNumber,
            OrderType = orderHeader.OrderType,
            AccountNo = orderHeader.AccountNo,
            GLAccountNo = orderHeader.GLAccountNo,
            StockNumber = orderHeader.StockNumber,
            RepairOrderNumber = orderHeader.RepairOrderNumber,
            JobNumber = orderHeader.JobNumber,
            InternalType = orderHeader.InternalType,
            Description = orderHeader.Description,
            BaseOrForeignCurr = currencyCode,
            ExchangeRate = 1,
            Category = orderHeader.Category
        };
    }
}