using System.Collections.Generic;
using System.Xml.Serialization;

namespace WeBuyCars.Evolve.Infrastructure.Evolve.Entities.Requests
{
    [XmlRoot(ElementName = "RowDetails")]
    public sealed class FullOTPMaintenanceRequest
    {
		[XmlElement(ElementName = "RowID")]
		public int RowId { get; set; }

		[XmlElement(ElementName = "CustomerDetail")]
		public CustomerDetail CustomerDetail { get; set; }

		[XmlElement(ElementName = "CustomerProfile")]
		public CustomerProfile CustomerProfile { get; set; }

		[XmlElement(ElementName = "AccountsReceivable")]
		public AccountsReceivable AccountsReceivable { get; set; }

		[XmlElement(ElementName = "VehicleStockDetail")]
		public VehicleStockDetail VehicleStockDetail { get; set; }

		[XmlElement(ElementName = "DealHeader")]
		public DealHeader DealHeader { get; set; }

		[XmlElement(ElementName = "VehicleDetail")]
		public VehicleDetail VehicleDetail { get; set; }

		[XmlElement(ElementName = "Accessories")]
		public List<Accessories> Accessories { get; set; } = new List<Accessories>();
	}
}
