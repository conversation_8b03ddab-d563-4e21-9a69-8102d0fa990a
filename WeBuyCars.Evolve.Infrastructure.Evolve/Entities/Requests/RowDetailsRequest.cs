using System.Xml.Serialization;

namespace WeBuyCars.Evolve.Infrastructure.Evolve.Entities.Requests
{
    public sealed class RowDetailsRequest
    {
        [XmlElement(ElementName = "RowDetails")]
        public CustomerMaintenanceRequest Customer { get; set; }

        [XmlElement(ElementName = "RowDetails1")]
        public FullOTPMaintenanceRequest OTP { get; set; }

        public static RowDetailsRequest CreateCustomerMaintenanceRequest(CustomerDetail customerDetail, 
            bool creditAccount, bool inActiveAccount)
        {
            return new RowDetailsRequest
            {
                Customer = CustomerMaintenanceRequest.CreateCustomerRequest(customerDetail, creditAccount, inActiveAccount)
            };
        }
    }
}
