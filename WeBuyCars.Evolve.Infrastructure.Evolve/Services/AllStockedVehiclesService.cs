using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WeBuyCars.Core.Exceptions;
using WeBuyCars.Evolve.Infrastructure.Evolve.Configuration;
using WeBuyCars.Evolve.Infrastructure.Evolve.Entities.Requests;
using WeBuyCars.Evolve.Infrastructure.Evolve.Entities.Requests.AllStockedVehicles;
using WeBuyCars.Evolve.Infrastructure.Evolve.Entities.Responses;
using WeBuyCars.Evolve.Infrastructure.Evolve.Entities.Shared;
using WeBuyCars.Evolve.Infrastructure.Evolve.Extensions;

namespace WeBuyCars.Evolve.Infrastructure.Evolve.Services;

public sealed class AllStockedVehiclesService : ServiceBase
{
    #region Fields

    private const string FunctionName = "WBC_GetAllStockedVehicles";
    private readonly ILogger<AllStockedVehiclesService> _logger;
    private readonly TimeSpan _timeSpan = TimeSpan.FromMinutes(10);

    #endregion

    #region Constructors

    public AllStockedVehiclesService
    (
        HttpClient client,
        IOptionsMonitor<EvolveSettings> settings,
        ILogger<AllStockedVehiclesService> logger
    )
        : base(client, settings)
    {
        client.Timeout = _timeSpan;
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    #endregion

    #region Public Methods

    public async Task<List<EvolveVehicle>> GetAllStockedVehicles()
    {
        var cts = new CancellationTokenSource();
        cts.CancelAfter(_timeSpan);

        try
        {
            var request = new EvolveRequest
            {
                Action = new EvolveAction(FunctionName, Settings.InterfaceCode),
                Request = new AllStockedVehiclesRequest()
            };

            var xml = request.SerializeObject();
            _logger.LogWarning("{MethodName} - {FunctionName} Get All Stocked Vehicles Request: {RequestBody}",
                nameof(GetAllStockedVehicles),
                FunctionName,
                xml
            );

            var content = new StringContent(xml, Encoding.UTF8, "application/xml");
            var response = await Client.PostAsync("", content, cts.Token);
            response.EnsureSuccessStatusCode();

            var result = await response.Content.ReadAsStringAsync(cts.Token);
            var data = result.Deserialize<EvolveResponse>();

            return data.Result.Success switch
            {
                true => data.Response.RowDetails.Select(i => i.EvolveVehicle).ToList(),
                false => throw new DomainException(data.Result.RequestDetailedMessage)
            };
        }
        catch (DomainException)
        {
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "The stock request failed.");
            throw new InfrastructureException(ex.InnerException ?? ex);
        }
    }

    #endregion
}