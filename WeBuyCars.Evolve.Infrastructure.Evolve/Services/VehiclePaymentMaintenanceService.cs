using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using WeBuyCars.Core.Exceptions;
using WeBuyCars.Evolve.Infrastructure.Evolve.Configuration;
using WeBuyCars.Evolve.Infrastructure.Evolve.Entities.Requests;
using WeBuyCars.Evolve.Infrastructure.Evolve.Entities.Responses;
using WeBuyCars.Evolve.Infrastructure.Evolve.Entities.Shared;
using WeBuyCars.Evolve.Infrastructure.Evolve.Extensions;

namespace WeBuyCars.Evolve.Infrastructure.Evolve.Services
{
    public sealed class VehiclePaymentMaintenanceService : ServiceBase
    {
        #region Fields

        private const string FunctionName = "WBC_ARVehiclePaymentMaintenance";
        private readonly TimeSpan _timeSpan = TimeSpan.FromMinutes(2);
        private readonly ILogger<VehiclePaymentMaintenanceService> _logger;

        #endregion

        #region Constructors

        public VehiclePaymentMaintenanceService(
            HttpClient client, 
            IOptionsMonitor<EvolveSettings> settings,            
            ILogger<VehiclePaymentMaintenanceService> logger 

        ) : base(client, settings)
        {
            client.Timeout = _timeSpan;
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        #endregion

        #region Public Methods
        
        public async Task<ARPaymentInfoResponse> PostPaymentAsync(string reference, string accountNumber, 
            decimal amount, string[] stockNumbers = null)
        {
            var cts = new CancellationTokenSource();
            cts.CancelAfter(_timeSpan);

            try
            {
                var request = new EvolveRequest
                {
                    Action = new EvolveAction(FunctionName, Settings.InterfaceCode),
                    Request = new VehiclePaymentInfoRequest(VehiclePaymentInfo.Create(accountNumber, amount, 
                        stockNumbers, reference))
                };
                
                var xml = request.SerializeObject();
                _logger.LogWarning("{FunctionName} Post Payment Request: {Xml}",
                    FunctionName,
                    xml
                );

                var content = new StringContent(xml, Encoding.UTF8, "application/xml");
                var response = await Client.PostAsync("", content, cts.Token);

                response.EnsureSuccessStatusCode();

                var result = await response.Content.ReadAsStringAsync(cts.Token);
                _logger.LogWarning("{FunctionName} Post Payment Response: {Result}",
                    FunctionName,
                    result
                );

                var data = result.Deserialize<EvolveResponse>();
                if (!data.Result.Success)
                {
                    if (data.Result.RequestDetailedMessage.Contains("ERROR_ProgressErrorReturned -  Posting already exists with WBCReference"))
                    {
                        throw new DomainException(data.Result.RequestDetailedMessage);
                    }

                    throw new InfrastructureException(data.Result.RequestDetailedMessage);
                }

                var info = data.Response.RowDetails.FirstOrDefault();

                if (info is null)
                {
                    return null;
                }

                return new ARPaymentInfoResponse
                {
                    RowStatus = info.RowStatus,
                    RowDetailedMessage = info.RowDetailedMessage
                };
            }
            catch (DomainException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new InfrastructureException(ex);
            }
        }

        #endregion
    }
}
