using System.Collections.Generic;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.CalculateVehicleLicenceFee;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleLicenceRenewal.GetVehiclesDueForRenewal;

public class VehiclesLicenceExpiryData
{
    public Result result { get; set; }
    public Data data { get; set; }

    public class Result
    {
        public ErrorMessage[] errorMessages { get; set; }
        public bool successful { get; set; }
    }

    public class Data
    {
        public List<Vehicle> GetVehiclesAndLicenceExpiryDatesResponse { get; set; }
    }
}