namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleLicenceRenewal;

public sealed class Vehicle
{
    /// <summary>
    /// Register number of the motor vehicle (FieldId: 1)
    /// </summary>
    public string? VehicleRegisterNumber { get; set; }
    
    /// <summary>
    /// Licence number of the motor vehicle (FieldId: 2)
    /// </summary>
    public string? LicenceNumber { get; set; }
    
    /// <summary>
    /// Engine number of the vehicle (FieldId: 3)
    /// </summary>
    public string? EngineNumber { get; set; }
    
    /// <summary>
    /// Chassis number/VIN of the motor vehicle (FieldId: 4)
    /// </summary>
    public string? VinOrChassis { get; set; }
    
    /// <summary>
    /// Current licence expiry date (FieldId: 5)
    /// </summary>
    public string? MvLicExpryD { get; set; }
    
    /// <summary>
    /// Roadworthiness of the vehicle (FieldId: 6)
    /// </summary>
    public string? Roadworthy { get; set; }
    
    /// <summary>
    /// Make of the vehicle (FieldId: 7)
    /// </summary>
    public string? MvMake { get; set; }
    
    /// <summary>
    /// Model of the vehicle (FieldId: 8)
    /// </summary>
    public string? MvModel { get; set; }
    
    /// <summary>
    /// Series of the vehicle (FieldId: 9)
    /// </summary>
    public string? MvSeries { get; set; }
    
    /// <summary>
    /// Colour of the vehicle (FieldId: 10)
    /// </summary>
    public string? MvColour { get; set; }
    
    /// <summary>
    /// Tare weight in kilograms (FieldId: 11)
    /// </summary>
    public int Tare { get; set; }
}