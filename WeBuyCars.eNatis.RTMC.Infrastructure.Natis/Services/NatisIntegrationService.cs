using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Configurations;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces;
using System.Reflection;
using WeBuyCars.eNatis.RTMC.Core.Exceptions;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicle;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicleOwnerVerification;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetDriverInformation;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicleDetailed;
using System.Net.Http;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleOwnerRegistration;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.OwnershipHistory;
using Newtonsoft.Json;
using System.Net.Http.Headers;
using System.Text;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.NatisOwnershipHistoryRequest;
using System.Net;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.ControlNumber;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.NatisControlNumberVerificationRequest;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.CalculateVehicleLicenceFee;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.NatisCalculateVehicleLicenceFeeRequest;
using System.Collections.Generic;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.OnlineNCO;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleLicenceRenewal.CompleteRenewal;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleLicenceRenewal.GetVehiclesQuotation;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleLicenceRenewal.InitiateRenewal;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleLicenceRenewal.GetVehiclesDueForRenewal;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services
{
    public class NatisIntegrationService : INatisIntegrationService
    {
        #region prop

        readonly eNatisServiceOptions _serviceOptions;
        readonly eNatisEndPointOptions _endPointOptions;
        readonly ILogger<NatisIntegrationService> _logger;
        private readonly HttpClient _httpClient;
        readonly IDriverIntegrationService _driverIntegrationService;
        readonly IVehicleIntegrationService _vehicleIntegrationService;
        readonly IVehicleDetailIntegrationService _vehicleDetailIntegrationService;
        readonly IVehicleOwnerVerificationIntegrationService _vehicleOwnerVerificationIntegrationService;
        readonly IVehicleOwnerRegistrationIntegrationService _vehicleOwnerRegistrationIntegrationService;
        readonly IOnlineNCOIntegrationService _onlineNCOIntegrationService;

        #endregion

        #region ctor

        public NatisIntegrationService(
            IOptionsMonitor<eNatisServiceOptions> serviceOptions,
            IOptionsMonitor<eNatisEndPointOptions> endpointOptions,
            ILogger<NatisIntegrationService> logger,
            HttpClient httpClient,
            IDriverIntegrationService driverIntegrationService,
            IVehicleIntegrationService vehicleIntegrationService,
            IVehicleDetailIntegrationService vehicleDetailIntegrationService,
            IVehicleOwnerVerificationIntegrationService vehicleOwnerVerificationIntegrationService,
            IVehicleOwnerRegistrationIntegrationService vehicleOwnerRegistrationIntegrationService,
            IOnlineNCOIntegrationService onlineNCOIntegrationService
            )
        {

            _serviceOptions = serviceOptions?.CurrentValue ?? throw new ArgumentNullException(nameof(serviceOptions));
            _endPointOptions = endpointOptions?.CurrentValue ?? throw new ArgumentNullException(nameof(endpointOptions));
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _driverIntegrationService = driverIntegrationService ?? throw new ArgumentNullException(nameof(driverIntegrationService));
            _vehicleIntegrationService = vehicleIntegrationService ?? throw new ArgumentNullException(nameof(vehicleIntegrationService));
            _vehicleDetailIntegrationService = vehicleDetailIntegrationService ?? throw new ArgumentNullException(nameof(vehicleDetailIntegrationService));
            _vehicleOwnerVerificationIntegrationService = vehicleOwnerVerificationIntegrationService ?? throw new ArgumentNullException(nameof(vehicleOwnerVerificationIntegrationService));
            _vehicleOwnerRegistrationIntegrationService = vehicleOwnerRegistrationIntegrationService ?? throw new ArgumentNullException(nameof(vehicleOwnerRegistrationIntegrationService));
            _onlineNCOIntegrationService = onlineNCOIntegrationService ?? throw new ArgumentNullException(nameof(onlineNCOIntegrationService));

        }

        #endregion

        #region public

        /// <summary>
        /// HealthCheck Call to Natis System
        /// </summary>
        /// <param></param>
        /// <returns>bool</returns>
        public async Task<bool> NatisSystemAvailability()
        {

            var result = true;

            //Check WSDLs if they are operational
            result = await CheckRTMCVehicleEndPointAsync();
            result = await CheckRTMCVehicleDetailEndPointAsync();
            result = await CheckRTMCOwnerTitleHolderConfirmationEndPointAsync();
            result = await CheckRTMCDriverEndPointAsync();
            result = await CheckRTMCVehicleOwnerRegistrationEndPointAsync();
            result = await CheckRTMCTitleHolderTransferEndPointAsync();

            return result;

        }

        //Get Vehicle Health Check
        public async Task<bool> CheckRTMCVehicleEndPointAsync()
        {
            var result = true;

            //Check WSDLs if they are operational
            try
            {
                _httpClient.BaseAddress = new Uri(_endPointOptions.GetVehicle);
                var response = await _httpClient.GetAsync("");

                if (response.StatusCode == HttpStatusCode.OK)
                {
                    result = true;
                }
                else
                {
                    result = false;
                }

            }
            catch (Exception ex)
            {
                result = false;
                _logger.LogError($"HealthCheckFailure : GetVehicleResponse | Failed to Connect to {_endPointOptions.GetVehicle} | Exception : {ex}");
            }

            return result;
        }

        //Get Vehicle Detail Health Check
        public async Task<bool> CheckRTMCVehicleDetailEndPointAsync()
        {
            var result = true;

            //Check WSDLs if they are operational
            try
            {
                _httpClient.BaseAddress = new Uri(_endPointOptions.GetVehicleDetailedInformation);
                var response = await _httpClient.GetAsync("");

                if (response.StatusCode == HttpStatusCode.OK)
                {
                    result = true;
                }
                else
                {
                    result = false;
                }
            }
            catch (Exception ex)
            {
                result = false;
                _logger.LogError($"HealthCheckFailure : GetVehicleDetailedInformationResponse | Failed to Connect to {_endPointOptions.GetVehicleDetailedInformation} | Exception : {ex}");
            }

            return result;
        }

        //Get Owner Title Holder Confirmation Health Check
        public async Task<bool> CheckRTMCOwnerTitleHolderConfirmationEndPointAsync()
        {
            var result = true;

            //Check WSDLs if they are operational
            try
            {
                _httpClient.BaseAddress = new Uri(_endPointOptions.GetOwnerTitleHolderConfirmation);
                var response = await _httpClient.GetAsync("");

                if (response.StatusCode == HttpStatusCode.OK)
                {
                    result = true;
                }
                else
                {
                    result = false;
                }
            }
            catch (Exception ex)
            {
                result = false;
                _logger.LogError($"HealthCheckFailure : GetOwnerTitleHolderConfirmationResponse | Failed to Connect to {_endPointOptions.GetOwnerTitleHolderConfirmation} | Exception : {ex}");
            }

            return result;
        }

        //Get Driver Health Check
        public async Task<bool> CheckRTMCDriverEndPointAsync()
        {
            var result = true;

            //Check WSDLs if they are operational
            try
            {
                _httpClient.BaseAddress = new Uri(_endPointOptions.GetDriver);
                var response = await _httpClient.GetAsync("");

                if (response.StatusCode == HttpStatusCode.OK)
                {
                    result = true;
                }
                else
                {
                    result = false;
                }
            }
            catch (Exception ex)
            {
                result = false;
                _logger.LogError($"HealthCheckFailure : GetDriverResponse | Failed to Connect to {_endPointOptions.GetDriver} | Exception : {ex}");
            }

            return result;
        }

        //Get Vehicle Owner Registration Health Check
        public async Task<bool> CheckRTMCVehicleOwnerRegistrationEndPointAsync()
        {
            var result = true;

            //Check WSDLs if they are operational
            try
            {
                _httpClient.BaseAddress = new Uri(_endPointOptions.VehicleOwnerRegistration);
                var response = await _httpClient.GetAsync("");

                if (response.StatusCode == HttpStatusCode.OK)
                {
                    result = true;
                }
                else
                {
                    result = false;
                }
            }
            catch (Exception ex)
            {
                result = false;
                _logger.LogError($"HealthCheckFailure : VehicleOwnerRegistrationResponse | Failed to Connect to {_endPointOptions.VehicleOwnerRegistration} | Exception : {ex}");
            }

            return result;
        }

        //Get Owner Title Holder Transfer Health Check
        public async Task<bool> CheckRTMCTitleHolderTransferEndPointAsync()
        {
            var result = true;

            //Check WSDLs if they are operational
            try
            {
                _httpClient.BaseAddress = new Uri(_endPointOptions.OwnerTitleHolderTransfer);
                var response = await _httpClient.GetAsync("");

                if (response.StatusCode == HttpStatusCode.OK)
                {
                    result = true;
                }
                else
                {
                    result = false;
                }
            }
            catch (Exception ex)
            {
                result = false;
                _logger.LogError($"HealthCheckFailure : OwnerTitleHolderTransferResponse | Failed to Connect to {_endPointOptions.OwnerTitleHolderTransfer} | Exception : {ex}");
            }


            return result;
        }

        /// <summary>
        /// Make Call to Natis System to retrieve Vehicle Details
        /// </summary>
        /// <param name="vehicleRequest"></param>
        /// <returns>VehicleInformation</returns>
        public async Task<VehicleInformation> GetVehicleQuery(NatisGetVehicleRequest vehicleRequest)
        {

            VehicleInformation vehicleInformationResult = new VehicleInformation();
            var vehicleInformation = "";

            //Convert NatisGetVehicle Object to XML Payload for ENatis
            _logger.LogTrace("Service : NatisIntegrationService | Method : GetVehicleQuery | Convert Request to Enatis Request");
            var enatisrequest = _vehicleIntegrationService.ConvertNatisVehicleRequestXML(vehicleRequest);

            _logger.LogTrace("Service : NatisIntegrationService | Method : GetVehicleQuery | Enatis Request" + MaskXMLPassword(enatisrequest.ToString()));
            if (enatisrequest != "")
            {
                //Call Enatis
                _logger.LogTrace("Service : NatisIntegrationService | Method : GetVehicleQuery | Call GetVehicleInformation : " + MaskXMLPassword(enatisrequest.ToString()));

                //Convert Natis Response to Object
                try
                {
                    vehicleInformation = await CURLNatisIntegration(enatisrequest, "GetVehicleInformation");
                }
                catch (Exception ex)
                {
                    _logger.LogError($"NatisIntegrationService : GetVehicleQuery | Failed to Integrate to Natis | Request Object  : {MaskXMLPassword(enatisrequest.ToString())} | Exception : {ex}");
                }

                try
                {
                    //Check if the Response from RTMC have a value, else return a VPN Related Issue
                    if (vehicleInformation == "")
                    {
                        vehicleInformationResult.BaseResponse = DefaultVPNDownResponse();
                        return vehicleInformationResult;
                    }

                    vehicleInformationResult = await _vehicleIntegrationService.ConvertNatisVehicleResponseXML(vehicleInformation);
                }
                catch (Exception ex)
                {
                    _logger.LogError($"NatisIntegrationService : GetVehicleQuery | Failed to Convert VehicleInformation Object from Natis Response | Request Object  : {MaskXMLPassword(enatisrequest.ToString())} | Natis Response : {vehicleInformation} | Exception : {ex}");
                }
            }

            return vehicleInformationResult;

        }

        /// <summary>
        /// Make Call to Natis System to retrieve Vehicle Detailed Information
        /// </summary>
        /// <param name="vehicleRequest"></param>
        /// <returns>VehicleDetailInformation</returns>
        public async Task<VehicleDetailInformation> GetVehicleDetailedQuery(NatisGetVehicleDetailedRequest vehicleRequest, Guid auditLogId)
        {
            VehicleDetailInformation vehicleDetailedInformationResult = new VehicleDetailInformation();
            var vehicleDetailedInformation = "";

            //Convert NatisGetVehicle Object to XML Payload for ENatis
            _logger.LogWarning("GUID : " + auditLogId + " | Service : NatisIntegrationService | Method : GetVehicleDetailedQuery | Convert Request to Enatis Request");
            var enatisrequest = _vehicleDetailIntegrationService.ConvertNatisVehicleDetailedRequestXML(vehicleRequest);

            _logger.LogWarning("GUID : " + auditLogId + " | Service : NatisIntegrationService | Method : GetVehicleDetailedQuery | Enatis Request" + MaskXMLPassword(enatisrequest.ToString()));
            if (enatisrequest != "")
            {
                //Call Enatis
                _logger.LogWarning("GUID : " + auditLogId + " | NatisIntegrationService : GetVehicleDetailedQuery | Call CURLNatisIntegration : " + MaskXMLPassword(enatisrequest.ToString()));
                //Convert Natis Response to Object
                try
                {
                    vehicleDetailedInformation = await CURLNatisIntegration(enatisrequest, "GetVehicleDetailedInformation", auditLogId);
                }
                catch (Exception ex)
                {
                    _logger.LogError($"GUID : " + auditLogId + $" | NatisIntegrationService : GetVehicleDetailedQuery | Failed to Integrate to Natis | Request Object  : {MaskXMLPassword(enatisrequest.ToString())} | Exception : {ex}");
                }

                try
                {
                    //Check if the Response from RTMC have a value, else return a VPN Related Issue
                    if (vehicleDetailedInformation == "")
                    {
                        vehicleDetailedInformationResult.BaseResponse = DefaultVPNDownResponse();
                        return vehicleDetailedInformationResult;
                    }
                    _logger.LogWarning("GUID : " + auditLogId + " | NatisIntegrationService : GetVehicleDetailedQuery | Call ConvertNatisVehicleDetailedResponseXML : Vehicle Information Detail : " + vehicleDetailedInformation);
                    vehicleDetailedInformationResult = await _vehicleDetailIntegrationService.ConvertNatisVehicleDetailedResponseXML(vehicleDetailedInformation);
                }
                catch (Exception ex)
                {
                    _logger.LogError("GUID : " + auditLogId + $" | NatisIntegrationService : GetVehicleDetailedQuery | Failed to Convert DriverInformation Object from Natis Response | Request Object  : {MaskXMLPassword(enatisrequest.ToString())} | Natis Response : {vehicleDetailedInformation} | Exception : {ex}");
                }
            }

            return vehicleDetailedInformationResult;

        }

        /// <summary>
        /// Make Call to Natis System to retrieve Vehicle Owner TitleHolder Confirmation Details
        /// </summary>
        /// <param name="vehicleOwnerTitleHolderConfirmationRequest"></param>
        /// <returns>VehicleOwnerVerification</returns>
        public async Task<VehicleOwnerVerification> GetOwnerTitleHolderConfirmationQuery(NatisGetVehicleOwnerVerificationRequest vehicleOwnerTitleHolderConfirmationRequest)
        {
            VehicleOwnerVerification vehicleOwnerTitleHolderResult = new VehicleOwnerVerification();
            var vehicleOwnerVerification = "";

            //Convert NatisGetVehicle Object to XML Payload for ENatis
            _logger.LogTrace("NatisIntegrationService : GetOwnerTitleHolderConfirmationQuery | Convert Request to Enatis Request");
            var enatisrequest = _vehicleOwnerVerificationIntegrationService.ConvertNatisVehicleOwnerTitleHolderConfirmationRequestXML(vehicleOwnerTitleHolderConfirmationRequest);

            _logger.LogTrace("NatisIntegrationService : GetOwnerTitleHolderConfirmationQuery | Enatis Request" + MaskXMLPassword(enatisrequest.ToString()));
            if (enatisrequest != "")
            {
                //Call Enatis
                _logger.LogTrace("NatisIntegrationService : GetOwnerTitleHolderConfirmationQuery | Call GetVehicleOwnerTitleHolderConfirmationInformation : " + MaskXMLPassword(enatisrequest.ToString()));

                try
                {
                    vehicleOwnerVerification = await CURLNatisIntegration(enatisrequest, "GetVehicleOwnerTitleHolderConfirmationInformation");
                }
                catch (Exception ex)
                {
                    _logger.LogError($"NatisIntegrationService : GetOwnerTitleHolderConfirmationQuery | Failed to Integrate to Natis | Request Object  : {MaskXMLPassword(enatisrequest.ToString())} | Exception : {ex}");
                }

                try
                {
                    if (vehicleOwnerVerification == "")
                    {
                        vehicleOwnerTitleHolderResult.BaseResponse = DefaultVPNDownResponse();
                        return vehicleOwnerTitleHolderResult;
                    }

                    vehicleOwnerTitleHolderResult = await _vehicleOwnerVerificationIntegrationService.ConvertNatisVehicleOwnerVerificationResponseXML(vehicleOwnerVerification);
                }
                catch (Exception ex)
                {
                    _logger.LogError($"NatisIntegrationService : GetDriverQuery | Failed to Convert DriverInformation Object from Natis Response | Request Object  : {MaskXMLPassword(enatisrequest.ToString())} | Natis Response : {vehicleOwnerVerification} | Exception : {ex}");
                }

            }

            return vehicleOwnerTitleHolderResult;
        }

        /// <summary>
        /// Owner Title Holder Transfer
        /// </summary>
        /// <param name="driverInformationRequest"></param>
        /// <returns>DriverInformation</returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<DriverInformation> GetDriverQuery(NatisGetDriverInformationRequest driverInformationRequest)
        {
            DriverInformation driverInformationResult = new DriverInformation();
            string driverInformation = "";

            //Convert NatisDriverInformation Object to XML Payload for ENatis
            _logger.LogTrace("NatisIntegrationService : GetDriverQuery | Convert Request to Enatis Request");
            var enatisrequest = _driverIntegrationService.ConvertNatisDriverInformationRequestXML(driverInformationRequest);

            _logger.LogTrace("NatisIntegrationService : GetDriverQuery | Enatis Request" + MaskXMLPassword(enatisrequest.ToString()));
            if (enatisrequest != "")
            {
                //Call Enatis
                _logger.LogTrace("NatisIntegrationService : GetDriverQuery | Call GetDriverInformation : " + MaskXMLPassword(enatisrequest.ToString()));
                //Convert Natis Response to Object
                try
                {
                    driverInformation = await CURLNatisIntegration(enatisrequest, "GetDriverInformation");
                }
                catch (Exception ex)
                {
                    _logger.LogError($"NatisIntegrationService : GetDriverQuery | Failed to Integrate to Natis | Request Object  : {MaskXMLPassword(enatisrequest.ToString())} | Exception : {ex}");
                }

                try
                {

                    if (driverInformation == "")
                    {
                        driverInformationResult.BaseResponse = DefaultVPNDownResponse();
                        return driverInformationResult;
                    }

                    driverInformationResult = await _driverIntegrationService.ConvertNatisDriverInformationResponseXML(driverInformation);
                }
                catch (Exception ex)
                {
                    _logger.LogError($"NatisIntegrationService : GetDriverQuery | Failed to Convert DriverInformation Object from Natis Response | Request Object  : {MaskXMLPassword(enatisrequest.ToString())} | Natis Response : {driverInformation} | Exception : {ex}");
                }
            }

            return driverInformationResult;
        }

        /// <summary>
        /// Register Vehicle Owner
        /// </summary>
        /// <param name="registerVehicleOwnerRequest"></param>
        /// <param name="username"></param>
        /// <param name="password"></param>
        /// <param name="environmentName"></param>
        /// <returns>VehicleOwnerRegistrationInformation</returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<VehicleOwnerRegistrationInformation> RegisterVehicleOwnerQuery(NatisVehicleOwnerRegistrationRequest registerVehicleOwnerRequest, string username, string password, string environmentName, Guid auditLogId)
        {
            VehicleOwnerRegistrationInformation registerVehicleOwnerResult = new VehicleOwnerRegistrationInformation();
            string registerVehicleOwner = "";

            //Final Check to ensure that the Development Environment does not potentially contain a Prod URL.
            if(environmentName != "Production")
            {
                //Check if the End Point contains a Prod in the URL
                if(_serviceOptions.BaseUrl.Contains("prod"))
                {
                    registerVehicleOwnerResult.BaseResponse = new BaseResponse()
                    {
                        Successful = false,
                        Message = "The Development Environment is Pointing to Production for a Sensitive Service!"
                    };
                    return registerVehicleOwnerResult;
                }
            }                

            //Convert NatisDriverInfomration Object to XML Payload for ENatis
            _logger.LogTrace("Reference : " + auditLogId + " | NatisIntegrationService : RegisterVehicleOwnerQuery | Convert Request to Enatis Request");
            var enatisrequest = _vehicleOwnerRegistrationIntegrationService.ConvertNatisRegisterVehicleOwnerRequestXML(registerVehicleOwnerRequest, username, password);

            _logger.LogTrace("Reference : " + auditLogId + " | NatisIntegrationService : RegisterVehicleOwnerQuery | Enatis Request" + MaskXMLPassword(enatisrequest.ToString()));
            if (enatisrequest != "")
            {
                //Call Enatis
                _logger.LogWarning("Reference : " + auditLogId + " | RegisterVehicleOwnerQuery | Request RegisterVehicleOwner : " + MaskXMLPassword(enatisrequest.ToString()));
                //Convert Natis Response to Object
                try
                {

                    registerVehicleOwner = await CURLNatisIntegration(enatisrequest, "RegisterVehicleOwner");

                    //Testing
                    //registerVehicleOwner = @"<SOAP-ENV:Envelope xmlns:SOAP-ENV=""http://schemas.xmlsoap.org/soap/envelope/""><SOAP-ENV:Header/><SOAP-ENV:Body><ns2:X3141Response xmlns:ns2=""http://tasima/common/ws/schema/"" xmlns:ns4=""https://ws.enatis.co.za/types"" xmlns=""""><ns2:transactionStatus>SUCCESS</ns2:transactionStatus><ns2:result><ns2:RegistrationFeeAmount>224.40</ns2:RegistrationFeeAmount><ns2:ConvenienceFeeAmount>330</ns2:ConvenienceFeeAmount><ns2:TotalRegistrationFeeAmount>554.40</ns2:TotalRegistrationFeeAmount><ns2:PaymentReferenceNumber>49880000000P</ns2:PaymentReferenceNumber><ns2:VehicleParticulars><ns2:RegisterNumber>DKT818A</ns2:RegisterNumber><ns2:VinOrChassis>ABV1017SNSN6P0144</ns2:VinOrChassis><ns2:VehicleCertificateNumber>49880000001G</ns2:VehicleCertificateNumber></ns2:VehicleParticulars><ns2:TitleHolder><ns2:IdDocumentType>04</ns2:IdDocumentType><ns2:IdDocumentNumber>F151307720094</ns2:IdDocumentNumber></ns2:TitleHolder><ns2:Owner><ns2:IdDocumentType>04</ns2:IdDocumentType><ns2:IdDocumentNumber>F151307720094</ns2:IdDocumentNumber></ns2:Owner></ns2:result></ns2:X3141Response></SOAP-ENV:Body></SOAP-ENV:Envelope>";                    
                   
                    _logger.LogError("Reference : " + auditLogId + " | NatisIntegrationService : RegisterVehicleOwnerQuery | Natis Response | Request Object  : " + registerVehicleOwner);
                }
                catch (Exception ex)
                {
                    _logger.LogError($"Reference : {auditLogId} | NatisIntegrationService : RegisterVehicleOwnerQuery | Failed to Integrate to Natis | Request Object  : {MaskXMLPassword(enatisrequest.ToString())} | Exception : {ex}");
                }

                try
                {

                    if (registerVehicleOwner == "")
                    {
                        registerVehicleOwnerResult.BaseResponse = DefaultVPNDownResponse();
                        return registerVehicleOwnerResult;
                    }

                    registerVehicleOwnerResult = await _vehicleOwnerRegistrationIntegrationService.ConvertNatisVehicleOwnerRegistrationResponseXML(registerVehicleOwner);

                }
                catch (Exception ex)
                {
                    _logger.LogError($"Reference : {auditLogId} | NatisIntegrationService : RegisterVehicleOwnerQuery | Failed to Convert VehicleRegistration Object from Natis Response | Request Object  : {MaskXMLPassword(enatisrequest.ToString())} | Natis Response : {registerVehicleOwner} | Exception : {ex}");
                }
            }

            return registerVehicleOwnerResult;
        }

        /// <summary>
        /// Get Owner History of Vehicle
        /// </summary>
        /// <param name="ownershipHistoryRequest"></param>
        /// <returns>OwnershipHistory</returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<OwnershipHistoryData> OwnershipHistoryQuery(NatisOwnershipHistoryRequest ownershipHistoryRequest, string token, string businessRegistrationNumber)
        {

            OwnershipHistoryData ownershipHistoryResult = new OwnershipHistoryData();
            ownershipHistoryResult.result = new Models.OwnershipHistory.Result();
            string ownershipHistory = "";

            //Convert NatisOwnershipHistoryRequest Object to REST Payload for ENatis

            var enatisRequestString = JsonConvert.SerializeObject(ownershipHistoryRequest);

            //Call Enatis
            _logger.LogTrace("NatisIntegrationService : OwnershipHistoryQuery | Call OwnershipHistoryQuery : " + enatisRequestString);

            try
            {
                ownershipHistory = await PostRESTNatisIntegration(token, enatisRequestString, businessRegistrationNumber, _endPointOptions.GetAllOwners);

            }
            catch (Exception ex)
            {
                _logger.LogError($"NatisIntegrationService : OwnershipHistoryQuery | Failed to Integrate to Natis | Request Object  : {enatisRequestString} | Exception : {ex}");
            }

            try
            {

                if (ownershipHistory == "")
                {
                    var failedBaseResponse = DefaultVPNDownResponse();
                    ownershipHistoryResult.result.successful = failedBaseResponse.Successful;
                    ownershipHistoryResult.result.errorMessages = new Object[] { failedBaseResponse.Message };

                    return ownershipHistoryResult;
                }

                ownershipHistoryResult = JsonConvert.DeserializeObject<OwnershipHistoryData>(ownershipHistory);

            }
            catch (Exception ex)
            {
                _logger.LogError($"NatisIntegrationService : OwnershipHistoryQuery | Failed to Convert OwnershipHistory Object from Natis Response | Request Object  : {enatisRequestString} | Natis Response : {ownershipHistory} | Exception : {ex}");
                ownershipHistoryResult.result.successful = false;
                ownershipHistoryResult.result.errorMessages = new Object[] { "Failed to convert OwnershipHistory Response from RTMC" };
            }

            return ownershipHistoryResult;
        }

        /// <summary>
        /// Get Control Number Verification
        /// </summary>
        /// <param name="controlNumberVerificationRequest"></param>
        /// <param name="token"></param>
        /// <param name="businessRegistrationNumber"></param>
        /// <returns></returns>
        public async Task<ControlNumberVerificationData> ControlNumberVerificationQuery(NatisControlNumberVerificationRequest controlNumberVerificationRequest, string token, string businessRegistrationNumber)
        {

            ControlNumberVerificationData controlNumberVerificationResult = new ControlNumberVerificationData();
            string controlNumberVerification = "";

            //Convert ControlNumberVerificationRequest Object to REST Payload for ENatis

            var enatisRequestString = JsonConvert.SerializeObject(controlNumberVerificationRequest);

            //Call Enatis
            _logger.LogTrace("NatisIntegrationService : ControlNumberVerificationQuery | Call ControlNumberVerificationQuery : " + enatisRequestString);

            try
            {
                controlNumberVerification = await PostRESTNatisIntegration(token, enatisRequestString, businessRegistrationNumber, _endPointOptions.ControlNumberVerification);
            }
            catch (Exception ex)
            {  
                _logger.LogError($"NatisIntegrationService : ControlNumberVerificationQuery | Failed to Integrate to Natis | Request Object  : {enatisRequestString} | Exception : {ex}");
            }

            try
            {

                if (controlNumberVerification == "")
                {
                    var failedBaseResponse = DefaultVPNDownResponse();
                    controlNumberVerificationResult.result.successful = failedBaseResponse.Successful;
                    controlNumberVerificationResult.result.errorMessages = new Object[] { failedBaseResponse.Message };

                    return controlNumberVerificationResult;
                }

                controlNumberVerificationResult = JsonConvert.DeserializeObject<ControlNumberVerificationData>(controlNumberVerification);

            }
            catch (Exception ex)
            {
                _logger.LogError($"NatisIntegrationService : ControlNumberVerificationQuery | Failed to Convert ControlNumberVerification Object from Natis Response | Request Object  : {enatisRequestString} | Natis Response : {controlNumberVerification} | Exception : {ex}");
                controlNumberVerificationResult.result.successful = false;
                controlNumberVerificationResult.result.errorMessages = new Object[] { "Failed to convert ControlNumberVerification Response from RTMC" };
            }

            return controlNumberVerificationResult;
        }

        /// <summary>
        /// Get Licence Fee Calculation
        /// </summary>
        /// <param name="licenceFeeCalculationRequest"></param>
        /// <param name="token"></param>
        /// <param name="businessRegistrationNumber"></param>
        /// <returns></returns>
        public async Task<CalculateVehicleLicenceFeeData> CalculateVehicleLicenceFeeQuery(NatisCalculateVehicleLicenceFeeRequest licenceFeeCalculationRequest, string token, string businessRegistrationNumber)
        {

            CalculateVehicleLicenceFeeData licenceFeeVerificationResult = new CalculateVehicleLicenceFeeData();
            licenceFeeVerificationResult.result = new Models.CalculateVehicleLicenceFee.Result();
            string licenceFeeVerification = "";

            //Convert LicenceFeeCalculationRequest Object to REST Payload for ENatis

            var enatisRequestString = JsonConvert.SerializeObject(licenceFeeCalculationRequest);

            //Call Enatis
            _logger.LogTrace("NatisIntegrationService : CalculateVehicleLicenceFeeQuery | Call CalculateVehicleLicenceFeeQuery : " + enatisRequestString);

            try
            {
                licenceFeeVerification = await PostRESTNatisIntegration(token, enatisRequestString, businessRegistrationNumber, _endPointOptions.FeeCalculator);
            }
            catch (Exception ex)
            {  
                _logger.LogError($"NatisIntegrationService : CalculateVehicleLicenceFeeQuery | Failed to Integrate to Natis | Request Object  : {enatisRequestString} | Exception : {ex}");
            }

            try
            {

                if (licenceFeeVerification == "")
                {
                    var failedBaseResponse = DefaultVPNDownResponse();
                    licenceFeeVerificationResult.result.successful = failedBaseResponse.Successful;
                    licenceFeeVerificationResult.result.errorMessages = (new List<ErrorMessage>(){ new ErrorMessage(){ message = failedBaseResponse.Message}}).ToArray();

                    return licenceFeeVerificationResult;
                }

                licenceFeeVerificationResult = JsonConvert.DeserializeObject<CalculateVehicleLicenceFeeData>(licenceFeeVerification);

            }
            catch (Exception ex)
            {
                _logger.LogError($"NatisIntegrationService : CalculateVehicleLicenceFeeQuery | Failed to Convert LicenceFeeCalculation Object from Natis Response | Request Object  : {enatisRequestString} | Natis Response : {licenceFeeVerification} | Exception : {ex}");
                licenceFeeVerificationResult.result.successful = false;
                licenceFeeVerificationResult.result.errorMessages = (new List<ErrorMessage>(){ new ErrorMessage(){ message = "Failed to convert LicenceFeeCalculation Response from RTMC"}}).ToArray();
            }

            return licenceFeeVerificationResult;
        }

        /// <summary>
        /// Online NCO
        /// </summary>
        /// <param name="onlineNCORequest"></param>
        /// <param name="username"></param>
        /// <param name="password"></param>
        /// <param name="environmentName"></param>
        /// <returns>OnlineNCOInformation</returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<OnlineNCOInformation> OnlineNCOQuery(NatisOnlineNCORequest onlineNCORequest, string username, string password, string environmentName, Guid auditLogId)
        {

            OnlineNCOInformation onlineNCOInformationResult = new OnlineNCOInformation();
            string onlineNCO = "";

            //Final Check to ensure that the Development Environment does not potentially contain a Prod URL.
            if(environmentName != "Production")
            {
                //Check if the End Point contains a Prod in the URL
                if(_serviceOptions.BaseUrl.Contains("prod"))
                {
                    onlineNCOInformationResult.BaseResponse = new BaseResponse()
                    {
                        Successful = false,
                        Message = "The Development Environment is Pointing to Production for a Sensitive Service!"
                    };
                    return onlineNCOInformationResult;
                }
            }                

            //Convert NatisOnlineNCOInfomration Object to XML Payload for ENatis
            _logger.LogTrace("Reference : " + auditLogId + " | NatisIntegrationService : OnlineNCOQuery | Convert Request to Enatis Request");
            var enatisrequest = _onlineNCOIntegrationService.ConvertNatisOnlineNCORequestXML(onlineNCORequest, username, password);

            _logger.LogTrace("Reference : " + auditLogId + " | NatisIntegrationService : OnlineNCOQuery | Enatis Request" + MaskXMLPassword(enatisrequest.ToString()));
            if (enatisrequest != "")
            {
                //Call Enatis
                _logger.LogTrace("Reference : " + auditLogId + " | NatisIntegrationService : OnlineNCOQuery | Call OnlineNCO : " + MaskXMLPassword(enatisrequest.ToString()));
                //Convert Natis Response to Object
                try
                {
                    onlineNCO = await CURLNatisIntegration(enatisrequest, "OnlineNCO");

                    //onlineNCO = "<SOAP-ENV:Envelope xmlns:SOAP-ENV=\"http://schemas.xmlsoap.org/soap/envelope/\"><SOAP-ENV:Header/><SOAP-ENV:Body><ns2:X314AResponse xmlns:ns2=\"http://tasima/common/ws/schema/\" xmlns:ns4=\"https://ws.enatis.co.za/types\" xmlns=\"\"><ns2:transactionStatus>SUCCESS</ns2:transactionStatus><ns2:result><ns2:RC2><ns2:RegAuthority>4009 Boksburg</ns2:RegAuthority><ns2:RegisterNumber>BBB027N</ns2:RegisterNumber><ns2:VinOrChassis>ABM60100080000286</ns2:VinOrChassis><ns2:EngineNumber>CRWABM60100080000286</ns2:EngineNumber><ns2:Make>BMW</ns2:Make><ns2:ModelName>X-5 4.4I AUTOMATIC</ns2:ModelName><ns2:VehicleCategory>Light passenger mv(less than 12 persons)</ns2:VehicleCategory><ns2:Driven>Self-propelled / Selfgedrewe</ns2:Driven><ns2:VehicleDescription>Sedan (closed top) / Sedan (toe-kap)</ns2:VehicleDescription><ns2:Tare>2700</ns2:Tare><ns2:FirstLicenceLiabiltyDate>2006-12-18</ns2:FirstLicenceLiabiltyDate><ns2:VehicleLifeStatus>New / Nuut</ns2:VehicleLifeStatus><ns2:Seller><ns2:IdDocumentType>Business reg certif / Besighd reg sertif</ns2:IdDocumentType><ns2:IdDocumentNumber>F151307720094</ns2:IdDocumentNumber><ns2:CountryOfIssue>South Africa</ns2:CountryOfIssue><ns2:Name>WE BUY CARS ONLINE</ns2:Name></ns2:Seller><ns2:Purchaser><ns2:IdDocumentType>RSA ID document / RSA ID dokument</ns2:IdDocumentType><ns2:IdDocumentNumber>6903150286085</ns2:IdDocumentNumber><ns2:CountryOfIssue>South Africa</ns2:CountryOfIssue><ns2:Name>J DE MARCO</ns2:Name></ns2:Purchaser><ns2:FirstRegistrationLiabiltyDate>2006-12-18</ns2:FirstRegistrationLiabiltyDate><ns2:IssueDate>2023-06-30</ns2:IssueDate><ns2:IssuedBy>WE BUY CARS ONLINE</ns2:IssuedBy><ns2:VehicleCertificateNumber>498800000017</ns2:VehicleCertificateNumber><ns2:Barcode>JVJDMjU3JTE4MSU0OTg4QTAwMSUxJTQ5ODgwMDAwMDAxNyVCQkIwMjdOJVNlZGFuIChjbG9zZWQgdG9wKSAvIFNlZGFuICh0b2Uta2FwKSVCTVclWC01IDQuNEkgQVVUT01BVElDJUFCTTYwMTAwMDgwMDAwMjg2JUNSV0FCTTYwMTAwMDgwMDAwMjg2JU5ldyAvIE51dXQlMjAyMy0wNi0zMCUwNCVGMTUxMzA3NzIwMDk0JVdFIEJVWSBDQVJTIE9OTElORSUlJSU=</ns2:Barcode><ns2:UserGroupCode>4988</ns2:UserGroupCode><ns2:DateTime>2023-06-30T13:41:33</ns2:DateTime></ns2:RC2></ns2:result></ns2:X314AResponse></SOAP-ENV:Body></SOAP-ENV:Envelope>";

                    //onlineNCO = @"<SOAP-ENV:Envelope xmlns:SOAP-ENV='http://schemas.xmlsoap.org/soap/envelope/'><SOAP-ENV:Header/><SOAP-ENV:Body><ns2:X314AResponse xmlns:ns2='http://tasima/common/ws/schema/' xmlns:ns4='https://ws.enatis.co.za/types' xmlns=''><ns2:transactionStatus>SUCCESS</ns2:transactionStatus><ns2:result><ns2:RC2><ns2:RegAuthority>4988 WE BUY CARS ONLINE</ns2:RegAuthority><ns2:RegisterNumber>NTC040W</ns2:RegisterNumber><ns2:VinOrChassis>WDC1641222A689452</ns2:VinOrChassis><ns2:EngineNumber>64294041068609</ns2:EngineNumber><ns2:Make>MERCEDES-BENZ</ns2:Make><ns2:ModelName>W164</ns2:ModelName><ns2:VehicleCategory>Light passenger mv(less than 12 persons)</ns2:VehicleCategory><ns2:Driven>Self-propelled / Selfgedrewe</ns2:Driven><ns2:VehicleDescription>Station wagon / Stasiewa</ns2:VehicleDescription><ns2:Tare>2185</ns2:Tare><ns2:FirstLicenceLiabiltyDate>2011-04-06</ns2:FirstLicenceLiabiltyDate><ns2:VehicleLifeStatus>Used / Gebruik</ns2:VehicleLifeStatus><ns2:Seller><ns2:IdDocumentType>Business reg certif / Besighd reg sertif</ns2:IdDocumentType><ns2:IdDocumentNumber>F151307720094</ns2:IdDocumentNumber><ns2:CountryOfIssue>South Africa</ns2:CountryOfIssue><ns2:Name>WE BUY CARS ONLINE</ns2:Name></ns2:Seller><ns2:Purchaser><ns2:IdDocumentType>Business reg certif / Besighd reg sertif</ns2:IdDocumentType><ns2:IdDocumentNumber>F151024200016</ns2:IdDocumentNumber><ns2:CountryOfIssue>South Africa</ns2:CountryOfIssue><ns2:Name>WE BUY CARS (PTY) LTD</ns2:Name></ns2:Purchaser><ns2:FirstRegistrationLiabiltyDate>2011-02-08</ns2:FirstRegistrationLiabiltyDate><ns2:IssueDate>2023-06-29</ns2:IssueDate><ns2:IssuedBy>WE BUY CARS ONLINE</ns2:IssuedBy><ns2:VehicleCertificateNumber>49880000000T</ns2:VehicleCertificateNumber><ns2:Barcode>JVJDMjgzJTE2MyU0OTg4QTAwMSUxJTQ5ODgwMDAwMDAwVCVOVEMwNDBXJVN0YXRpb24gd2Fnb24gLyBTdGFzaWV3YSVNRVJDRURFUy1CRU5aJVcxNjQlV0RDMTY0MTIyMkE2ODk0NTIlNjQyOTQwNDEwNjg2MDklVXNlZCAvIEdlYnJ1aWslMjAyMy0wNi0yOSUwNCVGMTUxMzA3NzIwMDk0JVdFIEJVWSBDQVJTIE9OTElORSUlJSU=</ns2:Barcode><ns2:UserGroupCode>4988</ns2:UserGroupCode><ns2:DateTime>2023-06-29T09:40:30</ns2:DateTime></ns2:RC2></ns2:result></ns2:X314AResponse></SOAP-ENV:Body></SOAP-ENV:Envelope>";

                    //onlineNCO = @"<SOAP-ENV:Envelope xmlns:SOAP-ENV='http://schemas.xmlsoap.org/soap/envelope/'><SOAP-ENV:Header/><SOAP-ENV:Body><ns2:X314AResponse xmlns:ns2='http://tasima/common/ws/schema/' xmlns:ns4='https://ws.enatis.co.za/types' xmlns=''><ns2:transactionStatus>SUCCESS</ns2:transactionStatus><ns2:result><ns2:RC2><ns2:RegAuthority>4988 WE BUY CARS ONLINE</ns2:RegAuthority><ns2:RegisterNumber>DKT818A</ns2:RegisterNumber><ns2:VinOrChassis>ABV1017SNSN6P0144</ns2:VinOrChassis><ns2:EngineNumber>NULL</ns2:EngineNumber><ns2:Make>VENTER</ns2:Make><ns2:ModelName>0000</ns2:ModelName><ns2:VehicleCategory>Light load vehicle (GVM 3500Kg or less)</ns2:VehicleCategory><ns2:Driven>Trailer / Sleepwa</ns2:Driven><ns2:VehicleDescription>Unknown / Onbekend</ns2:VehicleDescription><ns2:Tare>159</ns2:Tare><ns2:FirstLicenceLiabiltyDate>1995-01-01</ns2:FirstLicenceLiabiltyDate><ns2:VehicleLifeStatus>Used / Gebruik</ns2:VehicleLifeStatus><ns2:Seller><ns2:IdDocumentType>Business reg certif / Besighd reg sertif</ns2:IdDocumentType><ns2:IdDocumentNumber>F151307720094</ns2:IdDocumentNumber><ns2:CountryOfIssue>South Africa</ns2:CountryOfIssue><ns2:Name>WE BUY CARS ONLINE</ns2:Name></ns2:Seller><ns2:Purchaser><ns2:IdDocumentType>RSA ID document / RSA ID dokument</ns2:IdDocumentType><ns2:IdDocumentNumber>9501030037087</ns2:IdDocumentNumber><ns2:CountryOfIssue>South Africa / Suid-Afrika</ns2:CountryOfIssue><ns2:Name>HATTINGH</ns2:Name></ns2:Purchaser><ns2:FirstRegistrationLiabiltyDate>1995-01- 01</ns2:FirstRegistrationLiabiltyDate><ns2:IssueDate>2023-08-12</ns2:IssueDate><ns2:IssuedBy>WE BUY CARS ONLINE</ns2:IssuedBy><ns2:VehicleCertificateNumber>49880000001H</ns2:VehicleCertificateNumber><ns2:Barcode>JVJDMjgzJTE2MyU0OTg4QTAwMSUxJTQ5ODgwMDAwMDAwVCVES1Q4MThBJVRyYWlsZXIgLyBTbGVlcHdhJVZFTlRFUiUwMDAwJUFCVjEwMTdTTlNONlAwMTQ0JSVVc2VkIC8gR2VicnVpayUyMDIzLTA4LTEyJTA0JUYxNTEzMDc3MjAwOTQlV0UgQlVZIENBUlMgT05MSU5FJSUlJQ==</ns2:Barcode><ns2:UserGroupCode>4988</ns2:UserGroupCode><ns2:DateTime>2023-09-12T10:00:13</ns2:DateTime></ns2:RC2></ns2:result></ns2:X314AResponse></SOAP-ENV:Body></SOAP-ENV:Envelope>";

                    _logger.LogError("Reference : " + auditLogId + " | NatisIntegrationService : OnlineNCOQuery | Natis Response | Response Object  : " + onlineNCO);

                }
                catch (Exception ex)
                {
                    _logger.LogError($"Reference : {auditLogId} | NatisIntegrationService : OnlineNCOQuery | Failed to Integrate to Natis | Request Object  : {MaskXMLPassword(enatisrequest.ToString())} | Exception : {ex}");
                }

                try
                {

                    if (onlineNCO == "")
                    {
                        onlineNCOInformationResult.BaseResponse = DefaultVPNDownResponse();
                        return onlineNCOInformationResult;
                    }

                    onlineNCOInformationResult = await _onlineNCOIntegrationService.ConvertNatisOnlineNCOInformationResponseXML(onlineNCO);
                }
                catch (Exception ex)
                {
                    _logger.LogError($"Reference : {auditLogId} | NatisIntegrationService : OnlineNCOQuery | Failed to Convert Online NCO Object from Natis Response | Request Object  : {MaskXMLPassword(enatisrequest.ToString())} | Natis Response : {onlineNCO} | Exception : {ex}");
                }
            }

            //Remove after modifying Testing
            // onlineNCOInformationResult.RegAuthority = "4009 Boksburg";
            // onlineNCOInformationResult.RegisterNumber = "BBB180N";
            // onlineNCOInformationResult.VinOrChassis = "ABM60100080000555";
            // onlineNCOInformationResult.EngineNumber = "CRWABM60100080000555";
            // onlineNCOInformationResult.Make = "BMW";
            // onlineNCOInformationResult.ModelName = "X-5 4.4I AUTOMATIC";
            // onlineNCOInformationResult.VehicleCategory = "Light passenger mv(less than 12 persons)";
            // onlineNCOInformationResult.Driven = "Self-propelled / Selfgedrewe";
            // onlineNCOInformationResult.VehicleDescription = "Sedan (closed top) / Sedan (toe-kap)";
            // onlineNCOInformationResult.Tare = "2700";
            // onlineNCOInformationResult.FirstLicenceLiabilityDate = "01/23/2007 00:00:00";
            // onlineNCOInformationResult.VehicleLifeStatus = "New / Nuut";
            // onlineNCOInformationResult.SellerIdDocumentType = "Business reg certif / Besighd reg sertif";
            // onlineNCOInformationResult.SellerIdDocumentNumber = "F151307720094";
            // onlineNCOInformationResult.SellerName = "South Africa";
            // onlineNCOInformationResult.PurchaserIdDocumentType = "RSA ID document / RSA ID dokument";
            // onlineNCOInformationResult.PurchaserName = "S SHELLY";
            // onlineNCOInformationResult.PurchaserIdDocumentNumber = "8304010266086";
            // onlineNCOInformationResult.PurchaserCountryOfIssue = "South Africa";
            // onlineNCOInformationResult.FirstRegistrationLiabilityDate = "01/23/2007 00:00:00";
            // onlineNCOInformationResult.IssueDate = "06/27/2023 00:00:00";
            // onlineNCOInformationResult.IssuedBy = "WE BUY CARS ONLINE";
            // onlineNCOInformationResult.VehicleCertificateNumber = "498800000011";
            // onlineNCOInformationResult.Barcode = "JVJDMjUxJTE4MSU0OTg4QTAwMSUxJTQ5ODgwMDAwMDAxMSVCQkIxODBOJVNlZGFuIChjbG9zZWQgdG9wKSAvIFNlZGFuICh0b2Uta2FwKSVCTVclWC01IDQuNEkgQVVUT01BVElDJUFCTTYwMTAwMDgwMDAwNTU1JUNSV0FCTTYwMTAwMDgwMDAwNTU1JU5ldyAvIE51dXQlMjAyMy0wNi0yNyUwNCVGMTUxMzA3NzIwMDk0JVdFIEJVWSBDQVJTIE9OTElORSUlJSU=";
            // onlineNCOInformationResult.UserGroupCode = null;
            // onlineNCOInformationResult.DateTime = null;
            // onlineNCOInformationResult.Watermark = null;

            // BaseResponse baseResponse = new BaseResponse(){
            //     Successful = true,
            //     Message = null,
            //     Status = "Success",
            //     StatusCode = "Success"
            // };

            // onlineNCOInformationResult.BaseResponse = baseResponse;

            return onlineNCOInformationResult;
        }
        
        /// <summary>
        /// Get Vehicles and License Expiry Dates
        /// </summary>
        /// <param name="vehiclesAndLicenseExpiryDatesRequest"></param>
        /// <param name="token"></param>
        /// <param name="businessRegistrationNumber"></param>
        /// <returns></returns>
        public async Task<VehiclesLicenceExpiryData> GetVehiclesAndLicenseExpiryDatesQuery(NatisGetVehiclesAndLicenceExpiryDatesRequest vehiclesAndLicenseExpiryDatesRequest, string token, string businessRegistrationNumber)
        {
            var vehiclesLicenseExpiryData = new VehiclesLicenceExpiryData
            {
                result = new VehiclesLicenceExpiryData.Result()
            };
            var enatisRequestString = JsonConvert.SerializeObject(vehiclesAndLicenseExpiryDatesRequest);
            var vehicleLicenseExpiryResponse = string.Empty;
            
            try
            {
                vehicleLicenseExpiryResponse = await PostRESTNatisIntegration(token, enatisRequestString, businessRegistrationNumber, _endPointOptions.GetVehiclesAndLicenseExpiryDates);
            }
            catch (Exception ex)
            {
                _logger.LogError("GetVehiclesAndLicenseExpiryDatesQuery Request Failed: {EnatisRequestString} | Exception : {Exception}", enatisRequestString, ex);
            }

            try
            {
                if (vehicleLicenseExpiryResponse == "")
                {
                    var failedBaseResponse = DefaultVPNDownResponse();
                    vehiclesLicenseExpiryData.result.successful = failedBaseResponse.Successful;
                    vehiclesLicenseExpiryData.result.errorMessages = new List<ErrorMessage> { new() { message = failedBaseResponse.Message } }.ToArray();
                    return vehiclesLicenseExpiryData;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("GetVehiclesAndLicenseExpiryDatesQuery Failed: Natis Response : {VehicleLicenseExpiryResponse} | Exception : {Exception}", vehicleLicenseExpiryResponse, ex);
                vehiclesLicenseExpiryData.result.successful = false;
                vehiclesLicenseExpiryData.result.errorMessages = new List<ErrorMessage> { new(){ message = "Failed to convert VehiclesAndLicenseExpiryDates Response from RTMC"}}.ToArray();
            }
            return vehiclesLicenseExpiryData;
        }

        /// <summary>
        /// Get Vehicles Quotation and Total amount required including the delivery fee
        /// </summary>
        /// <param name="vehiclesQuotationRequest"></param>
        /// <param name="token"></param>
        /// <param name="businessRegistrationNumber"></param>
        /// <returns></returns>
        public async Task<VehiclesQuotationData> GetVehiclesLicenceRenewalQuotationQuery(NatisGetVehiclesQuotationRequest vehiclesQuotationRequest, string token, string businessRegistrationNumber)
        {
            var vehiclesQuotationData = new VehiclesQuotationData
            {
                result = new VehiclesQuotationData.Result()
            };
            var enatisRequestString = JsonConvert.SerializeObject(vehiclesQuotationRequest);
            var vehiclesQuotationResponse = string.Empty;

            try
            {
                vehiclesQuotationResponse = await PostRESTNatisIntegration(token, enatisRequestString, businessRegistrationNumber,_endPointOptions.GetVehiclesQuotationRequest);
            }
            catch (Exception ex)
            {
                _logger.LogError("GetVehiclesQuotationQuery Request Failed: {EnatisRequestString} | Exception : {Exception}", enatisRequestString, ex);
            }

            try
            {
                if (vehiclesQuotationResponse == "")
                {
                    var failedBaseResponse = DefaultVPNDownResponse();
                    vehiclesQuotationData.result.successful = failedBaseResponse.Successful;
                    vehiclesQuotationData.result.errorMessages = new List<ErrorMessage> { new() { message = failedBaseResponse.Message } }.ToArray();
                    return vehiclesQuotationData;
                }

                vehiclesQuotationData = JsonConvert.DeserializeObject<VehiclesQuotationData>(vehiclesQuotationResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError("GetVehiclesQuotationQuery Failed: Natis Response : {VehicleLicenseExpiryResponse} | Exception : {Exception}", vehiclesQuotationResponse, ex);
                vehiclesQuotationData.result.successful = false;
                vehiclesQuotationData.result.errorMessages = new List<ErrorMessage> { new() { message = "Failed to convert Vehicles Quotations Response from RTMC" } }.ToArray();
            }

            return vehiclesQuotationData;
        }

        /// <summary>
        /// Initiate Vehicle License Renewal
        /// </summary>
        /// <param name="vehicleLicenseRenewalRequest"></param>
        /// <param name="token"></param>
        /// <param name="businessRegistrationNumber"></param>
        /// <returns></returns>
        public async Task<InitiateRenewalData> InitiateVehicleLicenseRenewalQuery(NatisInitiateRenewalRequest vehicleLicenseRenewalRequest, string token, string businessRegistrationNumber)
        {
            var vehicleLicenseRenewalData = new InitiateRenewalData
            {
                result = new InitiateRenewalData.Result()
            };
            var enatisRequestString = JsonConvert.SerializeObject(vehicleLicenseRenewalRequest);
            var vehicleLicenseRenewalResponse = string.Empty;

            try
            {
                vehicleLicenseRenewalResponse = await PostRESTNatisIntegration(token, enatisRequestString, businessRegistrationNumber, _endPointOptions.InitiateRenewalRequest);
            }
            catch (Exception ex)
            {
                _logger.LogError("InitiateVehicleLicenseRenewalQuery Request Failed: {EnatisRequestString} | Exception : {Exception}", enatisRequestString, ex);
            }

            try
            {
                if (vehicleLicenseRenewalResponse == "")
                {
                    var failedBaseResponse = DefaultVPNDownResponse();
                    vehicleLicenseRenewalData.result.successful = failedBaseResponse.Successful;
                    vehicleLicenseRenewalData.result.errorMessages = new List<ErrorMessage> { new() { message = failedBaseResponse.Message } }.ToArray();
                    return vehicleLicenseRenewalData;
                }

                vehicleLicenseRenewalData = JsonConvert.DeserializeObject<InitiateRenewalData>(vehicleLicenseRenewalResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError("InitiateVehicleLicenseRenewalQuery Failed: Natis Response : {VehicleLicenseExpiryResponse} | Exception : {Exception}", vehicleLicenseRenewalResponse, ex);
                vehicleLicenseRenewalData.result.successful = false;
                vehicleLicenseRenewalData.result.errorMessages = new List<ErrorMessage> { new() { message = "Failed to convert Vehicle License Initiate Renewal Response from RTMC" } }.ToArray();
            }

            return vehicleLicenseRenewalData;
        }

        /// <summary>
        /// Complete Vehicle License Renewal
        /// </summary>
        /// <param name="vehicleLicenseRenewalCompleteRequest"></param>
        /// <param name="token"></param>
        /// <param name="businessRegistrationNumber"></param>
        /// <returns></returns>
        public async Task<CompleteRenewalData> CompleteVehicleLicenseRenewalQuery(NatisCompleteRenewalRequest vehicleLicenseRenewalCompleteRequest, string token, string businessRegistrationNumber)
        {
            var vehicleLicenseRenewalData = new CompleteRenewalData
            {
                result = new CompleteRenewalData.Result()
            };
            var enatisRequestString = JsonConvert.SerializeObject(vehicleLicenseRenewalCompleteRequest);
            var vehicleLicenseCompleteRenewalResponse = string.Empty;

            try
            {
                vehicleLicenseCompleteRenewalResponse = await PostRESTNatisIntegration(token, enatisRequestString, businessRegistrationNumber, _endPointOptions.CompleteRenewalRequest);
            }
            catch (Exception ex)
            {
                _logger.LogError("InitiateVehicleLicenseRenewalQuery Request Failed: {EnatisRequestString} | Exception : {Exception}", enatisRequestString, ex);
            }

            try
            {
                if (vehicleLicenseCompleteRenewalResponse == "")
                {
                    var failedBaseResponse = DefaultVPNDownResponse();
                    vehicleLicenseRenewalData.result.successful = failedBaseResponse.Successful;
                    vehicleLicenseRenewalData.result.errorMessages = new List<ErrorMessage> { new() { message = failedBaseResponse.Message } }.ToArray();
                    return vehicleLicenseRenewalData;
                }

                vehicleLicenseRenewalData = JsonConvert.DeserializeObject<CompleteRenewalData>(vehicleLicenseCompleteRenewalResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError("CompleteVehicleLicenseRenewalQuery Failed: Natis Response : {VehicleLicenseExpiryResponse} | Exception : {Exception}", vehicleLicenseCompleteRenewalResponse, ex);
                vehicleLicenseRenewalData.result.successful = false;
                vehicleLicenseRenewalData.result.errorMessages = new List<ErrorMessage> { new() { message = "Failed to convert Vehicle License Renewal Complete Response from RTMC" } }.ToArray();
            }

            return vehicleLicenseRenewalData;
        }

        #endregion

        #region private

        /// <summary>
        /// CURL Natis Integration
        /// </summary>
        /// <param name="enatisrequest"></param>
        /// <param name="method"></param>
        /// <returns></returns>
        /// <exception cref="InfrastructureException"></exception>
        private async Task<string> CURLNatisIntegration(string enatisrequest, string method, Guid? auditLogId = null)
        {

            var enatisresult = "";

            try
            {

                var command = "sh";
                ProcessStartInfo startinfo = new ProcessStartInfo();
                startinfo.FileName = command;

                _logger.LogWarning($"Reference : {auditLogId} | Service : NatisIntegrationService | Method : {method} | Retrieve Path : " + MaskXMLPassword(enatisrequest.ToString()));
                //Append the XML file to the path
                // string path = Directory.GetParent(Directory.GetCurrentDirectory()).Parent.FullName;
                string path = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);

                _logger.LogWarning($"Reference : {auditLogId} | Service : NatisIntegrationService | Method : {method} | Path : " + path.ToString());


                startinfo.ArgumentList.Add($"{path}/{_serviceOptions.PathExtension}");

                //Local Path
                //startinfo.ArgumentList.Add($"{path}/Scripts/curl_local.sh");

                //Prod Path
                //startinfo.ArgumentList.Add($"{path}/Scripts/curl.sh");

                startinfo.ArgumentList.Add(enatisrequest);
                startinfo.ArgumentList.Add(_serviceOptions.BaseUrl);

                Process process = new Process();
                process.StartInfo = startinfo;
                process.StartInfo.UseShellExecute = false;

                process.StartInfo.RedirectStandardInput = true;
                process.StartInfo.RedirectStandardOutput = true;

                _logger.LogTrace($"Reference : {auditLogId} | Service : NatisIntegrationService | Method : {method} | Start Process");

                process.Start();

                process.StandardInput.WriteLine("ls -ltr /opt/*.tmp");
                process.StandardInput.WriteLine("exit");

                 _logger.LogWarning($"Reference : {auditLogId} | Service : NatisIntegrationService | Method : {method} | Get Standard Output");
                while (!process.StandardOutput.EndOfStream)
                {
                    string line = process.StandardOutput.ReadLine();
                    enatisresult = enatisresult + line;
                }

                 _logger.LogWarning($"Reference : {auditLogId} | Service : NatisIntegrationService | Method : {method} | Return ENatis Results : " + MaskXMLPassword(enatisresult.ToString()));
                return enatisresult;

            }
            catch (Exception ex)
            {
                //Throw Error indicating that the Integration towards ENatis 
                 _logger.LogError($"Reference : {auditLogId} |Error : Service : NatisIntegrationService | Method : {method} | Problem with Enatis Response Exception : " + ex.ToString());
                throw new InfrastructureException($"Error : Service : NatisIntegrationService | Method : {method} | Problem with Enatis Response Exception");
            }

        }

        /// <summary>
        /// Rest Client for Natis Integration
        /// </summary>
        /// <param name="token"></param>
        /// <param name="request"></param>
        /// <param name="businessRegistrationNumber"></param>
        /// <param name="endpointURL"></param>
        /// <returns></returns>
        private async Task<string> PostRESTNatisIntegration(string token, string request, string businessRegistrationNumber, string endpointURL)
        {

            string result = "";

            try
            {
                var baseUrl = _serviceOptions.RESTBaseUrl + endpointURL;
                _httpClient.Timeout = TimeSpan.FromSeconds(20);
                _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                StringContent querystring = new StringContent(request, Encoding.UTF8, "application/json");
                HttpResponseMessage httpResponseMessage = await _httpClient.PostAsync(baseUrl, querystring).ConfigureAwait(false);
                result = await httpResponseMessage.Content.ReadAsStringAsync();

                // var test = _serviceOptions.RESTBaseUrl + endpointURL;

                // var cacheKey = businessRegistrationNumber;

                // HttpClient httpClient = new HttpClient();

                // var httpClientHandler = new HttpClientHandler();

                // httpClientHandler.ServerCertificateCustomValidationCallback = (message, cert, chain, sslPolicyErrors) =>
                // {
                //     return true;
                // };

                // httpClient = new HttpClient(httpClientHandler) { BaseAddress = new Uri(endpointURL) };
                // httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                // httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

                // StringContent querystring = new StringContent(request, Encoding.UTF8, "application/json");

                // HttpResponseMessage httpResponseMessage = await httpClient.PostAsync(test,querystring).ConfigureAwait(false);

                // result = httpResponseMessage.Content.ReadAsStringAsync().Result;

                _logger.LogWarning($"NatisIntegrationService : PostRESTNatisIntegration | Success POST REST Request to Natis | Request Object  : {request} | Result : {result} | BRN : {businessRegistrationNumber}");

            }
            catch (Exception ex)
            {
                _logger.LogError($"NatisIntegrationService : PostRESTNatisIntegration | Failed POST REST Request to Natis | Request Object  : {request} | Result : {result} | BRN : {businessRegistrationNumber} | Exception : {ex}");
                throw ex;
            }

            return result;
        }

        /// <summary>
        /// Remove Payload Password
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private string MaskXMLPassword(string input)
        {

            var result = "";

            try
            {
                var startIndex = input.IndexOf(@"PasswordText");
                var endIndex = input.IndexOf(@"</wsse:Password>");

                if (startIndex < endIndex)
                {
                    result = input.Remove(startIndex + 14, endIndex - startIndex - 14);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error : Unable to Mask Password" + ex.ToString());
            }

            return result;
        }

        /// <summary>
        /// Creating Default Response Base Object for when CURL Script Failed
        /// </summary>
        /// <returns></returns>
        private BaseResponse DefaultVPNDownResponse()
        {
            BaseResponse baseResponse = new BaseResponse()
            {
                Successful = false,
                Message = "RTMC VPN is down"
            };
            return baseResponse;
        }

        #endregion
    }
}