using System;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Financial.Contracts;
using MassTransit;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using WeBuyCars.Financial.Api.V1.Models.Ozow;
using WeBuyCars.Financial.Api.V1.Services.PaymentPlatforms.Ozow.Interfaces;

namespace WeBuyCars.Financial.Api.Masstransit.Ozow.Consumers;

public class OzowRefundNotificationsConsumer : IConsumer<OzowRefundNotificationMessage>
{
    #region Constructors

    public OzowRefundNotificationsConsumer(ILogger<OzowRefundNotificationsConsumer> logger,
        IOzowRefundService ozowRefundService, IMapper mapper)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _ozowRefundService = ozowRefundService ?? throw new ArgumentNullException(nameof(ozowRefundService));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
    }

    #endregion

    #region Methods

    public async Task Consume(ConsumeContext<OzowRefundNotificationMessage> context)
    {
        if (string.IsNullOrEmpty(context.Message.TransactionId))
        {
            _logger.LogError("Cannot Consume OzowRefundNotificationMessage - transaction id is null or empty");
            return;
        }

        _logger.LogInformation("Consuming Ozow Refund Notification Response: {MessageContent}",
            JsonConvert.SerializeObject(context.Message, Formatting.Indented));

        var message = _mapper.Map<RefundNotificationRequest>(context.Message);
        await _ozowRefundService.HandleRefundNotificationAsync(message, CancellationToken.None);
    }

    #endregion

    #region Fields

    private readonly ILogger<OzowRefundNotificationsConsumer> _logger;
    private readonly IOzowRefundService _ozowRefundService;
    private readonly IMapper _mapper;

    #endregion
}