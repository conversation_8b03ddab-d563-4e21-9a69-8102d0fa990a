using System;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Financial.Contracts;
using MassTransit;
using Microsoft.Extensions.Logging;
using WeBuyCars.Financial.Api.V1.Services.Blms;
using WeBuyCars.Financial.Infrastructure.BLMS.Models.Subscriptions;

namespace WeBuyCars.Financial.Api.Masstransit.BLMS.Consumers;

public class BLMSStockItemUpdatedConsumer : IConsumer<BLMSStockItemUpdatedMessage>
{
    #region Constructors

    public BLMSStockItemUpdatedConsumer
    (
        ILogger<BLMSStockItemUpdatedConsumer> logger,
        BlmsStockItemService blmsStockItemService,
        IMapper mapper
    )
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _blmsStockItemService = blmsStockItemService ?? throw new ArgumentNullException(nameof(blmsStockItemService));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
    }

    #endregion

    #region Public Methods

    public async Task Consume(ConsumeContext<BLMSStockItemUpdatedMessage> context)
    {
        if (context.Message.Data.UpdatedValues.FinalOffer == null ||
            context.Message.Data.UpdatedValues.ExternalId == Guid.Empty)
        {
            _logger.LogError(
                "Failed to consume BLMS Stock Item Updated Message - Final Offer: {FinalOffer} or External Id: {ExternalId} is null or empty",
                context.Message.Data.UpdatedValues.FinalOffer,
                context.Message.Data.UpdatedValues.ExternalId);
            return;
        }

        var message = _mapper.Map<StockItemUpdatedDto>(context.Message);
        await _blmsStockItemService.ProcessStockItemUpdated(message, CancellationToken.None);
    }

    #endregion

    #region Fields

    private readonly ILogger<BLMSStockItemUpdatedConsumer> _logger;
    private readonly BlmsStockItemService _blmsStockItemService;
    private readonly IMapper _mapper;

    #endregion
}