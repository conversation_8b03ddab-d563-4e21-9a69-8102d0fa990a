{"MultiTenancy": {"TenantId": "ZA"}, "AppConfig": {"Endpoint": "https://wbc-appconfig-prod.azconfig.io"}, "AzureServiceBusConfig": {"Subscriptions": [{"Id": "fin-events-transaction-status-updated", "ServiceBusNamespaceName": "webuycars", "TopicName": "fin-api-events", "SubscriptionName": "fin-api-transaction-status", "MaxSizeInMegabytes": 1024, "DefaultMessageTimeToLiveInDays": 14, "MaxConcurrentCalls": 2, "AutoCompleteMessages": false, "MaxDeliveryCount": 120, "LockDurationInSeconds": 60, "SqlFilter": "EventType LIKE '%TransactionStatusChange%'"}, {"Id": "blms-events-flag-created", "ServiceBusNamespaceName": "webuycars", "TopicName": "blms-events", "SubscriptionName": "finance-api-flag-created", "MaxSizeInMegabytes": 1024, "DefaultMessageTimeToLiveInDays": 14, "MaxConcurrentCalls": 2, "AutoCompleteMessages": false, "MaxDeliveryCount": 120, "LockDurationInSeconds": 60, "SqlFilter": "EventType LIKE '%LeadFlagCreated%'"}, {"Id": "blms-events-stock-updated", "ServiceBusNamespaceName": "webuycars", "TopicName": "blms-events", "SubscriptionName": "finance-api-stock-updated", "MaxSizeInMegabytes": 1024, "DefaultMessageTimeToLiveInDays": 14, "MaxConcurrentCalls": 2, "AutoCompleteMessages": false, "MaxDeliveryCount": 120, "LockDurationInSeconds": 60, "SqlFilter": "EventType LIKE '%StockItemUpdated%'"}, {"Id": "blms-events-payment-allocated", "ServiceBusNamespaceName": "webuycars", "TopicName": "blms-events", "SubscriptionName": "finance-api-payment-allocated", "MaxSizeInMegabytes": 1024, "DefaultMessageTimeToLiveInDays": 14, "MaxConcurrentCalls": 2, "AutoCompleteMessages": false, "MaxDeliveryCount": 120, "LockDurationInSeconds": 60, "SqlFilter": "EventType LIKE '%LeadPaymentAllocated%'"}, {"Id": "blms-events-lead-status-updated", "ServiceBusNamespaceName": "webuycars", "TopicName": "blms-events", "SubscriptionName": "finance-api-lead-status-updated", "MaxSizeInMegabytes": 1024, "DefaultMessageTimeToLiveInDays": 14, "MaxConcurrentCalls": 2, "AutoCompleteMessages": false, "MaxDeliveryCount": 120, "LockDurationInSeconds": 60, "SqlFilter": "EventType LIKE '%LeadStatusUpdated%'"}]}, "Walletdoc": {"MedianFirstRetryDelay": 2, "RetryCount": 3}, "FeatureFlags": {"EnableHyphen": true, "EnableOnlineAuction": true, "EnableOzow": true, "EnableRtmc": true, "EnableWalletdoc": true, "EnableSourceOfFund": true}}