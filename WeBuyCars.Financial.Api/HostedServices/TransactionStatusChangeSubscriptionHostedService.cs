using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using WeBuyCars.Core.Infrastructure.ServiceBus.Azure;
using WeBuyCars.Core.Infrastructure.ServiceBus.Azure.Configurations;
using WeBuyCars.Core.Infrastructure.ServiceBus.Interface;
using WeBuyCars.Core.SharedKernel;
using WeBuyCars.Financial.Api.V1.Models;
using WeBuyCars.Financial.Api.V1.Services;

namespace WeBuyCars.Financial.Api.HostedServices;

public class TransactionStatusChangeSubscriptionHostedService : HostedService
{
    private const string SubscriptionId = "fin-events-transaction-status-updated";
    private readonly AzureServiceBusSubscriptionSetting _azureServiceBusSubscriptionSetting;
    private readonly IServiceProvider _serviceProvider;

    public TransactionStatusChangeSubscriptionHostedService(IServiceProvider serviceProvider, 
        AzureServiceBusFactory azureServiceBusFactory)
    {
        _serviceProvider = serviceProvider;
        _ = azureServiceBusFactory ?? throw new ArgumentNullException(nameof(azureServiceBusFactory));
        _azureServiceBusSubscriptionSetting = azureServiceBusFactory.GetSubscriptionSettingsById(SubscriptionId);
    }

    protected override async Task ExecuteAsync(CancellationToken cancellationToken)
    {
        await StartProcessingAsync(cancellationToken);
    }

    private async Task StartProcessingAsync(CancellationToken cancellationToken)
    {
        using (var scope = _serviceProvider.CreateScope())
        {
            var processor = scope.ServiceProvider
                .GetRequiredService<IServiceBusSubscriptionProcessor<TransactionEvent, TransactionEventsService>>();

            await processor.StartProcessingAsync(_azureServiceBusSubscriptionSetting.SubscriptionName,
                cancellationToken);
        }
    }
}