using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Financial.Contracts;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using WeBuyCars.Core.Exceptions;
using WeBuyCars.Financial.Api.V1.Models;
using WeBuyCars.Financial.Core.Constants;
using WeBuyCars.Financial.Core.Entities;
using WeBuyCars.Financial.Core.Enumerations;
using WeBuyCars.Financial.Core.SharedKernel;
using WeBuyCars.Financial.Infrastructure.Hyphen.Facs;
using WeBuyCars.Financial.Infrastructure.Messaging.Services.Interfaces;

namespace WeBuyCars.Financial.Api.V1.Services;

public class PaymentsService
{
    #region Constructors

    public PaymentsService(
        ILogger<PaymentsService> logger,
        IHyphenTransactionRespository hyphenTransactionRepository,
        IAccountRepository accountRepository,
        IOutstandingPaymentRepository outstandingPaymentRepository,
        ITransactionRepository transactionRepository,
        I<PERSON>apper mapper,
        IMessageService messageService
    )
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _hyphenTransactionRepository = hyphenTransactionRepository ?? throw new ArgumentNullException(nameof(hyphenTransactionRepository));
        _accountRepository = accountRepository ?? throw new ArgumentNullException(nameof(accountRepository));
        _outstandingPaymentRepository = outstandingPaymentRepository ?? throw new ArgumentNullException(nameof(outstandingPaymentRepository));
        _transactionRepository = transactionRepository ?? throw new ArgumentNullException(nameof(transactionRepository));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        _messageService = messageService ?? throw new ArgumentNullException(nameof(messageService));
    }

    #endregion

    #region Fields

    private readonly ILogger _logger;
    private readonly IHyphenTransactionRespository _hyphenTransactionRepository;
    private readonly IAccountRepository _accountRepository;
    private readonly IOutstandingPaymentRepository _outstandingPaymentRepository;
    private readonly ITransactionRepository _transactionRepository;
    private readonly IMapper _mapper;
    private readonly IMessageService _messageService;

    #endregion

    #region Public Methods

    //Check if Hyphen transaction exists method

    public async Task<Transaction> CreatePaymentTransactionAsync(
        Account account,
        TransactionTypeEnum transactionType,
        TransactionStatusEnum transactionStatus,
        PaymentTransactionMessage message,
        string transactionDescription,
        string providerReference
    )
    {
        return await CreatePaymentTransactionAsync(
            account, 
            transactionType, 
            transactionStatus, 
            (ProviderEnum)message.Provider, 
            message.TransactionReference, 
            message.CurrencyCode, 
            message.Amount, 
            message.BuyLeadReference, 
            message.CreatedBy, 
            message.ActionDate, 
            transactionDescription, 
            providerReference
        );
    }

    public async Task<Transaction> CreatePaymentTransactionAsync(
        Account account,
        TransactionTypeEnum transactionType,
        TransactionStatusEnum transactionStatus,
        ProviderEnum provider,
        Guid transactionReference,
        string currencyCode,
        decimal amount,
        string buyLeadReference,
        string createdBy,
        DateTimeOffset actionDate,
        string transactionDescription,
        string providerReference
    )
    {
        Transaction transaction = null;

        var transactionAccount = await _accountRepository.GetByTransactionReferenceAsync(transactionReference.ToString());

        if (transactionAccount is null)
        {
            using var contextTransaction = _accountRepository.UnitOfWork.BeginTransaction();
            try
            {
                transaction = new Transaction(
                    AccountTypeEnumeration.AccountsPayable,
                    transactionReference.ToString(),
                    currencyCode,
                    amount,
                    transactionDescription,
                    transactionStatus,
                    provider,
                    providerReference,
                    transactionType,
                    null,
                    buyLeadReference,
                    actionDate,
                    createdBy
                );
                account.MakePayment(transaction);
                _accountRepository.Update(account);
                await _accountRepository.UnitOfWork.SaveChangesAsync();

                var outstandingPaymentTransactionHistory = _mapper.Map<OutstandingPaymentTransactionHistory>(transaction);
                outstandingPaymentTransactionHistory.CreatedBy = createdBy;
                _outstandingPaymentRepository.AddOutstandingPaymentHistory(outstandingPaymentTransactionHistory);

                await _outstandingPaymentRepository.UnitOfWork.SaveChangesAsync();

                // Commit transaction if all commands succeed, transaction will auto-rollback when disposed if either commands fails
                await contextTransaction.CommitAsync();
            }
            catch (Exception ex)
            {
                throw new InfrastructureException($"An error occurred while saving the transaction with reference: '{transaction?.TransactionReference}'.", ex);
            }
        }
        else
        {
            transaction = account.Transactions.FirstOrDefault(f => f.TransactionReference == transactionReference.ToString());
        }

        return transaction;
    }

    public async Task<HyphenTransactionDetail> CreateHyphenPaymentTransactionAsync(
        long transactionId, 
        HyphenTransactionTypeEnum hyphenTransactionTypeId, 
        PaymentTransactionMessage message
    )
    {
        return await CreateHyphenPaymentTransactionAsync(
            transactionId, 
            hyphenTransactionTypeId, 
            message.TransactionReference,
            message.DocumentType,
            message.Reference1,
            message.Reference2,
            message.Code1,
            message.Code2,
            message.Amount,
            message.ClientName,
            message.ClientBankAccountHolderType,
            message.ClientBankAccountNumber,
            (BankAccountTypeEnumeration)(Convert.ToInt32(message.ClientBankAccountType)),
            message.ClientBranchCode,
            message.ActionDate,
            message.FediIndicator,
            message.CreatedBy
        );
    }

    private async Task<HyphenTransactionDetail> CreateHyphenPaymentTransactionAsync(
        long transactionId, 
        HyphenTransactionTypeEnum hyphenTransactionTypeId, 
        Guid transactionReference,
        string documentType,
        string reference1,
        string reference2,
        string code1,
        string code2,
        decimal amount,
        string clientName,
        BankAccountHolderTypeEnumeration clientBankAccountHolderType,
        string clientBankAccountNumber,
        BankAccountTypeEnumeration clientBankAccountType,
        string clientBranchCode,
        DateTimeOffset actionDate,
        bool fediIndicator,
        string createdBy
    )
    {
        var hyphenTransactionDetail = await _hyphenTransactionRepository.FirstOrDefaultAsync(x => 
            x.TransactionId == transactionId && 
            x.TransactionReference == transactionReference
        );

        if (hyphenTransactionDetail is not null) return hyphenTransactionDetail;
            
        clientName = clientName[..Math.Min(80, clientName.Length)];
        code2 = code2[..Math.Min(20, code2.Length)];
        reference2 = reference2[..Math.Min(20, reference2.Length)];
                
        hyphenTransactionDetail = new HyphenTransactionDetail(
            hyphenTransactionTypeId,
            documentType,
            reference1,
            reference2,
            code1,
            code2,
            amount,
            HyphenProcessingOption1.List.First(f => Enum.GetName(typeof(HyphenProcessingOption1Enum), f)!.Equals(Enum.GetName(typeof(processingOption1Type), processingOption1Type.I))),
            HyphenProcessingOption2.List.First(f => Enum.GetName(typeof(HyphenProcessingOption2Enum), f)!.Equals(Enum.GetName(typeof(processingOption2Type), processingOption2Type.S))),
            clientName,
            clientBankAccountHolderType,
            clientBankAccountNumber,
            clientBankAccountType,
            clientBranchCode,
            string.Empty,
            actionDate,
            fediIndicator,
            transactionReference,
            createdBy
        );

        hyphenTransactionDetail.LinkTransaction(transactionId);

        _hyphenTransactionRepository.Add(hyphenTransactionDetail);

        await _hyphenTransactionRepository.UnitOfWork.SaveChangesAsync();

        return hyphenTransactionDetail;
    }

    public async Task<TransactionResponse> GetPaymentTransactionAsync(string transactionReference)
    {
        var transaction = await _transactionRepository.FirstOrDefaultAsync(i =>
            i.TransactionReference == transactionReference.Trim() &&
            i.AccountTypeId == AccountTypeEnumeration.AccountsPayable
        );

        if (transaction == null)
        {
            return null;
        }

        return _mapper.Map<TransactionResponse>(transaction);
    }

    public async Task EnqueuePaymentTransactionMessageAsync(PaymentTransactionMessage message)
    {
        _logger.LogInformation("Enqueue {MessageName} - {MessageContent}", nameof(PaymentTransactionMessage),
            JsonConvert.SerializeObject(message, Formatting.Indented));

        var request = _mapper.Map<HyphenPaymentTransactionMessage>(message);
        await _messageService.SendMessageToQueue(request, MassTransitQueues.FinancialHyphenTransactions, CancellationToken.None);
    }

    #endregion
}