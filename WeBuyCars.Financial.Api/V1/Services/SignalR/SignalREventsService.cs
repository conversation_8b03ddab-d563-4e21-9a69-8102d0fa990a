using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Financial.Contracts;
using Microsoft.Extensions.Logging;
using WeBuyCars.Core.Exceptions;
using WeBuyCars.Financial.Core.Constants;
using WeBuyCars.Financial.Core.Enumerations;
using WeBuyCars.Financial.Core.SharedKernel;
using WeBuyCars.Financial.Infrastructure.Communications.Enumerations;
using WeBuyCars.Financial.Infrastructure.Communications.Models;
using WeBuyCars.Financial.Infrastructure.Communications.Services.Interfaces;
using WeBuyCars.Financial.Infrastructure.Messaging.Services.Interfaces;

namespace WeBuyCars.Financial.Api.V1.Services.SignalR;

public sealed class SignalREventsService
{
    #region Constructors

    public SignalREventsService(ICommunicationService communicationService, ILogger<SignalREventsService> logger,
        IBuyLeadRepository buyLeadRepository, IMessageService messageService)
    {
        _communicationService = communicationService ?? throw new ArgumentNullException(nameof(communicationService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _buyLeadRepository = buyLeadRepository ?? throw new ArgumentNullException(nameof(buyLeadRepository));
        _messageService = messageService ?? throw new ArgumentNullException(nameof(messageService));
    }

    #endregion

    #region Class Fields

    private readonly ICommunicationService _communicationService;
    private readonly ILogger<SignalREventsService> _logger;
    private readonly IBuyLeadRepository _buyLeadRepository;
    private readonly IMessageService _messageService;

    #endregion

    #region Public Methods

    public async Task SendSignalRNotificationBuyers(PaymentNotificationBuyersMessage message)
    {
        var buyLead =
            await _buyLeadRepository.FindBuyLeadAsync(b => b.BuyLeadCode == message.BuyLeadCode,
                CancellationToken.None);
        if (buyLead is null)
            throw new DomainException($"No buy lead found for {message.BuyLeadCode}");

        var processIdentifier = buyLead.BuyLeadProcess?.ProcessDefinitionExternalId;
        var buyerEmail = buyLead.BuyLeadBuyer?.EmailAddress;
        var buyersAssistantEmail = buyLead.BuyLeadBuyersAssistant?.EmailAddress;

        if (string.IsNullOrWhiteSpace(processIdentifier))
            throw new DomainException($"Process identifier doesn't exist for {message.BuyLeadCode}");
        if (string.IsNullOrWhiteSpace(buyerEmail))
            throw new DomainException($"Buyer email doesn't exist for {message.BuyLeadCode}");

        var notification = new SignalRNotificationRequestDto
        {
            Attributes = new List<KeyValueObject>
            {
                new()
                {
                    Key = "ProcessDefinitionIdentifier",
                    Value = processIdentifier
                },
                new()
                {
                    Key = "BuyLeadCode",
                    Value = message.BuyLeadCode
                }
            },
            AudienceIdentifier = buyerEmail
        };

        switch (message.Action)
        {
            case OutstandingPaymentActions.Accepted:
                notification.Type = SignalrNotificationType.Info;
                notification.Severity = SignalrNotificationSeverity.Low;

                notification.Message = $"Payment Accepted - {message.BuyLeadCode}";
                notification.NotificationTitle = "Payment Accepted";
                notification.NotificationBody = $"Payment Accepted - {message.BuyLeadCode}";
                break;
            case OutstandingPaymentActions.Rejected:
                notification.Type = SignalrNotificationType.Action;
                notification.Severity = SignalrNotificationSeverity.High;

                notification.Message = $"Payment Rejected - {message.BuyLeadCode}";
                notification.NotificationTitle = "Payment Rejected";
                notification.NotificationBody = $"Payment Rejected - {message.BuyLeadCode}";
                break;
            case OutstandingPaymentActions.Completed:
                notification.Type = SignalrNotificationType.Info;
                notification.Severity = SignalrNotificationSeverity.High;

                switch (message.TransactionType)
                {
                    case TransactionTypeEnum.Settlement:
                        notification.Message = $"Settlement Payment Completed - {message.BuyLeadCode}";
                        notification.NotificationTitle = "Settlement Payment Completed";
                        notification.NotificationBody = $"Settlement Payment Completed - {message.BuyLeadCode}";
                        break;
                    case TransactionTypeEnum.Deposit:
                        notification.Message = $"Deposit Payment Completed - {message.BuyLeadCode}";
                        notification.NotificationTitle = "Deposit Payment Completed";
                        notification.NotificationBody = $"Deposit Payment Completed - {message.BuyLeadCode}";
                        break;
                    default:
                        notification.Message = $"Payment Completed - {message.BuyLeadCode}";
                        notification.NotificationTitle = "Payment Completed";
                        notification.NotificationBody = $"Payment Completed - {message.BuyLeadCode}";
                        break;
                }
                break;
            default:
                throw new ArgumentOutOfRangeException();
        }

        // Send notification to buyer
        await _communicationService.SendSignalrNotification(notification, CancellationToken.None);

        // Send notification to buyers assistant
        if (!string.IsNullOrWhiteSpace(buyersAssistantEmail))
        {
            notification.AudienceIdentifier = buyersAssistantEmail;
            await _communicationService.SendSignalrNotification(notification, CancellationToken.None);
        }
    }

    public async Task EnqueueOutstandingPaymentEvent(OutstandingPaymentActions action, TransactionTypeEnum transactionType, 
        string buyLeadCode, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(buyLeadCode))
            throw new ArgumentNullException(buyLeadCode);

        _logger.LogInformation("Enqueueing Signal-R Event [{MessageName} - {BuyLeadCode}]",
            nameof(EnqueueOutstandingPaymentEvent),
            buyLeadCode
        );

        var message = new PaymentNotificationBuyersMessage(buyLeadCode, action, transactionType);

        await _messageService.SendMessageToQueue(message, MassTransitQueues.FinancialSignalREvents, cancellationToken);
    }

    #endregion
}