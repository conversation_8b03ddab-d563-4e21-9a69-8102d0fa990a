using FluentValidation;
using WeBuyCars.Financial.Api.V1.Models;

namespace WeBuyCars.Financial.Api.V1.Validation.Accounts;

public sealed class GetTotalDepositAmountValidator : AbstractValidator<GetTotalDepositAmountRequest>
{
    public GetTotalDepositAmountValidator()
    {
        RuleFor(x => x.AccountNumber)
            .NotEmpty();

        RuleFor(x => x.StockNumber)
            .NotEmpty();
    }
}