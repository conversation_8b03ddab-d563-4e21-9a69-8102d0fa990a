using FluentValidation;
using Microsoft.FeatureManagement;
using WeBuyCars.Financial.Api.V1.Models;
using WeBuyCars.Financial.Api.V1.Validation.CustomValidators;

namespace WeBuyCars.Financial.Api.V1.Validation.Payments;

public sealed class PaymentTransactionSouthAfricanIndividualValidator : AbstractValidator<PaymentTransactionSouthAfricanIndividualRequest>
{
    #region Constructors

    public PaymentTransactionSouthAfricanIndividualValidator(IFeatureManager featureManager)
    {
        Include(new PaymentTransactionIndividualValidator(featureManager));
        
        RuleFor(p => p.IdNumber)
            .NotEmpty()
            .MaximumLength(13)
            .Matches("^[0-9]*$")
            .WithMessage("{PropertyName} must be a numeric value")
            .SetValidator(new RsaIdValidator<PaymentTransactionSouthAfricanIndividualRequest>());
    }

    #endregion
}