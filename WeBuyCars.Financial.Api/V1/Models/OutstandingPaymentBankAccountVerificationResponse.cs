using System;

namespace WeBuyCars.Financial.Api.V1.Models
{
    public class OutstandingPaymentBankAccountVerificationResponse
    {
		public string UserReference { get; set; }  
        public DateTimeOffset? RequestDateTime { get; set; }
        public DateTimeOffset? ReRequestDateTime { get; set; }
        public bool VerificationExpired { get; set; }
        public OutstandingPaymentBankAccountVerificationBankAccountResponse BankAccount { get; set; }
		public string Provider { get; set; }  
        public string BankAccountVerificationMessageCode { get; set; }
        public string BankAccountVerificationMessageDescription { get; set; }
    }
}