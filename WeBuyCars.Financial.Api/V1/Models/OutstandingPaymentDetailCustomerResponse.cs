using System.Collections.Generic;

namespace WeBuyCars.Financial.Api.V1.Models
{
    public class OutstandingPaymentDetailCustomerResponse : OutstandingPaymentCustomerResponse
    {
        public List<OutstandingPaymentBankAccountResponse> BankAccounts { get; set; }

        public List<OutstandingPaymentTransactionResponse> ManualTransactions { get; set; }

        public OutstandingPaymentDetailCustomerResponse()
        {
            BankAccounts = new List<OutstandingPaymentBankAccountResponse>();
            ManualTransactions = new List<OutstandingPaymentTransactionResponse>();
        }
    }
}