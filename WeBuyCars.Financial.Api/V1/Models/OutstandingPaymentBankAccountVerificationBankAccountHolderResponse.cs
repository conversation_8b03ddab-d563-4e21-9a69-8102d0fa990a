namespace WeBuyCars.Financial.Api.V1.Models
{
    public class OutstandingPaymentBankAccountVerificationBankAccountHolderResponse
    {
        public string BankAccountHolderType { get; set; }
        public string IdNumber { get; set; }
        public string PassportNumber { get; set; }
        public string CompanyRegistrationNumber { get; set; }
        public string EstateNumber { get; set; }
        public string Identification { get; set; }
        public string Initials { get; set; }
        public string LastName { get; set; }
        public string CompanyName { get; set; }
        public string EstateName { get; set; }
        public string Name { get; set; }
        public string PhoneNumber { get; set; }
        public string EmailAddress { get; set; }

        public OutstandingPaymentBankAccountVerificationBankAccountHolderMatchResponse Matches { get; set; }

    }
}