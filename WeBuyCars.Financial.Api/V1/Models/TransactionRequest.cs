using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using WeBuyCars.Financial.Core.Enumerations;

namespace WeBuyCars.Financial.Api.V1.Models
{
    public class TransactionRequest
    {
        [Required]
        public AccountTypeEnumeration AccountType { get; set; }

        [Required]
        [StringLength(50, ErrorMessage = "{0} cannot exceed {1} characters.")]
        public string TransactionReference { get; set; }

        /// <summary>
        /// The ISO 4217 3 letter code for the transaction currency. 
        /// Please note only South African Rand (ZAR) is currently supported.
        /// </summary>
        [Required]
        [StringLength(3, ErrorMessage = "{0} cannot exceed {1} characters.")]
        public string CurrencyCode { get; set; } = "ZAR";

        [Required]
        public decimal Amount { get; set; }

        [Required]
        public TransactionStatusEnum Status { get; set; }

        [Required]
        [StringLength(200, ErrorMessage = "{0} cannot exceed {1} characters.")]
        public string Description { get; set; }

        [Required]
        public DateTimeOffset PaymentDate { get; set; }

        [Required]
        public ProviderEnum Provider { get; set; }

        [Required]
        public TransactionTypeEnum TransactionType { get; set; }

        /// <summary>
        /// The external provider transaction reference/identifier.
        /// </summary>
        [StringLength(50, ErrorMessage = "{0} cannot exceed {1} characters.")]
        public string ProviderReference { get; set; }

        [StringLength(20, ErrorMessage = "{0} cannot exceed {1} characters.")]
        public string StockNumber { get; set; }

        [StringLength(20, ErrorMessage = "{0} cannot exceed {1} characters.")]
        public string BuyLeadReference { get; set; }

        public HashSet<string> Consents { get; set; } = new HashSet<string>();

        [JsonIgnore]
        public string CreatedBy { get; set; }
    }
}