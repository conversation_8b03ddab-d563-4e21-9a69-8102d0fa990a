using System.Net;
using Microsoft.Extensions.Logging;
using Refit;
using WeBuyCars.Core.Exceptions;
using WeBuyCars.Evolve.Infrastructure.CRM.Models;
using WeBuyCars.Evolve.Infrastructure.CRM.Services.Interfaces;

namespace WeBuyCars.Evolve.Infrastructure.CRM.Services;

public sealed class CrmService : ICrmService
{
    #region Class Members

    private readonly ICrmIntegrationService _crmIntegrationService;
    private readonly ILogger<CrmService> _logger;

    #endregion

    #region Constructors

    public CrmService(ICrmIntegrationService crmIntegrationService, 
        ILogger<CrmService> logger)
    {
        _crmIntegrationService =
            crmIntegrationService ?? throw new ArgumentNullException(nameof(crmIntegrationService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    #endregion

    #region Public Methods

    public async Task<SalesTermsResponseDto> GetSalesTermsAsync(string accountNumber, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(accountNumber))
            throw new ArgumentNullException(nameof(accountNumber));

        try
        {
            var salesTerms = await _crmIntegrationService.GetSalesTerms(accountNumber, cancellationToken);

            return salesTerms;
        }
        catch (ValidationApiException ve)
        {
            _logger.LogError(ve, "[Account Number: {AccountNumber}] {MethodName}: Failed to get sales terms",
                accountNumber,
                nameof(GetSalesTermsAsync));
            throw new DomainException(
                $"[Account Number: {accountNumber}] {nameof(GetSalesTermsAsync)}: Failed to get sales terms");
        }
        catch (ApiException ae) when (ae.StatusCode is HttpStatusCode.NoContent)
        {
            return new SalesTermsResponseDto();
        }
        catch (ApiException ae)
        {
            _logger.LogError(ae, "[Account Number: {AccountNumber}] {MethodName}: Failed to get sales terms",
                accountNumber,
                nameof(GetSalesTermsAsync));
            throw new InfrastructureException(
                $"[Account Number: {accountNumber}] {nameof(GetSalesTermsAsync)}: Failed to get sales terms");
        }
    }

    #endregion
}