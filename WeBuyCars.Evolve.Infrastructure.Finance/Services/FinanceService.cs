using System.Net;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Refit;
using WeBuyCars.Core.Exceptions;
using WeBuyCars.Evolve.Core.Enumerations;
using WeBuyCars.Evolve.Core.Localisation;
using WeBuyCars.Evolve.Core.SharedKernel;
using WeBuyCars.Evolve.Infrastructure.Finance.Models;
using WeBuyCars.Evolve.Infrastructure.Finance.Services.Interfaces;

namespace WeBuyCars.Evolve.Infrastructure.Finance.Services;

public sealed class FinanceService : IFinanceService
{
    #region Class Members

    private readonly IFinanceIntegrationService _financeIntegrationService;
    private readonly ILogger<FinanceService> _logger;
    private readonly ILocalizationService _localizationService;
    private readonly IMemoryCache _memoryCache;

    #endregion

    #region Constructors

    public FinanceService(IFinanceIntegrationService financeIntegrationService, 
        ILogger<FinanceService> logger, ILocalizationService localizationService, IMemoryCache memoryCache)
    {
        _financeIntegrationService =
            financeIntegrationService ?? throw new ArgumentNullException(nameof(financeIntegrationService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _localizationService = localizationService;
        _memoryCache = memoryCache;
    }

    #endregion

    public async Task<GetTotalDepositAmountResponse> GetTotalDepositAmountAsync(GetTotalDepositAmountRequest request, 
        CancellationToken cancellationToken)
    {
        if (!_localizationService.LocaleSettings.FeatureFlags.EnableFinancialApiIntegration)
            return new GetTotalDepositAmountResponse(0.0m);
        
        try
        {
            return await _financeIntegrationService.GetTotalDepositAmountAsync(request, cancellationToken);
        }
        catch (ValidationApiException ve)
        {
            _logger.LogError(ve, "{MethodName}: Failed to get total deposit amount", nameof(GetTotalDepositAmountAsync));
            throw new DomainException($"{nameof(GetTotalDepositAmountAsync)}: Failed to get total deposit amount");
        }
        catch (ApiException ae) when (ae.StatusCode is HttpStatusCode.NoContent)
        {
            return new GetTotalDepositAmountResponse(0.00m);
        }
        catch (ApiException ae)
        {
            _logger.LogError(ae, "{MethodName}: Failed to get total deposit amount", nameof(GetTotalDepositAmountAsync));
            throw new InfrastructureException($"{nameof(GetTotalDepositAmountAsync)}: Failed to get total deposit amount");
        }
    }
    
    public async Task<decimal> GetFinancialConstantValueAsync(FinancialConstantsTypeEnum constantType, CancellationToken cancellationToken)
    {
        if (!_localizationService.LocaleSettings.FeatureFlags.EnableFinancialApiIntegration)
        {
            return GetDefaultConstantValue(constantType);
        }
        
        try
        {
            var cacheKey = $"Finance:{constantType}";
            var constantValue = await _memoryCache.GetOrCreateAsync(cacheKey, async cacheEntry =>
            {
                var response = await _financeIntegrationService.GetCurrentActiveConstantValueAsync(constantType, cancellationToken);
                cacheEntry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5);
                
                return response;
            });

            // Special handling for VAT
            if (constantType == FinancialConstantsTypeEnum.Vat)
            {
                return constantValue > 0 ? constantValue / 100.00m + 1 : GetDefaultConstantValue(constantType);
            }

            return constantValue;
        }
        catch (ValidationApiException ve)
        {
            _logger.LogError(ve, "{MethodName}: Failed to retrieve {ConstantType} value",
                nameof(GetFinancialConstantValueAsync), constantType);
            throw new DomainException($"{nameof(GetFinancialConstantValueAsync)}: Failed to retrieve {constantType} value");
        }
        catch (ApiException ae)
        {
            _logger.LogError(ae, "{MethodName}: Failed to retrieve {ConstantType} value",
                nameof(GetFinancialConstantValueAsync), constantType);
            throw new InfrastructureException($"{nameof(GetFinancialConstantValueAsync)}: Failed to retrieve {constantType} value");
        }
    }
    
    private static decimal GetDefaultConstantValue(FinancialConstantsTypeEnum constantType)
    {
        return constantType switch
        {
            FinancialConstantsTypeEnum.Vat => Constants.VAT,
            _ => 0.00m
        };
    }
}