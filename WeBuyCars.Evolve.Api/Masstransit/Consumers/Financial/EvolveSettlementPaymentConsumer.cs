using System;
using System.Threading.Tasks;
using AutoMapper;
using Evolve.Contracts;
using MassTransit;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using WeBuyCars.Core.Exceptions;
using WeBuyCars.Evolve.Api.V1.Models;
using WeBuyCars.Evolve.Api.V1.Services;
using WeBuyCars.Evolve.Core.Constant;

namespace WeBuyCars.Evolve.Api.Masstransit.Consumers.Financial;

public class EvolveSettlementPaymentConsumer : IConsumer<SettlementPaymentRequestMessage>
{
    #region Fields
    
    private readonly ILogger<EvolveSettlementPaymentConsumer> _logger;
    private readonly PaymentService _paymentService;
    private readonly IMapper _mapper;
    
    #endregion
    
    #region Constructors

    public EvolveSettlementPaymentConsumer(ILogger<EvolveSettlementPaymentConsumer> logger,
        PaymentService paymentService, IMapper mapper)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _paymentService = paymentService ?? throw new ArgumentNullException(nameof(paymentService));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
    }
    
    #endregion
    
    #region Public Methods

    public async Task Consume(ConsumeContext<SettlementPaymentRequestMessage> context)
    {
        var message = context.Message;
        if (message == null)
            throw new DomainException("SettlementPaymentRequestMessage is null");
            
        _logger.LogInformation("Consuming queue {QueueName} - {MessageName} - {MessageContent}",
            MassTransitQueues.EvolveSettlementPayments,
            nameof(SettlementPaymentRequestMessage),
            JsonConvert.SerializeObject(context.Message, Formatting.Indented));

        var request = _mapper.Map<SettlementPaymentRequest>(message);
        await _paymentService.MakeSettlementPaymentAsync(request.AccountNumber, request.Amount,
            request.TransactionReference, request.StockNumbers);
    }
    
    #endregion
}