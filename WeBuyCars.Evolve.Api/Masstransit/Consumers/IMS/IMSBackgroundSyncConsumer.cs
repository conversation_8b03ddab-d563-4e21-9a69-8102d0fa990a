using System;
using System.Threading.Tasks;
using Evolve.Contracts;
using MassTransit;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using WeBuyCars.Core.Exceptions;
using WeBuyCars.Evolve.Api.V1.Services.BackgroundService;
using WeBuyCars.Evolve.Core.Constant;

namespace WeBuyCars.Evolve.Api.Masstransit.Consumers.IMS;

public class IMSBackgroundSyncConsumer : IConsumer<BackgroundSyncMessage>
{
    #region Fields

    private readonly ILogger<IMSBackgroundSyncConsumer> _logger;
    private readonly BackgroundSyncImsService _backgroundSyncImsServiceService;

    #endregion

    #region Constructors

    public IMSBackgroundSyncConsumer
    (
        ILogger<IMSBackgroundSyncConsumer> logger,
        BackgroundSyncImsService backgroundSyncImsService
    )
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _backgroundSyncImsServiceService = backgroundSyncImsService ??
                                           throw new ArgumentNullException(nameof(backgroundSyncImsService));
    }

    #endregion

    #region Public Methods

    public async Task Consume(ConsumeContext<BackgroundSyncMessage> context)
    {
        var message = context.Message;
        if (message == null)
            throw new DomainException("BackgroundSyncMessage is null");

        _logger.LogInformation("Consuming queue {QueueName} - {MessageName} - {MessageContent}",
            MassTransitQueues.EvolveBackgroundSync,
            nameof(BackgroundSyncMessage),
            JsonConvert.SerializeObject(message, Formatting.Indented));

        await _backgroundSyncImsServiceService.BackgroundSync(message);
    }

    #endregion
}