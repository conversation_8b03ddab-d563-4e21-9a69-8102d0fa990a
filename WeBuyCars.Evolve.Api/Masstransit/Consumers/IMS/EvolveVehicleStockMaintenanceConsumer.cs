using System;
using System.Threading.Tasks;
using AutoMapper;
using Evolve.Contracts;
using MassTransit;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using WeBuyCars.Core.Exceptions;
using WeBuyCars.Evolve.Api.V1.Models;
using WeBuyCars.Evolve.Api.V1.Services;
using WeBuyCars.Evolve.Core.Constant;

namespace WeBuyCars.Evolve.Api.Masstransit.Consumers.IMS;

public class EvolveVehicleStockMaintenanceConsumer : IConsumer<UpdatedStockStatusMessage>
{
    #region Fields
    
    private readonly ILogger<EvolveVehicleStockMaintenanceConsumer> _logger;
    private readonly StockUpdateService _stockUpdateService;
    private readonly IMapper _mapper;
    
    #endregion
    
    #region Constructors

    public EvolveVehicleStockMaintenanceConsumer(ILogger<EvolveVehicleStockMaintenanceConsumer> logger,
        StockUpdateService stockUpdateService, IMapper mapper)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _stockUpdateService = stockUpdateService ?? throw new ArgumentNullException(nameof(stockUpdateService));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
    }
    
    #endregion
    
    #region Public Methods

    public async Task Consume(ConsumeContext<UpdatedStockStatusMessage> context)
    {
        var message = context.Message;
        if (message == null)
            throw new DomainException("UpdatedStockStatusMessage is null");

        _logger.LogInformation("Consuming queue {QueueName} - {MessageName} - {MessageContent}",
            MassTransitQueues.EvolveVehicleStockMaintenance,
            nameof(UpdatedStockStatusMessage),
            JsonConvert.SerializeObject(message, Formatting.Indented));

        try
        {
            var request = _mapper.Map<StockStatusUpdateStockItemDto>(message);
            await _stockUpdateService.ProcessStockLocationUpdate(request);
        }
        catch (DomainException de)
        {
            // Persist Vehicle Stock Maintenance
            if (de.Message.IndexOf("Unexpected error occurred.", StringComparison.OrdinalIgnoreCase) > -1 ||
                de.Message.IndexOf("network exception occurred processing request", StringComparison.OrdinalIgnoreCase) > -1 ||
                de.Message.IndexOf("in locked status", StringComparison.OrdinalIgnoreCase) > -1 ||
                de.Message.IndexOf("Connection Error:  BROKER", StringComparison.OrdinalIgnoreCase) > -1 ||
                de.Message.IndexOf("The Server application has returned an error", StringComparison.OrdinalIgnoreCase) > -1)
                throw;
        }
    }
    
    #endregion
}