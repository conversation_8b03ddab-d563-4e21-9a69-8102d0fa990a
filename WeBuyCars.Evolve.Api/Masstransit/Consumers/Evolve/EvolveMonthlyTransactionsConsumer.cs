using System;
using System.Threading;
using System.Threading.Tasks;
using Evolve.Contracts;
using MassTransit;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using WeBuyCars.Core.Exceptions;
using WeBuyCars.Evolve.Api.V1.Interfaces;
using WeBuyCars.Evolve.Core.Constant;
using WeBuyCars.Evolve.Core.Enumerations;

namespace WeBuyCars.Evolve.Api.Masstransit.Consumers.Evolve;

public class EvolveMonthlyTransactionsConsumer : IConsumer<MonthlyTransactionsSyncMessage>
{
    #region Fields

    private readonly ILogger _logger;
    private readonly IMonthlyTransactionService _monthlyTransactionService;

    #endregion

    #region Constructors

    public EvolveMonthlyTransactionsConsumer(
        ILogger<EvolveMonthlyTransactionsConsumer> logger,
        IMonthlyTransactionService monthlyTransactionService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _monthlyTransactionService = monthlyTransactionService ??
                                     throw new ArgumentNullException(nameof(monthlyTransactionService));
    }

    #endregion

    #region Public Methods

    public async Task Consume(ConsumeContext<MonthlyTransactionsSyncMessage> context)
    {
        var message = context.Message;
        if (message == null)
            throw new DomainException("MonthlyTransactionsSyncMessage is null");

        _logger.LogInformation("Consuming queue {QueueName} - {MessageName} - {MessageContent}",
            MassTransitQueues.EvolveMonthlyTransactions,
            nameof(MonthlyTransactionsSyncMessage),
            JsonConvert.SerializeObject(context.Message, Formatting.Indented));

        var monthlyTransactionsSync = message.TypeId switch
        {
            TransactionEnum.Bought => _monthlyTransactionService.GetMonthlyBoughStockCount(message),
            TransactionEnum.Sold => await _monthlyTransactionService.GetMonthlySalesStockCount(message),
            _ => throw new ArgumentOutOfRangeException(nameof(message))
        };

        _logger.LogInformation("Processing Monthly Transaction Sync Object : {MessageName}: {MessageContent}",
            nameof(MonthlyTransactionsSyncMessage), monthlyTransactionsSync);

        await _monthlyTransactionService.UpdateMonthlyTransaction(monthlyTransactionsSync, CancellationToken.None);
    }

    #endregion
}