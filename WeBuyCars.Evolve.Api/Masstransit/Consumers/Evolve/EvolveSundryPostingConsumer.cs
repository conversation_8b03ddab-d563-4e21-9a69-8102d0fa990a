using System;
using System.Threading.Tasks;
using AutoMapper;
using Evolve.Contracts;
using MassTransit;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using WeBuyCars.Core.Exceptions;
using WeBuyCars.Evolve.Api.V1.Models;
using WeBuyCars.Evolve.Api.V1.Services;
using WeBuyCars.Evolve.Core.Constant;
using WeBuyCars.Evolve.Core.Enumerations;

namespace WeBuyCars.Evolve.Api.Masstransit.Consumers.Evolve;

public class EvolveSundryPostingConsumer : IConsumer<PostSundryRequestMessage>
{
    #region Fields

    private readonly ILogger<EvolveSundryPostingConsumer> _logger;
    private readonly GeneralPostingService _generalPostingService;
    private readonly IMapper _mapper;

    #endregion

    #region Constructors

    public EvolveSundryPostingConsumer(ILogger<EvolveSundryPostingConsumer> logger,
        GeneralPostingService generalPostingService, IMapper mapper)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _generalPostingService =
            generalPostingService ?? throw new ArgumentNullException(nameof(generalPostingService));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
    }

    #endregion

    #region Public Methods

    public async Task Consume(ConsumeContext<PostSundryRequestMessage> context)
    {
        var message = context.Message;
        if (message == null)
            throw new DomainException("PostSundryRequestMessage is null");

        _logger.LogInformation(
            "[AP Account: {AccountNumber}] Consuming queue {QueueName} - {MessageName} - {MessageContent}",
            message.AccountNumber,
            MassTransitQueues.EvolveSundryPosting,
            nameof(PostSundryRequestMessage),
            JsonConvert.SerializeObject(context.Message, Formatting.Indented));

        try
        {
            var request = _mapper.Map<PostSundryRequest>(message);
            await _generalPostingService.SendGeneralPostingToEvolveAsync(message.TransactionLogReference, request,
                EvolveTransactionType.SundryPosting);
        }
        catch (DomainException de)
        {
            if (de.Message.IndexOf("Can't find aptApMaster in company", StringComparison.OrdinalIgnoreCase) > -1 ||
                de.Message.IndexOf("in locked status", StringComparison.OrdinalIgnoreCase) > -1 ||
                de.Message.IndexOf("Connection Error:  BROKER", StringComparison.OrdinalIgnoreCase) > -1 ||
                de.Message.IndexOf("The Server application has returned an error", StringComparison.OrdinalIgnoreCase) > -1 ||
                de.Message.IndexOf("network exception occurred processing request", StringComparison.OrdinalIgnoreCase) > -1)
                throw;
        }
    }

    #endregion
}