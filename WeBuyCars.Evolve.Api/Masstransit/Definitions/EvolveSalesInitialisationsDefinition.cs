using System;
using MassTransit;
using Microsoft.Extensions.Options;
using WeBuyCars.Evolve.Api.Masstransit.Configuration;
using WeBuyCars.Evolve.Api.Masstransit.Consumers.SLMS;
using WeBuyCars.Evolve.Core.Constant;

namespace WeBuyCars.Evolve.Api.Masstransit.Definitions;

public class EvolveSalesInitialisationsDefinition : ConsumerDefinition<EvolveSalesInitialisationsConsumer>
{
    private readonly MassTransitConsumerConfig _massTransitConsumerConfig;

    public EvolveSalesInitialisationsDefinition(IOptions<MassTransitConsumerConfig> massTransitConsumerConfig)
    {
        EndpointName = MassTransitQueues.EvolveSalesInitialisations;

        _massTransitConsumerConfig = massTransitConsumerConfig.Value;
        ConcurrentMessageLimit = _massTransitConsumerConfig.ConcurrentMessageLimit;
    }

    protected override void ConfigureConsumer(IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<EvolveSalesInitialisationsConsumer> consumerConfigurator, 
        IRegistrationContext context)
    {
        consumerConfigurator.UseMessageRetry(r =>
        {
            r.Incremental(_massTransitConsumerConfig.RetryLimit,
                TimeSpan.FromSeconds(_massTransitConsumerConfig.Retry.InitialIntervalSeconds),
                TimeSpan.FromSeconds(_massTransitConsumerConfig.Retry.IntervalIncrementSeconds));
        });

        consumerConfigurator.UseScheduledRedelivery(r =>
        {
            r.Incremental(_massTransitConsumerConfig.RedeliveryLimit,
                TimeSpan.FromMinutes(_massTransitConsumerConfig.Redelivery.IntervalMinutes),
                TimeSpan.FromMinutes(_massTransitConsumerConfig.Redelivery.IntervalIncrementMinutes));
        });

        endpointConfigurator.PrefetchCount = _massTransitConsumerConfig.PrefetchCount;
        endpointConfigurator.ConfigureDeadLetterQueueDeadLetterTransport();
        endpointConfigurator.ConfigureDeadLetterQueueErrorTransport();
    }
}