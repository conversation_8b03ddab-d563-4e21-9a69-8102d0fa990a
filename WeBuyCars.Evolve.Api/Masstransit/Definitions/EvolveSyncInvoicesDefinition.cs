using MassTransit;
using WeBuyCars.Evolve.Api.Masstransit.Consumers.Evolve;
using WeBuyCars.Evolve.Core.Constant;

namespace WeBuyCars.Evolve.Api.Masstransit.Definitions;

public class EvolveSyncInvoicesDefinition : ConsumerDefinition<EvolveSyncInvoicesConsumer>
{
    public EvolveSyncInvoicesDefinition()
    {
        EndpointName = MassTransitQueues.EvolveSyncInvoices;
    }

    protected override void ConfigureConsumer(IReceiveEndpointConfigurator endpointConfigurator, IConsumerConfigurator<EvolveSyncInvoicesConsumer> consumerConfigurator,
        IRegistrationContext context)
    {
        endpointConfigurator.DiscardFaultedMessages();
    }
}