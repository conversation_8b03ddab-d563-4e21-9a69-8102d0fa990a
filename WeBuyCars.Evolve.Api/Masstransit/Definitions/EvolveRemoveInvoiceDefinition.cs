using MassTransit;
using WeBuyCars.Evolve.Api.Masstransit.Consumers.Evolve;
using WeBuyCars.Evolve.Core.Constant;

namespace WeBuyCars.Evolve.Api.Masstransit.Definitions;

public class EvolveRemoveInvoiceDefinition : ConsumerDefinition<EvolveRemoveInvoiceConsumer>
{
    public EvolveRemoveInvoiceDefinition()
    {
        EndpointName = MassTransitQueues.EvolveRemoveInvoice;
    }
    
    protected override void ConfigureConsumer(IReceiveEndpointConfigurator endpointConfigurator, IConsumerConfigurator<EvolveRemoveInvoiceConsumer> consumerConfigurator,
        IRegistrationContext context)
    {
        endpointConfigurator.DiscardFaultedMessages();
    }
}