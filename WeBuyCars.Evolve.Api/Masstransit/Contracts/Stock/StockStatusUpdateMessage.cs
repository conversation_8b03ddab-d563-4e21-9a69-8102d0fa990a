using System.Collections.Generic;
using WeBuyCars.Evolve.Infrastructure.Messaging.Services.Interfaces;

namespace Evolve.Contracts;

public class StockStatusUpdateMessage : INotificationContract
{
    public string RequestId { get; set; }
    public StockStatusUpdateOtpMessage Otp { get; set; }
}

public class StockStatusUpdateOtpMessage
{
    public string ReferenceNo { get; set; }
    public string DealStatus { get; set; }
    public string CustomerAccountNo { get; set; }
    public string VehicleStockNo { get; set; }
    public string InvoiceDate { get; set; }
    public string InvoiceNo { get; set; }
    public string CreditNoteNo { get; set; }
    public string SalesManCode { get; set; }
    public decimal SoldAmount { get; set; }
    public decimal AccessoriesCost { get; set; }
    public decimal TotalVatInvoiced { get; set; }
    public decimal TotalAmountDue { get; set; }
    public StockStatusUpdateOtpAccessoriesMessage Accessories { get; set; }
}

public class StockStatusUpdateOtpAccessoriesMessage
{
    public List<StockStatusUpdateOtpAccessoryMessage> Accessory { get; set; }
}

public class StockStatusUpdateOtpAccessoryMessage
{
    public string AccessoryDescription { get; set; }
    public decimal AccessoryPrice { get; set; }
    public string AccessoryTaxableInd { get; set; }
    public decimal AccessoryTaxAmount { get; set; }
}