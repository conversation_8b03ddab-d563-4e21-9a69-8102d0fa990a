using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using WeBuyCars.Core.Infrastructure.DynamicLinq.DynamicFilter;
using WeBuyCars.Evolve.Api.V1.Models;
using WeBuyCars.Evolve.Api.V1.Models.Gp;
using WeBuyCars.Evolve.Api.V1.Services.Elastic.Interfaces;
using WeBuyCars.Evolve.Api.V2.Interfaces;

namespace WeBuyCars.Evolve.Api.V2.Controllers;

[ApiController]
[ApiVersion("2.0")]
[Route("v{version:apiVersion}/[controller]")]
[Produces("application/json")]
[Authorize]
public class SalesController : ControllerBase
{
    #region Fields

    private readonly ISalesService _salesService;
    private readonly IDailySalesService _dailySalesService;
    private readonly IMonthlySalesService _monthlySalesService;

    #endregion

    #region Constructors

    public SalesController(ISalesService salesService, IDailySalesService dailySalesService,
        IMonthlySalesService monthlySalesService)
    {
        _salesService = salesService ?? throw new ArgumentNullException(nameof(salesService));
        _dailySalesService = dailySalesService ?? throw new ArgumentNullException(nameof(dailySalesService));
        _monthlySalesService = monthlySalesService ?? throw new ArgumentNullException(nameof(monthlySalesService));
    }

    #endregion

    /// <summary>
    /// Get the Evolve Sales Actuals
    /// </summary>
    /// <response code="200">Sales Actuals data</response>
    /// <response code="404">document not found</response>
    /// <response code="500">A server error occurred.</response>
    /// <returns>A <see cref="DocumentResponse"/> item.</returns>
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    [ProducesDefaultResponseType]
    [HttpPost("EvolveSalesActuals")]
    [Authorize(Policy = "CanReadInvoice")]
    public async Task<ActionResult<DynamicLinqResult>> DownloadInvoiceDocument(DynamicLinqRequest request,
        CancellationToken cancellationToken)
    {
        var result = await _salesService.GetEvolveSalesActuals(request, cancellationToken);
        return Ok(result);
    }    
    
    /// <summary>
    /// Get the Evolve Actual Sales
    /// </summary>
    /// <remarks>
    /// GET v2/sales/GetEvolveSalesActuals/2025-01-31T00:00:00/2025-01-31T23:59:59
    /// </remarks>
    /// <response code="200">Sales data</response>
    /// <response code="404">document not found</response>
    /// <response code="500">A server error occurred.</response>
    /// <returns>A <see cref="DocumentResponse"/> item.</returns>
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    [ProducesDefaultResponseType]
    [HttpGet("GetEvolveSalesActualsInvoices")]
    [Authorize(Policy = "CanRead")]
    public async Task<ActionResult<SalesActualsResponse>> GetEvolveSalesActualsInvoices([FromQuery] SalesActualsRequest request, CancellationToken cancellationToken)
    {
        var result = await _salesService.GetEvolveSalesActuals(request, cancellationToken);
        return Ok(result);
    }

    /// <summary>
    /// Gets sales summaries for a given date range.
    /// </summary>
    /// <remarks>
    /// Sample request:
    /// 
    ///     GET /v2/Sales/SalesSummary?FromDate=2024-12-31T22:00:00.000Z&ToDate=2024-12-17T22:00:00.000Z&IsRepo=false
    ///     
    /// </remarks>
    /// <param name="request">The <see cref="SalesSummaryRequest"/> request.</param>
    /// <returns>A list of deal summaries.</returns>
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    [ProducesDefaultResponseType]
    [HttpGet("SalesSummary")]
    [Authorize(Policy = "CanRead")]
    public async Task<IEnumerable<DealSummary>> GetSalesForDateRange([FromQuery] SalesSummaryRequest request)
    {
        var summaries = await _dailySalesService.GetByDateRangeAsync(request.FromDate, request.ToDate, request.IsRepo);
        return summaries?.Select(d => new DealSummary
        {
            Date = d.Date,
            Count = d.LocationSalesSummaries.Where(i => i.IsRepoSale == request.IsRepo)
                .Sum(i => i.Count) ?? 0,
            Value = d.LocationSalesSummaries.Where(i => i.IsRepoSale == request.IsRepo)
                .Sum(i => i.ValueIncl) ?? 0,
            GrossProfit = d.LocationSalesSummaries.Where(i => i.IsRepoSale == request.IsRepo)
                .Sum(i => i.GrossProfit) ?? 0,
            Type = "Sold"
        }) ?? new List<DealSummary>();
    }

    /// <summary>
    /// Gets sales summaries for a given date range per branch.
    /// </summary>
    /// <remarks>
    /// Sample request:
    /// 
    ///     GET /v2/Sales/Summary/Branch?FromDate=2024-12-31T22:00:00.000Z&ToDate=2024-12-17T22:00:00.000Z&IsRepo=false
    ///
    /// </remarks>
    [ProducesResponseType(typeof(List<BranchSalesSummary>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    [HttpGet("Summary/Branch")]
    [Authorize(Policy = "CanRead")]
    public async Task<IEnumerable<BranchSalesSummary>> GetBranchSummaryByDateRangeAsync([FromQuery] SalesSummaryRequest request)
    {
        return await _dailySalesService.GetBranchSummaryByDateRangeAsync(request.FromDate, request.ToDate, request.IsRepo);
    }

    /// <summary>
    /// Gets sales count per branch for a given date range.
    /// </summary>
    /// <remarks>
    /// Sample request:
    /// 
    ///     GET /v2/Sales/Count/Branch?FromDate=2024-12-31T22:00:00.000Z&ToDate=2024-12-17T22:00:00.000Z&IsRepo=false
    ///
    /// </remarks>
    /// <param name="request">The <see cref="SalesSummaryRequest"/> request.</param>
    /// <returns>A list of sales count per branch summaries.</returns>
    [ProducesResponseType(typeof(List<SalesCountByBranchSummary>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    [HttpGet("Count/Branch")]
    [Authorize(Policy = "CanRead")]
    public async Task<IEnumerable<SalesCountByBranchSummary>> GetSalesCountPerBranchByDateRangeAsync([FromQuery] SalesSummaryRequest request)
    {
        return await _dailySalesService.GetSalesCountPerBranchByDateRangeAsync(request.FromDate, request.ToDate, request.IsRepo);
    }

    /// <summary>
    /// Gets sales summaries for the current month per branch.
    /// </summary>
    /// <remarks>
    /// Sample request:
    /// 
    ///     GET /v2/Sales/Summary/Branch/Month?IsRepo=false
    ///     
    /// </remarks>
    /// <param name="isRepo">Repo flag.</param>
    /// <returns>A list of branch sales summaries.</returns>
    [ProducesResponseType(typeof(List<BranchSalesSummary>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    [HttpGet("Summary/Branch/Month")]
    [Authorize(Policy = "CanRead")]
    public async Task<IEnumerable<BranchSalesSummary>> GetBranchSummaryForCurrentMonthAsync([FromQuery] bool isRepo)
    {
        var today = DateTime.Today;
        var startOfMonth = new DateTime(today.Year, today.Month, 1);
        var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);

        return await _monthlySalesService.GetBranchSummaryByDateRangeAsync(startOfMonth, endOfMonth, isRepo);
    }

    /// <summary>
    /// Gets sales summaries for the current month.
    /// </summary>
    /// <remarks>
    /// Sample request:
    /// 
    ///     GET /v2/Sales/Month?IsRepo=false
    ///     
    /// </remarks>
    /// <param name="isRepo">Repo flag.</param>
    /// <returns>A list of monthly sales summaries.</returns>
    [ProducesResponseType(typeof(List<DealSummary>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    [HttpGet("CurrentMonth")]
    [Authorize(Policy = "CanRead")]
    public async Task<IEnumerable<DealSummary>> GetSalesForMonthPerDay([FromQuery] bool isRepo)
    {
        var today = DateTime.Today;
        var startOfMonth = new DateTime(today.Year, today.Month, 1);
        var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);
        var summaries = await _dailySalesService.GetByDateRangeAsync(startOfMonth, endOfMonth, isRepo);
        if (summaries == null)
        {
            return new List<DealSummary>();
        }

        return summaries.Select(d => new DealSummary
        {
            Date = d.Date,
            Count = d.LocationSalesSummaries.Where(i => i.IsRepoSale == isRepo)
                .Sum(i => i.Count) ?? 0,
            Value = d.LocationSalesSummaries.Where(i => i.IsRepoSale == isRepo)
                .Sum(i => i.ValueIncl) ?? 0,
            GrossProfit = d.LocationSalesSummaries.Where(i => i.IsRepoSale == isRepo)
                .Sum(i => i.GrossProfit) ?? 0,
            Type = "Sold"
        });
    }

    /// <summary>
    /// Gets sales summaries for the today.
    /// </summary>
    /// <remarks>
    /// Sample request:
    /// 
    ///     GET /v2/Sales/Summary/Today?IsRepo=false
    ///     
    /// </remarks>
    /// <param name="isRepo">Repo flag.</param>
    /// <returns>A list of daily sales summaries.</returns>
    [ProducesResponseType(typeof(DealSummary), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    [HttpGet("Summary/Today")]
    [Authorize(Policy = "CanRead")]
    public async Task<DealSummary> GetSalesSummaryForToday([FromQuery] bool isRepo)
    {
        var today = DateTime.Today;
        var summary = (await _dailySalesService.GetByDateRangeAsync(today, today.AddDays(1), isRepo)).FirstOrDefault();
        if (summary == null)
        {
            return new DealSummary();
        }

        return new DealSummary
        {
            Type = "Sold",
            Count = summary.LocationSalesSummaries.Where(i => i.IsRepoSale == isRepo)
                .Sum(i => i.Count) ?? 0,
            Value = summary.LocationSalesSummaries.Where(i => i.IsRepoSale == isRepo)
                .Sum(i => i.ValueIncl) ?? 0,
            GrossProfit = summary.LocationSalesSummaries.Where(i => i.IsRepoSale == isRepo)
                .Sum(i => i.GrossProfit) ?? 0,
            Date = today
        };
    }

    /// <summary>
    /// Gets sales summaries for the current month.
    /// </summary>
    /// <remarks>
    /// Sample request:
    /// 
    ///     GET /v2/Sales/Summary/Month?IsRepo=false
    ///     
    /// </remarks>
    /// <param name="isRepo">Repo flag.</param>
    /// <returns>A list of monthly sales summaries.</returns>
    [ProducesResponseType(typeof(DealSummary), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    [HttpGet("Summary/Month")]
    [Authorize(Policy = "CanRead")]
    public async Task<DealSummary> GetSalesSummaryForMonth([FromQuery] bool isRepo)
    {
        var today = DateTime.Today;
        var startOfMonth = new DateTime(today.Year, today.Month, 1);
        var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);
        var summaries = (await _monthlySalesService.GetByDateRangeAsync(startOfMonth, endOfMonth, isRepo)).ToList();

        return new DealSummary
        {
            Type = "Sold",
            Count = summaries.Sum(i => i.LocationSalesSummaries.Where(j => j.IsRepoSale == isRepo)
                .Sum(j => j.Count)) ?? 0,
            Value = summaries.Sum(i => i.LocationSalesSummaries.Where(j => j.IsRepoSale == isRepo)
                .Sum(j => j.ValueIncl)) ?? 0,
            GrossProfit = summaries.Sum(i => i.LocationSalesSummaries.Where(j => j.IsRepoSale == isRepo)
                .Sum(j => j.GrossProfit)) ?? 0
        };
    }
}