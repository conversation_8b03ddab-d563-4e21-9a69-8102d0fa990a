using System;
using System.Threading.Tasks;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using WeBuyCars.Core.Infrastructure.DynamicLinq.DynamicFilter;
using WeBuyCars.Evolve.Api.V2.Interfaces;

namespace WeBuyCars.Evolve.Api.V2.Controllers;

[ApiController]
[ApiVersion("2.0")]
[Route("v{version:apiVersion}/[controller]")]
[Produces("application/json")]
[Authorize]
public class StockController : ControllerBase
{
    #region Fields

    private readonly IStockReconService _stockReconService;

    #endregion
    
    #region Constructors

    public StockController(IStockReconService stockReconService)
    {
        _stockReconService = stockReconService ?? throw new ArgumentNullException(nameof(stockReconService));
    }
    
    #endregion

    [HttpPost("Recon/Prices")]
    [ProducesResponseType(typeof(DynamicLinqResult), StatusCodes.Status200OK)]
    [Authorize(Policy = "CanViewReports")]
    public async Task<ActionResult<DynamicLinqResult>> ReconPrices(DynamicLinqRequest request)
    {
        return Ok(await _stockReconService.GetStockPriceRecon(request));
    }
}