using System.Collections.Generic;
using System.Threading.Tasks;
using WeBuyCars.Core.Infrastructure.DynamicLinq.DynamicFilter;
using WeBuyCars.Evolve.Api.V1.Models;

namespace WeBuyCars.Evolve.Api.V2.Services.Elastic.Interfaces;

public interface IDebtorServiceV2
{
    Task<DynamicLinqResult> GetCurrentBranchTotalsAsync(DynamicLinqRequest request);

    Task<byte[]> GetCurrentBranchTotalsExportAsync(DynamicLinqRequest request);

    Task<byte[]> CreateExcelFile(List<UnallocatedTransactionResponse> transactions);
}