using System.ComponentModel.DataAnnotations;
using WeBuyCars.Evolve.Core.Enumerations;

namespace WeBuyCars.Evolve.Api.V1.Models
{
    public class TransactionRequest
    {
        /// <summary>
        /// Indicates the sales type i.e. Cash or Finance.
        /// </summary>
        [Required]
        public SalesType SalesType { get; set; }

        /// <summary>
        /// The ZAR price amount.
        /// </summary>
        [Required]
        [Range(0.00, double.MaxValue)]
        public decimal PriceAmount { get; set; }

        /// <summary>
        /// Indicate whether the sale should go through on Dealer Terms
        /// </summary>
        public bool CreditFacility { get; set; }

        /// <summary>
        /// The ZAR deposit amount.
        /// </summary>
        [Range(0.00, double.MaxValue)]
        public decimal? InitialDepositAmount { get; set; }

        [Range(0.00, double.MaxValue)]
        public decimal? DiscountUsedAsDepositAmount { get; set; }

        /// <summary>
        /// Indicates that the customer consented to receiving
        /// marketing information.
        /// </summary>
        public bool MarketingConsent { get; set; }

        /// <summary>
        /// Indicates that the customer consented to be 
        /// contacted by OUTsurance.
        /// </summary>
        public bool OutsuranceConsent { get; set; }

        /// <summary>
        /// Indicates that the customer consented to be 
        /// contacted by Netstar.
        /// </summary>
        public bool NetstarConsent { get; set; }

        /// <summary>
        /// Indicates the sales representative whom 
        /// concluded the sale. Refer to the lookup
        /// endpoint to get a list of sales representatives.
        /// </summary>
        [Required]
        public string SalesRep { get; set; }

        /// <summary>
        /// Indicates the sales manager of the
        /// sales representative. Refer to the lookup
        /// endpoint to get a list of sales managers.
        /// </summary>
        public string SalesManager { get; set; }

        /// <summary>
        /// Indicates the F and I Manager.
        /// </summary>
        public string FIManager { get; set; }

        /// <summary>
        /// Indicates who is responsible for payment.
        /// </summary>
        public WhoPays WhoPays { get; set; }

        /// <summary>
        /// Indicates if admin/document fees should be excluded from sale.
        /// </summary>
        public bool ExcludeFees { get; set; } = false;
        
        /// <summary>
        /// Indicates if the customer consented to Second Reg - Customer Registration Fee
        /// </summary>
        public bool SecondRegConsent { get; set; } = false;
        
        /// <summary>
        /// Sale Lead Number
        /// </summary>
        public string LeadNumber { get; set; } = string.Empty;
    }
}