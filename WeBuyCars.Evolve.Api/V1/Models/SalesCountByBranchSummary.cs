using System;
using System.Collections.Generic;

namespace WeBuyCars.Evolve.Api.V1.Models;

public sealed class SalesCountByBranchSummary
{
    public DateTimeOffset SaleDateTime { get; set; }
    public long TotalSalesCount { get; set; }
    public IEnumerable<SalesCountByBranchItem> BranchSales { get; set; }
}

public sealed class SalesCountByBranchItem
{
    public string BranchCode { get; set; }
    public string SalesType { get; set; }
    public string CustomerType { get; set; }
    public int Count { get; set; }
}