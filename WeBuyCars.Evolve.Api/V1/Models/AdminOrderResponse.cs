using System;
using System.Collections.Generic;

namespace WeBuyCars.Evolve.Api.V1.Models;

public sealed class AdminOrderResponse
{
    public string StockNumber { get; set; }
    
    public string OrderNumber { get; set; }
    
    public string OrderStatus { get; set; }
    
    public string OrderDetail { get; set; }
    
    public string OrderType { get; set; }
    
    public DateTimeOffset? DateCreated { get; set; }
    
    public string Category { get; set; }
    
    public List<AdminOrderLinesResult> OrderLines { get; set; }
}

public sealed class AdminOrderLinesResult
{
    public string AccessoryCode { get; set; }
    
    public string AccessoryDescription { get; set; }
    
    public decimal ValueExclVat { get; set; }
    
    public decimal Vat { get; set; }
    
    public decimal ValueInclVat { get; set; }
}