using MediatR;
using WeBuyCars.Evolve.Core.Enumerations;
using WeBuyCars.Evolve.Core.SharedKernel;
using WeBuyCars.Evolve.Infrastructure.Evolve.Enumerations;

namespace WeBuyCars.Evolve.Api.V1.Models
{
    public class DepositPaidEvent : IMessage, IRequest<bool>
    {
        public DepositPaidEvent
        (
            string customerAccountNumber,
            decimal amount,
            string transactionReference,
            PaymentProviderEnum paymentProvider)
        {
            CustomerAccountNumber = customerAccountNumber;
            DepositAmount = amount;
            TransactionReference = transactionReference;
            PaymentProvider = paymentProvider;
        }

        public string CustomerAccountNumber { get; set; }

        public decimal DepositAmount { get; set; }

        public string TransactionReference { get; set; }

        public PaymentProviderEnum PaymentProvider { get; set; }

        public EventType EventType => EventType.DepositPayment;
    }
}