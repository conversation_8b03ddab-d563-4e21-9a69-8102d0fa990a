using System.ComponentModel.DataAnnotations;

namespace WeBuyCars.Evolve.Api.V1.Models
{
    public class SettlementPaymentRequest
    {
        /// <summary>
        /// The account number.
        /// </summary>
        /// <remarks>
        /// This is the customer's account number as specified in CRM.
        /// </remarks>
        [Required]
        public string AccountNumber { get; set; }

        /// <summary>
        /// The payment amount.
        /// </summary>
        [Required]
        [Range(0, ********, ErrorMessage = "{0} must be between {1} and {2}.")]
        public decimal Amount { get; set; }

        /// <summary>
        /// The transaction reference number.
        /// </summary>
        public string TransactionReference { get; set; }

        /// <summary>
        /// The stock numbers of the vehicle which this payment should be allocated to.
        /// </summary>
        public string[] StockNumbers { get; set; }
    }
}
