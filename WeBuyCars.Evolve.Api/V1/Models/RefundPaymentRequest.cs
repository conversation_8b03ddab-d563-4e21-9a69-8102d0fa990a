using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using WeBuyCars.Evolve.Infrastructure.Evolve.Enumerations;

namespace WeBuyCars.Evolve.Api.V1.Models
{
    public class RefundPaymentRequest
    {
        /// <summary>
        /// Refund type
        /// </summary>
        public RefundType RefundType { get; set; } = RefundType.Auction;
        
        /// <summary>
        /// The account number.
        /// </summary>
        /// <remarks>
        /// This is the customer's account number as specified in CRM.
        /// </remarks>
        [Required]
        public string AccountNumber { get; set; }

        /// <summary>
        /// The deposit amount.
        /// </summary>
        [Required]
        [Range(0, ********, ErrorMessage = "{0} must be between {1} and {2}.")]
        public decimal Amount { get; set; }

        /// <summary>
        /// The transaction reference number.
        /// </summary>
        [Required]
        public string TransactionReference { get; set; }

        /// <summary>
        /// The payment provider.
        /// </summary>
        [Required]
        public PaymentProviderEnum PaymentProvider { get; set; }

        public override string ToString() => JsonConvert.SerializeObject(this, Formatting.Indented);
    }
}
