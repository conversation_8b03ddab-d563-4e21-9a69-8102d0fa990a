namespace WeBuyCars.Evolve.Api.V1.Models
{
    public class StockReconResponse
    {
        public int ImsStockNumberCount { get; set; }
        public int EvolveStockNumberCount { get; set; }
        public int Difference { get; set; }
        public StockItemReconResponse[]  StockNumbersInImsNotInEvolve { get; set; }
        public int StockNumbersInImsNotInEvolveCount { get; set; }
        public string[] StockNumbersInEvolveNotInIms { get; set; }
        public int StockNumbersInEvolveNotInImsCount { get; set; }
        public string[] StockNumbersCombinedDistinct { get; set; }
        public int StockNumbersCombinedDistinctCount { get; set; }
    }
}