using System;

namespace WeBuyCars.Evolve.Api.V1.Models
{
    public class DealSummary
    {
        public string Type { get; set; }

        public string Location { get; set; }

        public long Count { get; set; }

        public decimal Value { get; set; }

        public decimal GrossProfit { get; set; }

        public DateTimeOffset? Date { get; set; }
        
        // RFC - Return For Credit
        public long RfcCount { get; set; }
        
        public decimal RfcValue { get; set; }
        
        // 3rd Party (Repo)
        public long RepoCount { get; set; }
        
        public decimal RepoValue { get; set; }
        
        public long RfcRepoCount { get; set; }
        
        public decimal RfcRepoValue { get; set; }
    }
}