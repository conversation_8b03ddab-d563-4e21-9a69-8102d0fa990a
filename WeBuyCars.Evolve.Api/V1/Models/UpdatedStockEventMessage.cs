using System;
using Newtonsoft.Json;

namespace WeBuyCars.Evolve.Api.V1.Models
{
    public class UpdatedStockEventMessage
    {        
        public string Originator { get; set; }
        public string EventType { get; set; }
        public DateTimeOffset DateTime { get; set; }
        public UpdatedStockEventData Data { get; set; }

        public override string ToString() => JsonConvert.SerializeObject(this);
    }
}