namespace WeBuyCars.Evolve.Api.V1.Models.Evolve.StockSummary
{
    public sealed class StockSummaryStatic : StockSummary
    {
        /// <summary>
        /// The Total cost incl VAT for all the vehicles included in the qty count @1 above, for all vehicles where “SOLD” = N and != Cat “SSS”
        /// </summary>
        public override decimal StockForSaleValueIncl { get; set; }

        /// <summary>
        /// The Total cost incl VAT for all the vehicles included in the qty count @3 above, for all vehicles where “SOLD” = N and for Cat “SSS” only
        /// </summary>
        public override decimal? CompanyVehicleValueIncl { get; set; }

        /// <summary>
        /// The Total cost incl VAT for all vehicles where “SOLD” = N
        /// </summary>
        public override decimal VehicleStockTotalValueIncl { get; set; }

        public override decimal? RfcStockForSaleValueIncl { get; set; }

        public override decimal? RfcCompanyVehicleValueIncl { get; set; }

        public override decimal? RfcVehicleStockTotalValueIncl { get; set; }
        
        public override decimal? RepoStockForSaleValueIncl { get; set; }

        public override decimal? RepoVehicleStockTotalValueIncl { get; set; }
        
        public override decimal? RfcRepoStockForSaleValueIncl { get; set; }
        
        public override decimal? RfcRepoVehicleStockTotalValueIncl { get; set; }
    }
}