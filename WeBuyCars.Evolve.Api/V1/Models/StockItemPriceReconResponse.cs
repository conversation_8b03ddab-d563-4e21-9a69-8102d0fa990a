using System;
using WeBuyCars.Evolve.Api.V1.Enumerations;

namespace WeBuyCars.Evolve.Api.V1.Models;

public class StockItemPriceReconResponse
{
    public string StockNumber { get; }
    public string StockStatus { get; set; }
    public string ActivityStatus { get; set; }
    public decimal StockAdjustedCostPrice { get; }
    public decimal EvolveTotalAccountingCost { get; }
    public decimal EvolveTotalAccountingCostIncl { get; }
    public decimal Difference { get; }
    public DecimalRoundType DecimalRoundType { get; }
    public decimal RoundedStockAdjustedCostPrice { get; }
    public decimal RoundedEvolveTotalAccountingCostIncl { get; }

    public StockItemPriceReconResponse(
        string stockNumber, 
        string stockStatus, 
        string activityStatus,
        decimal stockAdjustedCostPrice,
        decimal evolveTotalAccountingCost,
        decimal evolveTotalAccountingCostIncl,
        DecimalRoundType decimalRoundType,
        decimal roundedStockAdjustedCostPrice,
        decimal roundedEvolveTotalAccountingCostIncl
    )
    {
        StockNumber = stockNumber;
        StockStatus = stockStatus;
        ActivityStatus = activityStatus;
        StockAdjustedCostPrice = stockAdjustedCostPrice;
        EvolveTotalAccountingCost = evolveTotalAccountingCost;
        EvolveTotalAccountingCostIncl = evolveTotalAccountingCostIncl;
        DecimalRoundType = decimalRoundType;
        RoundedStockAdjustedCostPrice = roundedStockAdjustedCostPrice;
        RoundedEvolveTotalAccountingCostIncl = roundedEvolveTotalAccountingCostIncl;

        Difference = stockAdjustedCostPrice - evolveTotalAccountingCostIncl;
    }

    public override bool Equals(object obj)
    {
        return obj is StockItemPriceReconResponse other &&
               StockNumber == other.StockNumber &&
               StockAdjustedCostPrice == other.StockAdjustedCostPrice &&
               EvolveTotalAccountingCost == other.EvolveTotalAccountingCost &&
               EvolveTotalAccountingCostIncl == other.EvolveTotalAccountingCostIncl;
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(StockNumber, StockAdjustedCostPrice, EvolveTotalAccountingCost, EvolveTotalAccountingCostIncl);
    }
}