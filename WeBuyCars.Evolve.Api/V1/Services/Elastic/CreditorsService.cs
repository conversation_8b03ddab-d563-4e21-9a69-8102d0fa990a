using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AutoMapper;
using WeBuyCars.Evolve.Api.V1.Models.AP;
using WeBuyCars.Evolve.Api.V1.Services.Elastic.Interfaces;
using WeBuyCars.Evolve.Infrastructure.Data.Elasticsearch;
using WeBuyCars.Evolve.Infrastructure.Evolve.Entities.Responses.AccountSummary;

namespace WeBuyCars.Evolve.Api.V1.Services.Elastic;

public class CreditorsService(ElasticClientProvider clientProvider, IMapper mapper)
    : ElasticSearchService<AccountDetail>(clientProvider, clientProvider.CreditorsIndex), ICreditorsService
{
    #region Class Members
    
    private readonly IMapper _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));

    #endregion
 
    #region Public Methods
    
    public async Task<IEnumerable<SundryAP>> GetSundryAPCreditors()
    {
        var response = await Client.SearchAsync<AccountDetail>(s => s
            .Index(IndexName)
            .Size(50000)
        );

        if (!response.IsValidResponse)
        {
            var retrievedException = response.TryGetOriginalException(out var originalException);
            if (retrievedException)
                throw originalException!;
        }

        var responseItems = _mapper.Map<List<SundryAP>>(response.Documents);
        
        return responseItems;
    }
    
    #endregion
}