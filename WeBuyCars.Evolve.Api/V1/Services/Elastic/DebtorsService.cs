using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.Aggregations;
using Elastic.Clients.Elasticsearch.QueryDsl;
using WeBuyCars.Evolve.Api.V1.Models;
using WeBuyCars.Evolve.Api.V1.Models.Evolve.AccountSummaryResponse;
using WeBuyCars.Evolve.Api.V1.Models.OrganizationLocations;
using WeBuyCars.Evolve.Api.V1.Services.Elastic.Interfaces;
using WeBuyCars.Evolve.Core.SharedKernel;
using WeBuyCars.Evolve.Infrastructure.Data.Elasticsearch;
using WeBuyCars.Evolve.Infrastructure.Evolve.Entities.Responses.AccountSummary;

namespace WeBuyCars.Evolve.Api.V1.Services.Elastic;

public class DebtorsService : ElasticSearchService<AccountDetail>, IDebtorService
{
    #region Class Fields

    private readonly OrganizationLocationService _organizationLocationService;
    private readonly IMapper _mapper;
    
    #endregion

    #region Constructors

    public DebtorsService(ElasticClientProvider clientProvider, OrganizationLocationService organizationLocationService, 
        IMapper mapper)
        :base(clientProvider, clientProvider.DebtorsIndex)
    {
        _organizationLocationService = organizationLocationService ??
                                       throw new ArgumentNullException(nameof(organizationLocationService));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
    }

    #endregion

    #region Public Methods

    public async Task<PagedResult<AccountDetailResponse>> GetPagedAsync(int pageSize, int pageNumber, 
        Sort<AccountDetail> sort)
    {
        var sortField = $"{sort.Property}.keyword";

        var request = new SearchRequest<AccountDetail>(IndexName)
        {
            Query = new MatchAllQuery(),
            From = pageSize * pageNumber,
            Size = pageSize,
            Aggregations = new AggregationDictionary {
                { "balance_total", new SumAggregation("balance_total", new Field("accountBalance")) },
                { "invoice_total", new SumAggregation("invoice_total", new Field("lastInvoiceAmount")) }
            }
        };

        if (sort.PropertyType == typeof(decimal?))
        {
            sortField = $"{sort.Property}";
        }

        if (sort.PropertyType == typeof(DateTime?))
        {
            sortField = $"{sort.Property}";
        }

        var esSort = sort.Direction switch
        {
            SortDirection.Ascending => SortOrder.Asc,
            SortDirection.Descending => SortOrder.Desc,
            _ => throw new ArgumentOutOfRangeException(nameof(sort))
        };
        request.Sort = new List<SortOptions> 
        {
            SortOptions.Field(new Field(sortField), new FieldSort{ Order = esSort })
        };

        var response = await Client.SearchAsync<AccountDetail>(request);

        if (!response.IsValidResponse)
        {
            var retrievedException = response.TryGetOriginalException(out var originalException);
            if (retrievedException)
                throw originalException!;
        }

        var totalBalanceAmount = response.Aggregations?.GetSum("balance_total")?.Value;
        var totalInvoiceAmount = response.Aggregations?.GetSum("invoice_total")?.Value;

        var responseItems = _mapper.Map<List<AccountDetailResponse>>(response.Documents);
        
        foreach (var responseItem in responseItems)
        {
            responseItem.Branch =
                await _organizationLocationService.GetBranchNameByCode(
                    new GetBranchNameByCodeRequest(responseItem.BranchCode), CancellationToken.None);
        }
        
        return new PagedResult<AccountDetailResponse>
        {
            Items = responseItems,
            TotalRows = response.HitsMetadata.Total?.Value ?? 0,
            Aggregations = new Dictionary<string, object>{
                { "totalBalanceAmount", totalBalanceAmount },
                { "totalInvoiceAmount", totalInvoiceAmount }
            }
        };
    }

    public async Task<PagedResult<AccountDetailResponse>> GetCurrentPagedAsync(int pageSize, int pageNumber, Sort<AccountDetail> sort)
    {
        var sortField = $"{sort.Property}.keyword";

        var request = new SearchRequest<AccountDetail>(IndexName)
        {
            Query = new DateRangeQuery(new Field("lastUpdated"))
            {
                Gte = DateMath.Now.Subtract(TimeSpan.FromHours(3))
            },
            From = pageSize * pageNumber,
            Size = pageSize,
            Aggregations = new AggregationDictionary {
                { "balance_total", new SumAggregation("balance_total", new Field("accountBalance")) },
                { "invoice_total", new SumAggregation("invoice_total", new Field("lastInvoiceAmount")) }
            }
        };

        if (sort.PropertyType == typeof(decimal?))
        {
            sortField = $"{sort.Property}";
        }

        if (sort.PropertyType == typeof(DateTime?))
        {
            sortField = $"{sort.Property}";
        }

        var esSort = sort.Direction switch
        {
            SortDirection.Ascending => SortOrder.Asc,
            SortDirection.Descending => SortOrder.Desc,
            _ => throw new ArgumentOutOfRangeException(nameof(sort))
        };
        request.Sort = new List<SortOptions>
        {
            SortOptions.Field(new Field(sortField), new FieldSort { Order = esSort })
        };

        var response = await Client.SearchAsync<AccountDetail>(request);

        if (!response.IsValidResponse)
        {
            var retrievedException = response.TryGetOriginalException(out var originalException);
            if (retrievedException)
                throw originalException!;
        }

        var totalBalanceAmount = response.Aggregations?.GetSum("balance_total")?.Value;
        var totalInvoiceAmount = response.Aggregations?.GetSum("invoice_total")?.Value;

        var responseItems = _mapper.Map<List<AccountDetailResponse>>(response.Documents);
        
        foreach (var responseItem in responseItems)
        {
            responseItem.Branch =
                await _organizationLocationService.GetBranchNameByCode(
                    new GetBranchNameByCodeRequest(responseItem.BranchCode), CancellationToken.None);
        }

        return new PagedResult<AccountDetailResponse>
        {
            Items = responseItems,
            TotalRows = response.HitsMetadata.Total?.Value ?? 0,
            Aggregations = new Dictionary<string, object>{
                { "totalBalanceAmount", totalBalanceAmount },
                { "totalInvoiceAmount", totalInvoiceAmount }
            }
        };
    }

    public async Task<IEnumerable<AccountDetailResponse>> GetCurrentByStockNumbers(List<string> stockNumbers)
    {
        var esStockNumbers = stockNumbers.Select(FieldValue.String).ToList();
        
        var response = await Client.SearchAsync<AccountDetail>(s => s
            .Index(IndexName)
            .Query(q => q
                .Bool(b => b
                    .Must(m => m
                        .Terms(ad => ad.Field(f => f.VehicleStockNo.Suffix("keyword")).Terms(new TermsQueryField(esStockNumbers)))
                        .Range(r => r
                            .DateRange(date => date
                                .Field(f => f.LastUpdated)
                                .Gte(DateMath.Now.Subtract(TimeSpan.FromHours(3)))
                            )
                        )
                    )
                )
            )
        );

        if (!response.IsValidResponse)
        {
            var retrievedException = response.TryGetOriginalException(out var originalException);
            if (retrievedException)
                throw originalException!;
        }

        var responseItems = _mapper.Map<List<AccountDetailResponse>>(response.Documents);
        
        foreach (var responseItem in responseItems)
        {
            responseItem.Branch =
                await _organizationLocationService.GetBranchNameByCode(
                    new GetBranchNameByCodeRequest(responseItem.BranchCode), CancellationToken.None);
        }
        
        return responseItems;
    }

    public async Task<IEnumerable<Summary<string>>> GetBranchTotalsAsync()
    {
        var response = await Client.SearchAsync<AccountDetail>(s => s
            .Index(IndexName)
            .Aggregations(a => a
                .Terms("branches", t => t
                    .Field(p => p.BranchCode.Suffix("keyword")
                    )
                    .Aggregations(c => c
                        .Sum("balance", sum => sum.Field(p => p.AccountBalance))
                    ))
            ));

        if (!response.IsValidResponse)
        {
            var retrievedException = response.TryGetOriginalException(out var originalException);
            if (retrievedException)
                throw originalException!;
        }

        var termsAggregate = response
            .Aggregations?
            .GetStringTerms("branches");

        var summaryItems = termsAggregate?.Buckets.Select(i => new Summary<string>
        {
            Key = i.Key.ToString(),
            Count = i.DocCount,
            Value = (decimal)(i.GetSum("balance")?.Value ?? 0)
        }).ToList();

        foreach (var summaryItem in summaryItems ?? new List<Summary<string>>())
        {
            summaryItem.Key =
                await _organizationLocationService.GetBranchNameByCode(new GetBranchNameByCodeRequest(summaryItem.Key),
                    CancellationToken.None);
        }
        
        return summaryItems;
    }

    public async Task<IEnumerable<Summary<string>>> GetCurrentBranchTotalsAsync()
    {
        var response = await Client.SearchAsync<AccountDetail>(s => s
            .Index(IndexName)
            .Query(q => q
                .Range(r => r
                    .DateRange(dr => dr
                        .Field(f => f.LastUpdated)
                        .Gte(DateMath.Now.Subtract(TimeSpan.FromHours(3)))
                    )
                )
            )
            .Aggregations(a => a
                .Terms("branches", t => t
                    .Field(p => p.BranchCode.Suffix("keyword")
                    )
                    .Aggregations(c => c
                        .Sum("balance", sum => sum.Field(p => p.AccountBalance))
                    ))
            ));

        if (!response.IsValidResponse)
        {
            var retrievedException = response.TryGetOriginalException(out var originalException);
            if (retrievedException)
                throw originalException!;
        }

        var buckets = response
            .Aggregations?
            .GetStringTerms("branches")?
            .Buckets 
                      ?? new List<StringTermsBucket>();

        var summaryItems = buckets.Select(i => new Summary<string>
        {
            Key = i.Key.ToString(),
            Count = i.DocCount,
            Value = (decimal)(i.GetSum("balance")?.Value ?? 0)
        }).ToList();
        
        foreach (var summaryItem in summaryItems)
        {
            summaryItem.Key =
                await _organizationLocationService.GetBranchNameByCode(new GetBranchNameByCodeRequest(summaryItem.Key),
                    CancellationToken.None);
        }
        
        return summaryItems;
    }

    public async Task<IEnumerable<Summary<DateTime>>> GetTrendAsync(DateTime from, DateTime to)
    {
        var response = await Client.SearchAsync<AccountDetail>(s => s
            .Index(IndexName)
            .Query(q => q
                .Range(r => r
                    .DateRange(dr => dr
                        .Field(f => f.Date)
                        .Gte(from)
                        .Lte(to)
                    )
                )
            )
            .Aggregations(a => a
                .DateHistogram("debtors_over_time", h => h
                    .Field(f => f.Date)
                    .CalendarInterval(CalendarInterval.Day)
                    .Aggregations(ca => ca
                        .Stats("account_balance_stats", b => b.Field(f => f.AccountBalance))
                        .Stats("last_invoice_amount_stats", i => i.Field(f => f.LastInvoiceAmount))
                    )
                )
            )
        );

        if (response.IsValidResponse)
            return response.Aggregations?.GetDateHistogram("debtors_over_time")?.Buckets.Select(i =>
                new Summary<DateTime>
                {
                    Key = DateTime.Parse(i.KeyAsString ?? string.Empty),
                    Count = i.DocCount,
                    Value = (decimal)(i.GetStats("account_balance_stats")?.Sum ?? 0),
                    Minimum = (decimal)(i.GetStats("account_balance_stats")?.Min ?? 0),
                    Maximum = (decimal)(i.GetStats("account_balance_stats")?.Max ?? 0),
                    Average = (decimal)(i.GetStats("account_balance_stats")?.Avg ?? 0)
                });
        
        var retrievedException = response.TryGetOriginalException(out var originalException);
        if (retrievedException)
            throw originalException!;

        return response.Aggregations?.GetDateHistogram("debtors_over_time")?.Buckets.Select(i => 
            new Summary<DateTime>
        {
            Key = DateTime.Parse(i.KeyAsString ?? string.Empty),
            Count = i.DocCount,
            Value = (decimal)(i.GetStats("account_balance_stats")?.Sum ?? 0),
            Minimum = (decimal)(i.GetStats("account_balance_stats")?.Min ?? 0),
            Maximum = (decimal)(i.GetStats("account_balance_stats")?.Max ?? 0),
            Average = (decimal)(i.GetStats("account_balance_stats")?.Avg ?? 0)
        });
    }

    #endregion
}