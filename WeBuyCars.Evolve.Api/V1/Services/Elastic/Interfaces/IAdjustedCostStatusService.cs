using System.Threading;
using System.Threading.Tasks;
using WeBuyCars.Core.Infrastructure.DynamicLinq.DynamicFilter;
using WeBuyCars.Evolve.Api.V1.Models.ElasticSearch;

namespace WeBuyCars.Evolve.Api.V1.Services.Elastic.Interfaces;

public interface IAdjustedCostStatusService
{
    Task<DynamicLinqResult> GetAdjustedCostStatusAsync(DynamicLinqRequest request, CancellationToken cancellationToken);
    
    Task InsertAsync(AdjustedCostStatus summary);

    Task DeleteAsync(AdjustedCostStatus item);
}