using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using WeBuyCars.Core.Exceptions;
using WeBuyCars.Core.Infrastructure.ServiceBus.Azure;
using WeBuyCars.Core.Infrastructure.ServiceBus.Interface;
using WeBuyCars.Evolve.Api.V1.Models.ServiceBus.Topic;

namespace WeBuyCars.Evolve.Api.V1.Services.ServiceBus;

public class AccountBalanceEventService
{
    #region Class Fields

    private readonly AccountService _accountService;
    
    private readonly IServiceBusTopic _serviceBusTopic;
    private readonly AzureServiceBusFactory _serviceBusFactory;

    private readonly ILogger<AccountBalanceEventService> _logger;

    private const string TopicName = "evolve";

    #endregion

    #region Constructors

    public AccountBalanceEventService(IServiceBusTopic serviceBusTopic, AzureServiceBusFactory serviceBusFactory, 
        AccountService accountService, ILogger<AccountBalanceEventService> logger)
    {
        _serviceBusTopic = serviceBusTopic ?? throw new ArgumentNullException(nameof(serviceBusTopic));
        _serviceBusFactory = serviceBusFactory ?? throw new ArgumentNullException(nameof(serviceBusFactory));
        _accountService = accountService ?? throw new ArgumentNullException(nameof(accountService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    #endregion
    
    #region Public Methods

    public async Task PublishAccountBalance(string accountNumber, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(accountNumber))
            throw new ArgumentNullException(nameof(accountNumber));
        
        var accountBalance = await GetLatestAccountBalance(accountNumber, cancellationToken);

        if (accountBalance is null)
        {
            _logger.LogError("{MethodName}: Failed to get account balance for account {AccountNumber}",
                nameof(PublishAccountBalance),
                accountNumber);
            return;
        }

        await SendMessageToTopic(accountBalance, cancellationToken);
    }

    #endregion

    #region Private Methods

    private async Task<AccountBalanceEventDto> GetLatestAccountBalance(string accountNumber, 
        CancellationToken cancellationToken)
    {
        var accountBalanceResponse = await _accountService.GetAccountBalanceAsync(accountNumber);

        if (accountBalanceResponse?.Balance is null || accountBalanceResponse.DepositBalance is null)
            throw new DomainException("Account Balance Event Error: Failed to get account balance");

        return new AccountBalanceEventDto
        {
            AccountNumber = accountBalanceResponse.AccountNumber,
            Balance = (decimal)accountBalanceResponse.Balance,
            DepositBalance = (decimal)accountBalanceResponse.DepositBalance
        };
    }

    private async Task SendMessageToTopic(AccountBalanceEventDto accountBalanceEventDto, 
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("{MethodName} [{TopicName}]: Publishing Account Balance Event: {MessageContent}",
            nameof(SendMessageToTopic),
            TopicName,
            JsonConvert.SerializeObject(accountBalanceEventDto, Formatting.Indented));
        
        var serviceBusTopic = _serviceBusFactory.GetTopicByName(TopicName);

        if (serviceBusTopic is null)
            throw new DomainException(
                "Failed to publish account balance event, Could not find valid service bus topic");
        
        await _serviceBusTopic.SendMessageAsync
        (
            serviceBusTopic.TopicInstance.Name,
            accountBalanceEventDto,
            "application/json",
            new Dictionary<string, object>
            {
                { "EventType", "AccountBalanceEvent" }
            },
            cancellationToken: cancellationToken
        );
    }

    #endregion
}