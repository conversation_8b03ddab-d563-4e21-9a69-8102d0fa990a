using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WeBuyCars.Core.Exceptions;
using WeBuyCars.Core.Infrastructure.DynamicLinq.DynamicFilter;
using WeBuyCars.Evolve.Api.V1.Models;
using WeBuyCars.Evolve.Api.V1.Models.ElasticSearch;
using WeBuyCars.Evolve.Api.V1.Services.Elastic.Interfaces;

namespace WeBuyCars.Evolve.Api.V1.Services.Blms;

public sealed class BlmsFlagService
{
    #region Fields
    
    private readonly IStockService _stockService;
    private readonly IAdjustedCostStatusService _adjustedCostStatusService;

    #endregion
    
    #region Constructors

    public BlmsFlagService(IStockService stockService, IAdjustedCostStatusService adjustedCostStatusService)
    {
        _stockService = stockService ?? throw new ArgumentNullException(nameof(stockService));
        _adjustedCostStatusService = adjustedCostStatusService ??
                                     throw new ArgumentNullException(nameof(adjustedCostStatusService));
    }
    
    #endregion

    #region Public Methods

    public async Task ProcessCompletedFlagCreated(string buyLeadCode, CancellationToken cancellationToken)
    {
        var stockSearchRequest = new DynamicLinqRequest(
            1, 
            0, 
            null,
            new DynamicLinqFilter(
                new List<DynamicLinqFilterField>
                {
                    new("buyLeadCode.keyword", "eq", buyLeadCode)
                },
                new List<DynamicLinqFilter>(), "and"
            ), 
            null, 
            null
        );

        var stockItemResult = await _stockService.GetStockAsync(stockSearchRequest, cancellationToken);
        
        if (stockItemResult.Total == 0)
            throw new DomainException(
                $"[Buy Lead Code - {buyLeadCode}] {nameof(ProcessCompletedFlagCreated)}: Evolve stock not found for buy lead code");
        
        var stockItem = stockItemResult.Data.Cast<Vehicle>().First();

        var cacheSearchRequest = new DynamicLinqRequest(
            1, 
            0, 
            null,
            new DynamicLinqFilter(
                new List<DynamicLinqFilterField>
                {
                    new("stockNumber.keyword", "eq", stockItem.StockNumber)
                },
                new List<DynamicLinqFilter>(), "and"
            ), 
            null, 
            null
        );

        var cacheItemResult =
            await _adjustedCostStatusService.GetAdjustedCostStatusAsync(cacheSearchRequest, cancellationToken);

        if (cacheItemResult.Total == 0)
            throw new DomainException(
                $"[Stock Number - {stockItem.StockNumber}] {nameof(ProcessCompletedFlagCreated)}: Could not find adjusted cost price cache item for stock number {stockItem.StockNumber}");

        var cacheItem = cacheItemResult.Data.Cast<AdjustedCostStatus>().First();

        await _adjustedCostStatusService.DeleteAsync(cacheItem);
    }

    #endregion
}