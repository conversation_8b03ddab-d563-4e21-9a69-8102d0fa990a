using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WeBuyCars.Evolve.Api.V1.Models;
using WeBuyCars.Evolve.Api.V1.Models.Evolve.Sales;
using WeBuyCars.Evolve.Api.V1.Models.Evolve.StockSummary;
using WeBuyCars.Evolve.Core.Enumerations;
using WeBuyCars.Evolve.Core.SharedKernel;

namespace WeBuyCars.Evolve.Api.V1.Services;

public sealed class ImsSnapshotService
{
    #region Fields

    private readonly IImsDailySalesSnapshotRepository _imsDailySalesSummaryRepository;
    private readonly IImsDailyBuysSnapshotRepository _imsStockSummaryRepository;

    #endregion

    #region Constructors

    public ImsSnapshotService(IImsDailySalesSnapshotRepository imsDailySalesSummaryRepository,
                              IImsDailyBuysSnapshotRepository imsStockSummaryRepository)
    {
        _imsDailySalesSummaryRepository = imsDailySalesSummaryRepository ?? throw new ArgumentNullException(nameof(imsDailySalesSummaryRepository));
        _imsStockSummaryRepository = imsStockSummaryRepository ?? throw new ArgumentNullException(nameof(imsStockSummaryRepository));
    }

    #endregion

    #region Public Methods

    public async Task<DailySalesSummaryStatic> GetDailySalesSnapshotData(DateTimeOffset date, CancellationToken cancellationToken)
    {
        var result = await _imsDailySalesSummaryRepository.GetDailySalesSummary(date, cancellationToken);

        var branchGroups = result.GroupBy(
            k => (k.BranchCode, k.Branch),
            v => v,
            (key, value) => new
            {
                Branch = key,
                DailySale = value
            });

        var locationSalesSummaries = branchGroups.Select(x => new LocationSalesSummaryStatic()
        {
            Category = $"{x.Branch.BranchCode} {SalesType.Cash.ToString()} {ClientType.Private.ToString()}",
            BranchCode = x.Branch.BranchCode,
            SalesType = SalesType.Cash,
            ClientType = ClientType.Private,
            Count = x.DailySale.Count(),
            Value = x.DailySale.Sum(y => y.BuyNowPrice),
            ValueIncl = x.DailySale.Sum(y => y.BuyNowPrice),
            GrossProfit = x.DailySale.Sum(y => y.BuyNowPrice) - x.DailySale.Sum(y => y.CostPrice),
        }).ToList();

        return new DailySalesSummaryStatic
        {
            Count = locationSalesSummaries.Sum(x => x.Count),
            Value = locationSalesSummaries.Sum(x => x.Value),
            ValueIncl = locationSalesSummaries.Sum(x => x.Value),
            LastUpdated = DateTime.Now,
            LocationSalesSummaries = locationSalesSummaries,
            Date = date.Date,
        };
    }

    public async Task<StockSummary> GetDailyBuysSnapshotData(DateTimeOffset date, CancellationToken cancellationToken)
    {
        var result = await _imsStockSummaryRepository.GetDailyBuysSummary(date, cancellationToken);

        var totalCostPrice = result.Sum(x => x.CostPrice);

        return new StockSummaryStatic
        {
            CompanyVehicleCount = 0,
            CompanyVehicleValue = 0,
            CompanyVehicleValueIncl = 0,
            StockForSaleCount = result.Count,
            StockForSaleValue = totalCostPrice,
            StockForSaleValueIncl = totalCostPrice,
            VehicleStockCount = 0 + result.Count,
            VehicleStockTotalValue = 0 + totalCostPrice,
            VehicleStockTotalValueIncl = 0 + totalCostPrice,
            RfcCompanyVehicleCount = 0,
            RfcCompanyVehicleValue = 0,
            RfcCompanyVehicleValueIncl = 0,
            RfcStockForSaleCount = 0,
            RfcStockForSaleValue = 0,
            RfcStockForSaleValueIncl = 0,
            RfcVehicleStockCount = 0,
            RfcVehicleStockTotalValue = 0,
            RfcVehicleStockTotalValueIncl = 0,
            RepoStockForSaleCount = 0,
            RepoStockForSaleValue = 0,
            RepoStockForSaleValueIncl = 0,
            RepoVehicleStockCount = 0,
            RepoVehicleStockTotalValue = 0,
            RepoVehicleStockTotalValueIncl = 0,
            RfcRepoStockForSaleCount = 0,
            RfcRepoStockForSaleValue = 0,
            RfcRepoStockForSaleValueIncl = 0,
            RfcRepoVehicleStockCount = 0,
            RfcRepoVehicleStockTotalValue = 0,
            RfcRepoVehicleStockTotalValueIncl = 0,
            LastUpdated = DateTime.Now,
            Date = date.Date,
        };
    }

    #endregion
}