using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Logging;
using WeBuyCars.Core.Exceptions;
using WeBuyCars.Evolve.Api.V1.Interfaces;
using WeBuyCars.Evolve.Api.V1.Models;
using WeBuyCars.Evolve.Api.V1.Models.OrganizationLocations;
using WeBuyCars.Evolve.Core.Entities;
using WeBuyCars.Evolve.Core.Enumerations;
using WeBuyCars.Evolve.Core.Extensions;
using WeBuyCars.Evolve.Core.SharedKernel;
using WeBuyCars.Evolve.Infrastructure.Evolve.Services;
using WeBuyCars.Evolve.Infrastructure.Evolve.Entities.Requests;
using WeBuyCars.Evolve.Infrastructure.Finance.Services.Interfaces;
using WeBuyCars.Evolve.Infrastructure.IMS.Enumerations;

namespace WeBuyCars.Evolve.Api.V1.Services.Evolve;

public class StockSyncService
{
    #region Class Members

    private readonly ICustomerRepository _customerRepository;
    private readonly ICatalogueItemRepository _catalogueItemRepository;
    private readonly IFinanceService _financeService;
    private readonly IBuyLeadEvaluationRepository _buyLeadEvaluationRepository;
    private readonly VehicleStockMaintenanceService _vehicleStockMaintenanceService;
    private readonly IEvolveStockItemRepository _evolveStockItemRepository;
    private readonly IWebHostEnvironment _environment;
    private readonly OrganizationLocationService _organizationLocationService;
    private readonly IRepoService _repoService;
    private readonly ILogger<StockSyncService> _logger;

    #endregion

    #region Constructors

    public StockSyncService
    (
        ICustomerRepository customerRepository,
        ICatalogueItemRepository catalogueItemRepository,
        IFinanceService financeService,
        IBuyLeadEvaluationRepository buyLeadEvaluationRepository,
        IEvolveStockItemRepository evolveStockItemRepository,
        VehicleStockMaintenanceService vehicleStockMaintenanceService,
        IWebHostEnvironment environment, OrganizationLocationService organizationLocationService, 
        IRepoService repoService,
        ILogger<StockSyncService> logger
    )
    {
        _customerRepository = customerRepository ?? throw new ArgumentNullException(nameof(customerRepository));
        _catalogueItemRepository =
            catalogueItemRepository ?? throw new ArgumentNullException(nameof(catalogueItemRepository));
        _financeService =
            financeService ?? throw new ArgumentNullException(nameof(financeService));
        _buyLeadEvaluationRepository = buyLeadEvaluationRepository ??
                                       throw new ArgumentNullException(nameof(buyLeadEvaluationRepository));
        _evolveStockItemRepository = evolveStockItemRepository ??
                                     throw new ArgumentNullException(nameof(evolveStockItemRepository));
        _vehicleStockMaintenanceService = vehicleStockMaintenanceService ??
                                          throw new ArgumentNullException(nameof(vehicleStockMaintenanceService));
        _environment = environment ?? throw new ArgumentNullException(nameof(environment));
        _organizationLocationService = organizationLocationService ??
                                       throw new ArgumentNullException(nameof(organizationLocationService));
        _repoService = repoService;
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    #endregion

    #region Public Methods

    public async Task PostVehicleStockMaintenanceToEvolve(StockItemDTO message, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(message.VinNumber))
        {
            throw new DomainException(
                $"'{nameof(message.VinNumber)}': '{message.VinNumber}'. '{nameof(message.VinNumber)}' must be set");
        }
        
        Customer customer;
        var accountNumber = message.CrmSellerAccNumber;

        try
        {
            customer = await _customerRepository.GetByAccountNumberAsync(accountNumber);
        }
        catch (Exception ex)
        {
            throw new InfrastructureException(
                $"Failed to retrieve customer by '{nameof(accountNumber)}': '{accountNumber}'. {nameof(NewStockEventMessage)}",
                ex);
        }

        var location =
            await _organizationLocationService.GetLocationByName(new GetBranchNameByNameRequest(message.Location),
                CancellationToken.None);
        if (location == null)
        {
            throw new DomainException(
                $"Failed to find the organization {nameof(message.Location)}: '{message.Location}' for the {nameof(message)}: {message}");
        }

        var VAT = await _financeService.GetFinancialConstantValueAsync(FinancialConstantsTypeEnum.Vat, CancellationToken.None);

        var catalogueId = message.CatalogueId;
        if (string.IsNullOrWhiteSpace(catalogueId) || catalogueId == "NotSet")
        {
            throw new DomainException(
                $"[Env:{_environment.EnvironmentName}] '{nameof(catalogueId)}': '{catalogueId}'. '{nameof(catalogueId)}' must be set. ");
        }

        var regYear = message.RegistrationYear ?? 0;
        var catalogueItem = await _catalogueItemRepository.GetByCatalogueIdAndRegYearAsync(catalogueId, regYear);
        if (catalogueItem == null)
        {
            throw new DomainException(
                $"Failed to retrieve {nameof(catalogueItem)} for '{nameof(catalogueId)}': '{catalogueId}' and '{nameof(regYear)}': '{regYear}'. {nameof(NewStockEventMessage)}");
        }

        var fuelType = catalogueItem.FuelType switch
        {
            "Petrol" => "P",
            "Diesel" => "D",
            "Electric" => "E",
            "Hybrid" => "H",
            _ => ""
        };

        var buyLeadId = message.BuyLeadCode;
        var buyLeadEvaluation = await _buyLeadEvaluationRepository.GetByBuyLeadIdAsync(buyLeadId);
        if (buyLeadEvaluation == null)
        {
            throw new InfrastructureException(
                $"Failed to retrieve {nameof(buyLeadEvaluation)} for '{nameof(buyLeadId)}': '{buyLeadId}'. {nameof(NewStockEventMessage)}");
        }

        var repoPrefix = await _repoService.GetRepoPrefix(message.StockNumber, cancellationToken);
        
        var request = new VehicleStockMaintenanceRequest
        {
            RowId = 1,
            VehicleStockDetail = new FullVehicleStockDetail
            {
                APAccountNumber = customer.AccountNumber,
                CurrentPrice = (message.CostPrice ?? 0) / VAT,
                DateStocked = ConvertToSouthAfricanTime(message.PurchaseDate).ToString("dd-MM-yyyy"),
                EngineNumber = message.EngineNumber,
                ExtColor = message.Colour,
                FranchiseCode = catalogueItem.MakeDisplayName,
                LisenseNumber = message.RegistrationNumber,
                MandMCode = catalogueItem.MMCode,
                ManufactureYear = regYear,
                MVRegNo = message.RegisterNumber,
                OdoReading = message.Odometer,
                OEMModel = catalogueItem.MMCode,
                RegistrationDate = new DateTime(regYear, 1, 1).ToString("dd/MM/yyyy"),
                TotalPurchaseInvoice = (message.CostPrice ?? 0) / VAT,
                VehicleLocation = location.WarehouseCode,
                VehStockNo = message.StockNumber,
                VehVinNumber = message.VinNumber,
                VehCatCode = message.StockStatus == StockStatusEnumeration.CompanyVehicle.GetDescription() 
                    ? $"{repoPrefix}SSS"
                    : repoPrefix + location.WarehouseCode,
                FuelType = fuelType,
                SupplierName = $"{customer.FirstName} {customer.LastName}",
                Condition = buyLeadEvaluation.Condition,
                FullServiceHistory = buyLeadEvaluation.FullServiceHistory,
                WarrantyActive = buyLeadEvaluation.WarrantyActive,
                FactoryJobNo = buyLeadId,
                CertificationNo = buyLeadId,
                VendorInvoice = buyLeadId,
                VendorInvoiceDate = ConvertToSouthAfricanTime(message.PurchaseDate).ToString("dd-MM-yyyy")
            }
        };

        var evolveStockItem = await _evolveStockItemRepository.GetByStockNumberAsync(message.StockNumber);

        if (evolveStockItem is null)
        {
            evolveStockItem = new EvolveStockItem(message.StockNumber);

            _evolveStockItemRepository.Add(evolveStockItem);

            await _evolveStockItemRepository.UnitOfWork.SaveChangesAsync();
        }

        try
        {
            var result = await _vehicleStockMaintenanceService.PostVehicleStockMaintenanceAsync(request);

            if (result.RowStatus == "S")
            {
                evolveStockItem = await _evolveStockItemRepository.GetByStockNumberAsync(message.StockNumber);

                if (!evolveStockItem.EvolveCreationSuccess.HasValue || !evolveStockItem.EvolveCreationSuccess.Value)
                {
                    evolveStockItem.CreatedSuccessfully(result.RequestXML, result.ResponseXML);

                    _evolveStockItemRepository.Update(evolveStockItem);

                    await _evolveStockItemRepository.UnitOfWork.SaveChangesAsync();
                }
            }
        }
        catch (DomainException)
        {
            throw;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to post vehicle stock maintenance to Evolve for {StockNumber}", message.StockNumber);
            throw;
        }
    }
    
    #endregion
    
    #region Private Methods

    private static DateTimeOffset ConvertToSouthAfricanTime(DateTimeOffset dateTimeOffset)
    {
        return dateTimeOffset.ToOffset(TimeSpan.FromHours(2));
    }

    #endregion
}