using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using WeBuyCars.Core.Infrastructure.ServiceBus.Azure;
using WeBuyCars.Core.Infrastructure.ServiceBus.Azure.Configurations;
using WeBuyCars.Core.Infrastructure.ServiceBus.Interface;
using WeBuyCars.Core.SharedKernel;
using WeBuyCars.Evolve.Api.V1.Models;
using WeBuyCars.Evolve.Api.V1.Services;

namespace WeBuyCars.Evolve.Api.V1.HostedServices
{
    public class UpdatedStockStatusEventProcessorBackgroundService : HostedService
    {
        private readonly string _subscriptionId = "inventory-updated-stock-status";
        private readonly IServiceProvider _serviceProvider;
        private readonly AzureServiceBusSubscriptionSetting _azureServiceBusSubscriptionSetting;

        public UpdatedStockStatusEventProcessorBackgroundService(IServiceProvider serviceProvider, AzureServiceBusFactory azureServiceBusFactory)
        {
            _serviceProvider = serviceProvider;
            _ = azureServiceBusFactory ?? throw new ArgumentNullException(nameof(azureServiceBusFactory));
            _azureServiceBusSubscriptionSetting = azureServiceBusFactory.GetSubscriptionSettingsById(_subscriptionId);
        }

        protected override async Task ExecuteAsync(CancellationToken cancellationToken)
        {
            await StartProcessingAsync(cancellationToken);
        }

        private async Task StartProcessingAsync(CancellationToken cancellationToken)
        {
            using (var scope = _serviceProvider.CreateScope())
            {
                var processor = scope.ServiceProvider.GetRequiredService<IServiceBusSubscriptionProcessor<UpdatedStockStatusEventMessage, StockUpdateService>>();

                await processor.StartProcessingAsync(_azureServiceBusSubscriptionSetting.SubscriptionName, cancellationToken);
            }
        }
    }
}
