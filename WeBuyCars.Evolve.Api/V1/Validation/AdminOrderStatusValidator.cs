using FluentValidation;
using WeBuyCars.Evolve.Api.V1.Models;

namespace WeBuyCars.Evolve.Api.V1.Validation;

public class AdminOrderStatusValidator : AbstractValidator<StockAdminOrderStatusRequest>
{
    public AdminOrderStatusValidator()
    {
        RuleFor(a => a.OrderStatus)
            .NotEmpty();

        RuleFor(a => a.OrderStatus.OrderNumber)
            .NotEmpty();

        RuleFor(a => a.OrderStatus.OrderType)
            .NotEmpty();

        RuleFor(a => a.OrderStatus.VehicleStockNo)
            .NotEmpty();

        RuleFor(a => a.OrderStatus.OrderCategory)
            .NotEmpty();

        RuleFor(a => a.OrderStatus.Status)
            .NotEmpty();
    }
}