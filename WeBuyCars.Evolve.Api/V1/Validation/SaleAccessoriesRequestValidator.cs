using FluentValidation;
using WeBuyCars.Evolve.Api.V1.Models;

namespace WeBuyCars.Evolve.Api.V1.Validation;

public sealed class SaleAccessoriesRequestValidator : AbstractValidator<SaleAccessoriesRequest>
{
    #region Constructors

    public SaleAccessoriesRequestValidator()
    {
        RuleFor(s => s.Location)
            .NotEmpty();
        RuleFor(s => s.Price)
            .NotEmpty();
        RuleFor(s => s.WarehouseCode)
            .NotEmpty();
    }

    #endregion
}