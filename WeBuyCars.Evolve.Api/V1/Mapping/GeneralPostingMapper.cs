using System;
using AutoMapper;
using Evolve.Contracts;
using WeBuyCars.Evolve.Api.V1.Models;
using WeBuyCars.Evolve.Infrastructure.Evolve.Entities.Requests;

namespace WeBuyCars.Evolve.Api.V1.Mapping;

public sealed class GeneralPostingMapper : Profile
{
    #region Constructors

    public GeneralPostingMapper()
    {
        CreateMap<PostSundryRequest, GeneralMaintenanceRowRequest>()
            .ForMember(dest => dest.RowDetails, opt => opt.MapFrom(src => new GeneralMaintenanceRequest
            {
                RowId = 1,
                GeneralPosting = new GeneralPostingRequest
                {
                    AccountsPayablePosting = new AccountsPayablePostingRequest
                    {
                        AccountNumber = src.AccountNumber,
                        Amount = src.Amount,
                        BatchDescription = src.TransactionTypeDescription,
                        Reference = $"{src.StockNumber}_{src.SundryCode}",
                        SundryCode = src.SundryCode,
                        AutoPost = "TRUE"
                    }
                }
            }));
        
        CreateMap<PostVehicleRequest, GeneralMaintenanceRowRequest>()
            .ForMember(dest => dest.RowDetails, opt => opt.MapFrom(src => new GeneralMaintenanceRequest
            {
                RowId = 1,
                GeneralPosting = new GeneralPostingRequest
                {
                    VehiclePosting = new VehiclePostingRequest()
                    {
                        Amount = src.Amount,
                        BatchDescription = $"Cost Adjustment: {src.StockNumber}",
                        Reference = $"{src.StockNumber}_{DateTime.Now:yyyyMMddHHmm}",
                        VehStockNo = src.StockNumber
                    }
                }
            }));
        
        // Post Vehicle Request
        CreateMap<PostVehicleRequest, PostVehicleRequestMessage>();
        CreateMap<PostVehicleRequestMessage, PostVehicleRequest>();
        
        // Post Sundry Request
        CreateMap<PostSundryRequest, PostSundryRequestMessage>();
        CreateMap<PostSundryRequestMessage, PostSundryRequest>();
    }

    #endregion
}