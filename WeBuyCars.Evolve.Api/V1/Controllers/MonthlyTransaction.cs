using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using WeBuyCars.Evolve.Api.V1.Interfaces;
using WeBuyCars.Evolve.Api.V1.Models.WBC;
using WeBuyCars.Evolve.Core.Enumerations;

namespace WeBuyCars.Evolve.Api.V1.Controllers
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("v{version:apiVersion}/[controller]")]
    [Produces("application/json")]
    [Authorize(Policy = "CanRead")]
    public class MonthlyTransaction : ControllerBase
    {
        private readonly IMonthlyTransactionService _monthlyTransactionService;

        public MonthlyTransaction(IMonthlyTransactionService monthlyTransactionService)
        {
            _monthlyTransactionService = monthlyTransactionService ?? throw new ArgumentNullException(nameof(monthlyTransactionService));
        }

        [HttpGet("ReturnMonthlyBoughtTransactionsAsync")]
        public async Task<ActionResult<List<SeriesViewModel<long, string>>>> ReturnMonthlyBoughtTransactionsAsync()
        {
            return await _monthlyTransactionService.ReturnTransactionsAsync(TransactionEnum.Bought);
        }

        [HttpGet("ReturnMonthlySoldTransactionsAsync")]
        public async Task<ActionResult<List<SeriesViewModel<long, string>>>> ReturnMonthlySoldTransactionsAsync()
        {
            return await _monthlyTransactionService.ReturnTransactionsAsync(TransactionEnum.Sold);
        }
    }
}