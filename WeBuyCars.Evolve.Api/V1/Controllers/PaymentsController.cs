using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Asp.Versioning;
using Evolve.Contracts;
using WeBuyCars.Evolve.Api.V1.Models;
using WeBuyCars.Evolve.Api.V1.Services;
using WeBuyCars.Evolve.Core.Constant;
using WeBuyCars.Evolve.Infrastructure.Messaging.Services.Interfaces;

namespace WeBuyCars.Evolve.Api.V1.Controllers;

[ApiController]
[ApiVersion("1.0")]
[Route("v{version:apiVersion}/[controller]")]
[Produces("application/json")]
[Authorize]
public class PaymentsController : ControllerBase
{
    #region Fields

    private readonly PaymentService _paymentService;
    private readonly IMessageService _messageService;
    private readonly ILogger<PaymentsController> _logger;
    private readonly IMapper _mapper;

    #endregion

    #region Constructors

    public PaymentsController
    (
        PaymentService paymentService,
        IMessageService messageService,
        ILogger<PaymentsController> logger,
        IMapper mapper 
    )
    {
        _paymentService = paymentService ?? throw new ArgumentNullException(nameof(paymentService));
        _messageService = messageService ?? throw new ArgumentNullException(nameof(messageService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
    }

    #endregion

    /// <summary>
    /// Make a deposit payment entry.
    /// </summary>
    /// <remarks>
    /// Sample request:
    ///
    ///     POST /api/v1/payments/deposit
    ///     {
    ///         "accountNumber": "123456",
    ///         "amount": "5000"
    ///     }
    ///
    /// </remarks>
    /// <param name="request">The deposit payment request.</param>
    [HttpPost("deposit")]
    [ProducesDefaultResponseType]
    [ProducesResponseType(StatusCodes.Status202Accepted)]
    [Authorize(Policy = "CanPost")]
    public async Task<ActionResult> MakeDepositPayment([FromBody] DepositPaymentRequest request)
    {
        _logger.LogInformation("{Request}",request.ToString());

        var message = _mapper.Map<DepositPaymentRequestMessage>(request);
        await _messageService.SendMessageToQueue(message, MassTransitQueues.EvolveDepositPayments, CancellationToken.None);
        
        return Accepted();
    }

    /// <summary>
    /// Make a settlement payment entry.
    /// </summary>
    /// <remarks>
    /// Sample request:
    ///
    ///     POST /api/v1/payments/settlement
    ///     {
    ///         "accountNumber": "123456",
    ///         "amount": "5000",
    ///         "transactionReference": "WBC16677"
    ///         "stockNumber": [
    ///             "41FDE0491",
    ///             "41FDE0491"
    ///         ]
    ///     }
    ///
    /// </remarks>
    /// <param name="request">The settlement payment request.</param>
    [HttpPost("settlement")]
    [ProducesDefaultResponseType]
    [ProducesResponseType(StatusCodes.Status202Accepted)]
    [Authorize(Policy = "CanMakeSettlementPayment")]
    public async Task<ActionResult> MakeSettlementPayment([FromBody] SettlementPaymentRequest request)
    {
        var message = _mapper.Map<SettlementPaymentRequestMessage>(request);
        await _messageService.SendMessageToQueue(message, MassTransitQueues.EvolveSettlementPayments,
            CancellationToken.None);
        
        return Accepted();
    }

    /// <summary>
    /// Make a refund payment entry.
    /// </summary>
    /// <remarks>
    /// Sample request:
    ///
    ///     POST /api/v1/payments/refund
    ///     {
    ///         "accountNumber": "123456",
    ///         "amount": "5000"
    ///     }
    ///
    /// </remarks>
    /// <param name="request">The refund payment request.</param>
    /// <param name="cancellationToken">Cancellation token for the request.</param>
    [HttpPost("refund")]
    [ProducesDefaultResponseType]
    [ProducesResponseType(StatusCodes.Status202Accepted)]
    [Authorize(Policy = "CanPost")]
    public async Task<ActionResult> MakeRefundPayment([FromBody] RefundPaymentRequest request, 
        CancellationToken cancellationToken)
    {
        var message = _mapper.Map<RefundPaymentRequestMessage>(request);
        await _messageService.SendMessageToQueue(message, MassTransitQueues.EvolveRefundPayments, cancellationToken);
            
        return Accepted();
    }
        
    /// <summary>
    /// Revert a refund payment entry.
    /// </summary>
    /// <remarks>
    /// Sample request:
    ///
    ///     POST /api/v1/payments/revertRefund
    ///     {
    ///         "accountNumber": "123456",
    ///         "amount": "5000"
    ///     }
    ///
    /// </remarks>
    /// <param name="request">The revert refund payment request.</param>
    /// <param name="cancellationToken">Cancellation token for the request.</param>
    [HttpPost("revertRefund")]
    [ProducesDefaultResponseType]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [Authorize(Policy = "CanPost")]
    public async Task<ActionResult> RevertRefundPayment([FromBody] RevertRefundPaymentRequest request, 
        CancellationToken cancellationToken)
    {
        await _paymentService.RevertRefundPaymentAsync(request.AccountNumber, request.Amount,
            request.TransactionReference, request.PaymentProvider, cancellationToken);

        return Created("", null);
    }
}