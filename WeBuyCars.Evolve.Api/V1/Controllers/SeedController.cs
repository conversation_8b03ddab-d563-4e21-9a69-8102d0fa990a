using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using WeBuyCars.Evolve.Api.V1.Models;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using WeBuyCars.Evolve.Api.V1.Services;
using WeBuyCars.Evolve.Api.V1.Services.BackgroundService;
using WeBuyCars.Evolve.Api.V1.Services.Elastic.Interfaces;
using WeBuyCars.Evolve.Core.SharedKernel;

namespace WeBuyCars.Evolve.Api.V1.Controllers
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("v{version:apiVersion}/[controller]")]
    [Produces("application/json")]
    [Authorize]
    public class SeedController : ControllerBase
    {
        #region Fields

        private readonly SalesService _saleService;
        private readonly IDailySalesService _salesService;
        private readonly IMonthlySalesService _monthlySalesService;
        private readonly IStockSummaryService _stockSummaryService;
        private readonly BackgroundSyncEvolveService _backgroundSyncEvolveService;

        #endregion

        #region Constructors

        public SeedController
        (
            SalesService saleService,
            IDailySalesService salesService,
            IStockSummaryService stockSummaryService,
            IMonthlySalesService monthlySalesService,
            BackgroundSyncEvolveService backgroundSyncEvolveService
        )
        {
            _saleService = saleService ?? throw new ArgumentNullException(nameof(saleService));
            _salesService = salesService ?? throw new ArgumentNullException(nameof(salesService));
            _stockSummaryService = stockSummaryService ?? throw new ArgumentNullException(nameof(stockSummaryService));
            _monthlySalesService = monthlySalesService ?? throw new ArgumentNullException(nameof(monthlySalesService));
            _backgroundSyncEvolveService = backgroundSyncEvolveService ?? throw new ArgumentNullException(nameof(backgroundSyncEvolveService));
        }

        #endregion

        /// <summary>
        /// Seeds Elasticsearch with daily sales summary data for each day within the provided date range.
        /// NOTE: This will overwrite any existing data.
        /// </summary>
        /// <param name="from">The start date.</param>
        /// <param name="to">The end date.</param>
        /// <response code="202">The request was accepted.</response>
        /// <response code="422">Request validation exception.</response>
        /// <response code="500">A server error occurred.</response>
        [HttpPost("sales/summaries/daily/{from:DateTime}/{to:DateTime}")]
        [ProducesResponseType(StatusCodes.Status202Accepted)]
        [ProducesResponseType(StatusCodes.Status422UnprocessableEntity)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [Authorize(Policy = "CanSync")]
        public async Task<ActionResult> SeedDailySalesSummaries(DateTime from, DateTime to)
        {
            for (var dt = from; dt <= to; dt = dt.AddDays(1))
            {
                var seedData = await _saleService.GetSalesSummaryAsync(dt, dt);
                seedData.Date = dt;
                await _salesService.InsertAsync(new DailySalesSummary
                {
                    Date = dt,
                    Count = seedData.Count,
                    Value = seedData.Value,
                    LastUpdated = DateTimeOffset.UtcNow,
                    LocationSalesSummaries = seedData.LocationSalesSummaries,
                    GrossProfit = seedData.GrossProfit
                });
            }

            return Accepted();
        }

        /// <summary>
        /// Seeds Elasticsearch with monthly sales summary data for each day within the provided date range.
        /// NOTE: This will overwrite any existing data.
        /// </summary>
        /// <param name="from">The start date.</param>
        /// <param name="to">The end date.</param>
        /// <response code="202">The request was accepted.</response>
        /// <response code="422">Request validation exception.</response>
        /// <response code="500">A server error occurred.</response>
        [HttpPost("sales/summaries/monthly/{from:DateTime}/{to:DateTime}")]
        [ProducesResponseType(StatusCodes.Status202Accepted)]
        [ProducesResponseType(StatusCodes.Status422UnprocessableEntity)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [Authorize(Policy = "CanSync")]
        public async Task<ActionResult> SeedMonthlySalesSummaries(DateTime from, DateTime to)
        {
            // Set the start date to the first day of the month.
            var startDate = new DateTime(from.Year, from.Month, 1);

            // Set the end date to the last day of the month.
            var daysInMonth = DateTime.DaysInMonth(to.Year, to.Month);
            var endDate = new DateTime(to.Year, to.Month, daysInMonth);

            var months = DateTimeHelpers.MonthsBetween(startDate, endDate);

            foreach (var month in months)
            {
                var seedData = await _saleService.GetSalesSummaryAsync(month.Item1, month.Item2);
                seedData.Date = month.Item1;
                await _monthlySalesService.InsertAsync(new MonthlySalesSummary 
                {
                    Date = month.Item1,
                    Count = seedData.Count,
                    Value = seedData.Value,
                    LastUpdated = DateTimeOffset.UtcNow,
                    LocationSalesSummaries = seedData.LocationSalesSummaries,
                    GrossProfit = seedData.GrossProfit
                });

            }

            return Accepted();
        }

        [HttpPost("stock/summaries/{dateTime:DateTime}")]
        [ProducesResponseType(StatusCodes.Status202Accepted)]
        [ProducesResponseType(StatusCodes.Status422UnprocessableEntity)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [Authorize(Policy = "CanSync")]
        public async Task<ActionResult> SeedVehicleSummary(DateTime dateTime)
        {
            var seedData = await _backgroundSyncEvolveService.GetVehicleStockSummary(dateTime, dateTime);
            seedData.Date = dateTime;
            await _stockSummaryService.InsertAsync(seedData);

            return Accepted();
        }

        [HttpPost("stock/summaries/{from:DateTime}/{to:DateTime}")]
        [ProducesResponseType(StatusCodes.Status202Accepted)]
        [ProducesResponseType(StatusCodes.Status422UnprocessableEntity)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [Authorize(Policy = "CanSync")]
        public async Task<ActionResult> SeedVehicleSummaries(DateTime from, DateTime to)
        {
            for (var dt = from; dt <= to; dt = dt.AddDays(1))
            {
                var seedData = await _backgroundSyncEvolveService.GetVehicleStockSummary(dt, dt);
                seedData.Date = dt;
                await _stockSummaryService.InsertAsync(seedData);
            }

            return Accepted();
        }
    }
}