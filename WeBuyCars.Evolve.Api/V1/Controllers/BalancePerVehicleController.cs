using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using WeBuyCars.Evolve.Api.V1.Services.Elastic.Interfaces;
using WeBuyCars.Evolve.Infrastructure.Evolve.Entities.Responses;

namespace WeBuyCars.Evolve.Api.V1.Controllers;

[ApiController]
[ApiVersion("1.0")]
[Route("v{version:apiVersion}/[controller]")]
[Produces("application/json")]
[Authorize]
public class BalancePerVehicleController : ControllerBase
{
    #region Fields

    private readonly IBalancePerVehicleElasticService _balancePerVehicleElasticService;

    #endregion

    #region Constructors

    public BalancePerVehicleController(IBalancePerVehicleElasticService balancePerVehicleElasticService)
    {
        _balancePerVehicleElasticService = balancePerVehicleElasticService ?? throw new ArgumentNullException(nameof(balancePerVehicleElasticService));
    }

    #endregion

    /// <summary>
    /// Fetch the outstanding balances per vehicle by stock number
    /// </summary>
    /// <returns></returns>
    [HttpPost("today")]
    [ProducesResponseType(typeof(IEnumerable<ArBalancePerVehicleResponse>), StatusCodes.Status200OK)]
    [Authorize(Policy = "CanRead")]
    public async Task<IEnumerable<ArBalancePerVehicleResponse>> GetCurrentPaged([FromBody] List<string> stockNumbers)
    {
        return await _balancePerVehicleElasticService.GetCurrentByStockNumbers(stockNumbers);
    }
}