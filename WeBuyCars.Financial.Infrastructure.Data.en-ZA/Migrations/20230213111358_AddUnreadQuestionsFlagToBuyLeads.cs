using Microsoft.EntityFrameworkCore.Migrations;
using WeBuyCars.Financial.Infrastructure.Data.en_ZA.Extensions.MigrationBuilderExtensions;

#nullable disable

namespace WeBuyCars.Financial.Infrastructure.Data.Migrations
{
    public partial class AddUnreadQuestionsFlagToBuyLeads : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "UnreadQuestions",
                schema: "FIN",
                table: "BuyLead",
                type: "bit",
                nullable: false,
                defaultValue: false);
            
            migrationBuilder.AlterOutstandingPaymentGridView_AddUnreadQuestionsFlag();
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Drop_AlterOutstandingPaymentGridView_AddUnreadQuestionsFlag();
            
            migrationBuilder.DropColumn(
                name: "UnreadQuestions",
                schema: "FIN",
                table: "BuyLead");
        }
    }
}
