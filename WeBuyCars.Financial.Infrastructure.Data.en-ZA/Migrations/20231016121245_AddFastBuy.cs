using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WeBuyCars.Financial.Infrastructure.Data.Migrations
{
    public partial class AddFastBuy : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "FastBuy",
                schema: "FIN",
                table: "FinanceHouse",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.InsertData(
                schema: "FIN",
                table: "FinanceHouse",
                columns: new[] { "Id", "BankAccountNumber", "BankBranchCode", "BankReference", "BeneficiaryCode", "CreatedBy", "CreatedOn", "FastBuy", "ModifiedBy", "ModifiedOn", "Name" },
                values: new object[] { 27L, "***********", "632005", "WE BUY CARS", "ABSA_FB", "System", new DateTimeOffset(new DateTime(2020, 10, 20, 10, 7, 45, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)), true, null, null, "ABSA FAST BUY ONLY" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                schema: "FIN",
                table: "FinanceHouse",
                keyColumn: "Id",
                keyValue: 27L);

            migrationBuilder.DropColumn(
                name: "FastBuy",
                schema: "FIN",
                table: "FinanceHouse");
        }
    }
}
