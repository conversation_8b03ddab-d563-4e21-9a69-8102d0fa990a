// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using WeBuyCars.Financial.Infrastructure.Data.Context;

namespace WeBuyCars.Financial.Infrastructure.Data.Migrations
{
    [DbContext(typeof(FinancialContext))]
    [Migration("20201021083825_TransactionReferencesDbMigration")]
    partial class TransactionReferencesDbMigration
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("FIN")
                .HasAnnotation("ProductVersion", "3.1.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 128)
                .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.Account", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("AccountNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(20)")
                        .HasMaxLength(20);

                    b.Property<int>("AccountTypeId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.HasIndex("AccountNumber")
                        .IsUnique();

                    b.HasIndex("AccountTypeId");

                    b.ToTable("Account");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.AccountType", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(20)")
                        .HasMaxLength(20);

                    b.HasKey("Id");

                    b.ToTable("AccountType");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Name = "AccountsReceivable"
                        },
                        new
                        {
                            Id = 2,
                            Name = "AccountsPayable"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.Audit", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset>("AuditDateTimeUtc")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("KeyValues")
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<string>("NewValues")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OldValues")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TableName")
                        .IsRequired()
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<string>("User")
                        .IsRequired()
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.HasKey("Id");

                    b.ToTable("Audit");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.Bank", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(20)")
                        .HasMaxLength(20);

                    b.HasKey("Id");

                    b.ToTable("Bank");

                    b.HasData(
                        new
                        {
                            Id = 0,
                            Name = "Other"
                        },
                        new
                        {
                            Id = 1,
                            Name = "Fnb"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BankAccount", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("AccountNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(13)")
                        .HasMaxLength(13);

                    b.Property<long>("BankAccountHolderId")
                        .HasColumnType("bigint");

                    b.Property<int>("BankAccountTypeId")
                        .HasColumnType("int");

                    b.Property<string>("BranchCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(6)")
                        .HasMaxLength(6);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.HasIndex("BankAccountHolderId");

                    b.HasIndex("BankAccountTypeId");

                    b.ToTable("BankAccount");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BankAccountHolder", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int>("BankAccountHolderTypeId")
                        .HasColumnType("int");

                    b.Property<string>("CompanyName")
                        .HasColumnType("nvarchar(60)")
                        .HasMaxLength(60);

                    b.Property<string>("CompanyRegistrationNumber")
                        .HasColumnType("nvarchar(13)")
                        .HasMaxLength(13);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("EmailAddress")
                        .HasColumnType("nvarchar(100)")
                        .HasMaxLength(100);

                    b.Property<string>("EstateName")
                        .HasColumnType("nvarchar(60)")
                        .HasMaxLength(60);

                    b.Property<string>("EstateNumber")
                        .HasColumnType("nvarchar(13)")
                        .HasMaxLength(13);

                    b.Property<string>("IdNumber")
                        .HasColumnType("nvarchar(13)")
                        .HasMaxLength(13);

                    b.Property<string>("Initials")
                        .HasColumnType("nvarchar(5)")
                        .HasMaxLength(5);

                    b.Property<string>("LastName")
                        .HasColumnType("nvarchar(60)")
                        .HasMaxLength(60);

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("PassportNumber")
                        .HasColumnType("nvarchar(13)")
                        .HasMaxLength(13);

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(15)")
                        .HasMaxLength(15);

                    b.HasKey("Id");

                    b.HasIndex("BankAccountHolderTypeId");

                    b.ToTable("BankAccountHolder");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BankAccountHolderType", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasMaxLength(50);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasMaxLength(50);

                    b.HasKey("Id");

                    b.ToTable("BankAccountHolderType");

                    b.HasData(
                        new
                        {
                            Id = 0,
                            Description = "Other",
                            Name = "Other"
                        },
                        new
                        {
                            Id = 1,
                            Description = "South African Individual",
                            Name = "SouthAfricanIndividual"
                        },
                        new
                        {
                            Id = 2,
                            Description = "Foreign Individual",
                            Name = "ForeignIndividual"
                        },
                        new
                        {
                            Id = 3,
                            Description = "Company",
                            Name = "Company"
                        },
                        new
                        {
                            Id = 4,
                            Description = "Estate",
                            Name = "Estate"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BankAccountType", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("nvarchar(2)")
                        .HasMaxLength(2);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(20)")
                        .HasMaxLength(20);

                    b.HasKey("Id");

                    b.ToTable("BankAccountType");

                    b.HasData(
                        new
                        {
                            Id = 0,
                            Code = "00",
                            Name = "Unknown"
                        },
                        new
                        {
                            Id = 1,
                            Code = "01",
                            Name = "CurrentCheque"
                        },
                        new
                        {
                            Id = 2,
                            Code = "02",
                            Name = "Savings"
                        },
                        new
                        {
                            Id = 3,
                            Code = "03",
                            Name = "Transmission"
                        },
                        new
                        {
                            Id = 4,
                            Code = "04",
                            Name = "Bond"
                        },
                        new
                        {
                            Id = 6,
                            Code = "06",
                            Name = "Subscription"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BankAccountVerification", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int?>("AccountAcceptsCredits")
                        .HasColumnType("int");

                    b.Property<int?>("AccountAcceptsDebits")
                        .HasColumnType("int");

                    b.Property<int?>("AccountCompanyRegistrationNumberMatch")
                        .HasColumnType("int");

                    b.Property<int?>("AccountEstateNumberMatch")
                        .HasColumnType("int");

                    b.Property<int?>("AccountExists")
                        .HasColumnType("int");

                    b.Property<int?>("AccountIdNumberMatch")
                        .HasColumnType("int");

                    b.Property<int?>("AccountOpen")
                        .HasColumnType("int");

                    b.Property<int?>("AccountOpenGtThreeMonths")
                        .HasColumnType("int");

                    b.Property<int?>("AccountPassportNumberMatch")
                        .HasColumnType("int");

                    b.Property<int?>("AccountTypeValid")
                        .HasColumnType("int");

                    b.Property<long>("BankAccountId")
                        .HasColumnType("bigint");

                    b.Property<string>("ClientReference")
                        .IsRequired()
                        .HasColumnType("nvarchar(20)")
                        .HasMaxLength(20);

                    b.Property<int?>("CompanyNameMatch")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<int?>("EmailValid")
                        .HasColumnType("int");

                    b.Property<int?>("EstateNameMatch")
                        .HasColumnType("int");

                    b.Property<int?>("InitialMatch")
                        .HasColumnType("int");

                    b.Property<int?>("LastNameMatch")
                        .HasColumnType("int");

                    b.Property<string>("MessageCode")
                        .HasColumnType("nvarchar(10)")
                        .HasMaxLength(10);

                    b.Property<string>("MessageDescription")
                        .HasColumnType("nvarchar(100)")
                        .HasMaxLength(100);

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Operator")
                        .IsRequired()
                        .HasColumnType("nvarchar(10)")
                        .HasMaxLength(10);

                    b.Property<int?>("PhoneValid")
                        .HasColumnType("int");

                    b.Property<int>("ProviderId")
                        .HasColumnType("int");

                    b.Property<DateTimeOffset>("RequestDateTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("TransactionReference")
                        .HasColumnType("nvarchar(30)")
                        .HasMaxLength(30);

                    b.Property<string>("UserReference")
                        .IsRequired()
                        .HasColumnType("nvarchar(20)")
                        .HasMaxLength(20);

                    b.HasKey("Id");

                    b.HasIndex("BankAccountId");

                    b.HasIndex("ClientReference")
                        .IsUnique();

                    b.HasIndex("ProviderId");

                    b.HasIndex("UserReference")
                        .IsUnique();

                    b.ToTable("BankAccountVerification");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BankAccountVerificationMatchType", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("nvarchar(2)")
                        .HasMaxLength(2);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(20)")
                        .HasMaxLength(20);

                    b.HasKey("Id");

                    b.ToTable("BankAccountVerificationMatchType");

                    b.HasData(
                        new
                        {
                            Id = 0,
                            Code = "00",
                            Name = "PositiveMatch"
                        },
                        new
                        {
                            Id = 1,
                            Code = "01",
                            Name = "NegativeMatch"
                        },
                        new
                        {
                            Id = 99,
                            Code = "99",
                            Name = "UnableToVerify"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.Branch", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(20)")
                        .HasMaxLength(20);

                    b.HasKey("Id");

                    b.ToTable("Branch");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Name = "Unknown"
                        },
                        new
                        {
                            Id = 2,
                            Name = "Midstream"
                        },
                        new
                        {
                            Id = 3,
                            Name = "SilverLakes"
                        },
                        new
                        {
                            Id = 4,
                            Name = "Durban"
                        },
                        new
                        {
                            Id = 5,
                            Name = "JoburgSouth"
                        },
                        new
                        {
                            Id = 6,
                            Name = "CapeTown"
                        },
                        new
                        {
                            Id = 7,
                            Name = "PortElizabeth"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.Consent", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<bool>("Agreed")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(1024)")
                        .HasMaxLength(1024);

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<long?>("TransactionId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TransactionId");

                    b.ToTable("Consent");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.FinanceHouse", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("BankAccountNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(13)")
                        .HasMaxLength(13);

                    b.Property<string>("BankBranchCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(6)")
                        .HasMaxLength(6);

                    b.Property<string>("BankReference")
                        .IsRequired()
                        .HasColumnType("nvarchar(13)")
                        .HasMaxLength(13);

                    b.Property<string>("BeneficiaryCode")
                        .HasColumnType("nvarchar(10)")
                        .HasMaxLength(10);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(30)")
                        .HasMaxLength(30);

                    b.HasKey("Id");

                    b.ToTable("FinanceHouse");

                    b.HasData(
                        new
                        {
                            Id = 1L,
                            BankAccountNumber = "**********",
                            BankBranchCode = "632005",
                            BankReference = "WE BUY CARS",
                            BeneficiaryCode = "ABSA LEGAL",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2020, 10, 20, 10, 7, 45, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "ABSA LEGAL VEHICLE & ASSET FIN"
                        },
                        new
                        {
                            Id = 2L,
                            BankAccountNumber = "**********",
                            BankBranchCode = "632005",
                            BankReference = "WE BUY CARS",
                            BeneficiaryCode = "AVAF001",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2020, 10, 20, 10, 7, 45, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "ABSA VEHICLE AND ASSET FINANCE"
                        },
                        new
                        {
                            Id = 3L,
                            BankAccountNumber = "***********",
                            BankBranchCode = "800000",
                            BankReference = "WE BUY CARS",
                            BeneficiaryCode = "ALBA",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2020, 10, 20, 10, 7, 45, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "ALBARAKA"
                        },
                        new
                        {
                            Id = 4L,
                            BankAccountNumber = "***********",
                            BankBranchCode = "255005",
                            BankReference = "WE BUY CARS",
                            BeneficiaryCode = "AVIS",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2020, 10, 20, 10, 7, 45, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "AVIS FLEET SERVICES"
                        },
                        new
                        {
                            Id = 5L,
                            BankAccountNumber = "**********",
                            BankBranchCode = "632005",
                            BankReference = "WE BUY CARS",
                            BeneficiaryCode = "BMW001",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2020, 10, 20, 10, 7, 45, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "BMW FINANCIAL SERVICES"
                        },
                        new
                        {
                            Id = 6L,
                            BankAccountNumber = "*********",
                            BankBranchCode = "25309",
                            BankReference = "WE BUY CARS",
                            BeneficiaryCode = "CAPEFIN",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2020, 10, 20, 10, 7, 45, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "CAPE FINANCE CORPORATION PTY L"
                        },
                        new
                        {
                            Id = 7L,
                            BankAccountNumber = "***********",
                            BankBranchCode = "251445",
                            BankReference = "WE BUY CARS",
                            BeneficiaryCode = "IEMAS001",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2020, 10, 20, 10, 7, 45, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "IEMAS FINANCIAL SERVICES"
                        },
                        new
                        {
                            Id = 8L,
                            BankAccountNumber = "**********",
                            BankBranchCode = "632005",
                            BankReference = "WE BUYCARS",
                            BeneficiaryCode = "INV0017",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2020, 10, 20, 10, 7, 45, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "INVESTEC BANK LIMITED"
                        },
                        new
                        {
                            Id = 9L,
                            BankAccountNumber = "**********",
                            BankBranchCode = "632005",
                            BankReference = "WE BUYCARS",
                            BeneficiaryCode = "INV0305",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2020, 10, 20, 10, 7, 45, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "INVESTEC BANK LIMITED"
                        },
                        new
                        {
                            Id = 10L,
                            BankAccountNumber = "**********",
                            BankBranchCode = "632005",
                            BankReference = "WE BUY CARS",
                            BeneficiaryCode = "INV0307",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2020, 10, 20, 10, 7, 45, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "INVESTEC BANK LIMITED"
                        },
                        new
                        {
                            Id = 11L,
                            BankAccountNumber = "**********",
                            BankBranchCode = "632005",
                            BankReference = "WE BUYCARS",
                            BeneficiaryCode = "INV1238",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2020, 10, 20, 10, 7, 45, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "INVESTEC BANK LIMITED"
                        },
                        new
                        {
                            Id = 12L,
                            BankAccountNumber = "**********",
                            BankBranchCode = "632005",
                            BankReference = "WE BUYCARS",
                            BeneficiaryCode = "",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2020, 10, 20, 10, 7, 45, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "INVESTEC BANK LIMITED"
                        },
                        new
                        {
                            Id = 13L,
                            BankAccountNumber = "*********",
                            BankBranchCode = "632005",
                            BankReference = "WE BUY CARS",
                            BeneficiaryCode = "KK001",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2020, 10, 20, 10, 7, 45, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "KOOPKRAG BEPERK"
                        },
                        new
                        {
                            Id = 14L,
                            BankAccountNumber = "***********",
                            BankBranchCode = "250655",
                            BankReference = "WE BUY CARS",
                            BeneficiaryCode = "MERC001",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2020, 10, 20, 10, 7, 45, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "MERCEDES-BENZ FINANCIAL SERVIC"
                        },
                        new
                        {
                            Id = 15L,
                            BankAccountNumber = "9704728",
                            BankBranchCode = "51001",
                            BankReference = "WE BUYCARS",
                            BeneficiaryCode = "STD002",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2020, 10, 20, 10, 7, 45, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "STANDARD BANK VEHICLE & FINANC"
                        },
                        new
                        {
                            Id = 16L,
                            BankAccountNumber = "***********",
                            BankBranchCode = "201809",
                            BankReference = "WE BUY CARS",
                            BeneficiaryCode = "SUNFIN",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2020, 10, 20, 10, 7, 45, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "SUN FINANCE SOUTH AFRICA"
                        },
                        new
                        {
                            Id = 17L,
                            BankAccountNumber = "**********",
                            BankBranchCode = "198765",
                            BankReference = "WE BUY CARS",
                            BeneficiaryCode = "MFC002",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2020, 10, 20, 10, 7, 45, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "THE MOTOR FINANCE CORPORATION"
                        },
                        new
                        {
                            Id = 18L,
                            BankAccountNumber = "***********",
                            BankBranchCode = "255005",
                            BankReference = "WE BUY CARS",
                            BeneficiaryCode = "TOYFIN001",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2020, 10, 20, 10, 7, 45, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "TOYOTA FINANCIAL SERVICES"
                        },
                        new
                        {
                            Id = 19L,
                            BankAccountNumber = "***********",
                            BankBranchCode = "255005",
                            BankReference = "WE BUY CARS",
                            BeneficiaryCode = "VWFIN001",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2020, 10, 20, 10, 7, 45, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "VOLKSWAGEN FINANCIAL SERVICES"
                        },
                        new
                        {
                            Id = 20L,
                            BankAccountNumber = "***********",
                            BankBranchCode = "255005",
                            BankReference = "WE BUY CARS",
                            BeneficiaryCode = "WESLEG001",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2020, 10, 20, 10, 7, 45, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "WESBANK LEGAL"
                        },
                        new
                        {
                            Id = 21L,
                            BankAccountNumber = "***********",
                            BankBranchCode = "255005",
                            BankReference = "WE BUY CARS",
                            BeneficiaryCode = "WES002",
                            CreatedBy = "System",
                            CreatedOn = new DateTimeOffset(new DateTime(2020, 10, 20, 10, 7, 45, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "WESBANK VEHICLE AND ASSET FINA"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.HyphenErrorCode", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("nvarchar(5)")
                        .HasMaxLength(5);

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(300)")
                        .HasMaxLength(300);

                    b.HasKey("Id");

                    b.ToTable("HyphenErrorCode");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Code = "H0",
                            Description = "Profile used for login is unknown (See note)"
                        },
                        new
                        {
                            Id = 2,
                            Code = "H4",
                            Description = "User checksum is invalid i.e. invalid password used"
                        },
                        new
                        {
                            Id = 3,
                            Code = "H5",
                            Description = "User checksum is valid but profile is disabled"
                        },
                        new
                        {
                            Id = 4,
                            Code = "H6",
                            Description = "Problem communicating with FACS server."
                        },
                        new
                        {
                            Id = 5,
                            Code = "H8",
                            Description = "User does not have the authority to use the requested method"
                        },
                        new
                        {
                            Id = 6,
                            Code = "WS01",
                            Description = "Invalid data checksum"
                        },
                        new
                        {
                            Id = 7,
                            Code = "WS02",
                            Description = "Profile missing FACS code"
                        },
                        new
                        {
                            Id = 8,
                            Code = "WS03",
                            Description = "Invalid length for transaction type"
                        },
                        new
                        {
                            Id = 9,
                            Code = "WS04",
                            Description = "Invalid length for document type"
                        },
                        new
                        {
                            Id = 10,
                            Code = "WS05",
                            Description = "Reference 1 too long"
                        },
                        new
                        {
                            Id = 11,
                            Code = "WS06",
                            Description = "Reference 2 not specified"
                        },
                        new
                        {
                            Id = 12,
                            Code = "WS07",
                            Description = "Reference 2 too long"
                        },
                        new
                        {
                            Id = 13,
                            Code = "WS08",
                            Description = "Code 1 too long"
                        },
                        new
                        {
                            Id = 14,
                            Code = "WS09",
                            Description = "Code 2 too long"
                        },
                        new
                        {
                            Id = 15,
                            Code = "WS10",
                            Description = "Amount not numeric"
                        },
                        new
                        {
                            Id = 16,
                            Code = "WS11",
                            Description = "Amount not specified"
                        },
                        new
                        {
                            Id = 17,
                            Code = "WS12",
                            Description = "Amount too large"
                        },
                        new
                        {
                            Id = 18,
                            Code = "WS13",
                            Description = "Client name not specified"
                        },
                        new
                        {
                            Id = 19,
                            Code = "WS14",
                            Description = "Client name too long"
                        },
                        new
                        {
                            Id = 20,
                            Code = "WS15",
                            Description = "Processing option 1 contains an invalid value"
                        },
                        new
                        {
                            Id = 21,
                            Code = "WS16",
                            Description = "Processing option 2 contains an invalid value"
                        },
                        new
                        {
                            Id = 22,
                            Code = "WS17",
                            Description = "Client bank account number contains invalid characters"
                        },
                        new
                        {
                            Id = 23,
                            Code = "WS18",
                            Description = "Client bank account not specified"
                        },
                        new
                        {
                            Id = 24,
                            Code = "WS19",
                            Description = "Client bank account too long"
                        },
                        new
                        {
                            Id = 25,
                            Code = "WS20",
                            Description = "Branch code contains invalid characters"
                        },
                        new
                        {
                            Id = 26,
                            Code = "WS21",
                            Description = "Branch code not specified"
                        },
                        new
                        {
                            Id = 27,
                            Code = "WS22",
                            Description = "Branch code too long"
                        },
                        new
                        {
                            Id = 28,
                            Code = "WS23",
                            Description = "Invalid value for account type"
                        },
                        new
                        {
                            Id = 29,
                            Code = "WS24",
                            Description = "CDV error"
                        },
                        new
                        {
                            Id = 30,
                            Code = "WS25",
                            Description = "Bank account number contains invalid characters"
                        },
                        new
                        {
                            Id = 31,
                            Code = "WS26",
                            Description = "Bank account number not specified"
                        },
                        new
                        {
                            Id = 32,
                            Code = "WS27",
                            Description = "Bank account number too long"
                        },
                        new
                        {
                            Id = 33,
                            Code = "WS28",
                            Description = "Action date invalid"
                        },
                        new
                        {
                            Id = 34,
                            Code = "WS29",
                            Description = "Invalid FEDI value specified"
                        },
                        new
                        {
                            Id = 35,
                            Code = "0016",
                            Description = "The transaction type is blank or invalid"
                        },
                        new
                        {
                            Id = 36,
                            Code = "0020",
                            Description = "Document type is blank or invalid"
                        },
                        new
                        {
                            Id = 37,
                            Code = "0024",
                            Description = "Invalid transaction type"
                        },
                        new
                        {
                            Id = 38,
                            Code = "0080",
                            Description = "Amount is less than zero"
                        },
                        new
                        {
                            Id = 39,
                            Code = "1009",
                            Description = "Invalid bank account number"
                        },
                        new
                        {
                            Id = 40,
                            Code = "1014",
                            Description = "Bank account details not transmitted"
                        },
                        new
                        {
                            Id = 41,
                            Code = "1042",
                            Description = "Client branch code invalid for FEDI transaction"
                        },
                        new
                        {
                            Id = 42,
                            Code = "1047",
                            Description = "FEDI not available for this bank at present"
                        },
                        new
                        {
                            Id = 43,
                            Code = "1054",
                            Description = "Invalid branch code"
                        },
                        new
                        {
                            Id = 44,
                            Code = "1055",
                            Description = "Invalid bank acc number/invalid acc number acc type combination"
                        },
                        new
                        {
                            Id = 45,
                            Code = "1056",
                            Description = "Account type not valid for this branch"
                        },
                        new
                        {
                            Id = 46,
                            Code = "1057",
                            Description = "Invalid branch code or blank"
                        },
                        new
                        {
                            Id = 47,
                            Code = "1058",
                            Description = "FNB saving account debit order not allowed"
                        },
                        new
                        {
                            Id = 48,
                            Code = "1059",
                            Description = "Account number too short/long"
                        },
                        new
                        {
                            Id = 49,
                            Code = "1078",
                            Description = "Duplicate transaction sent to HYPHEN"
                        },
                        new
                        {
                            Id = 50,
                            Code = "1084",
                            Description = "Bond account type '4' must be loaded as '1'"
                        },
                        new
                        {
                            Id = 51,
                            Code = "2061",
                            Description = "Transaction can not be created on credit card account"
                        },
                        new
                        {
                            Id = 52,
                            Code = "2062",
                            Description = "Credit card account number in invalid"
                        },
                        new
                        {
                            Id = 53,
                            Code = "2080",
                            Description = "Invalid branch"
                        },
                        new
                        {
                            Id = 54,
                            Code = "2138",
                            Description = "Record inactive on master file (Nominated payments)"
                        },
                        new
                        {
                            Id = 55,
                            Code = "2147",
                            Description = "Value exceeded for NPS limit"
                        },
                        new
                        {
                            Id = 56,
                            Code = "2150",
                            Description = "Reference not loaded on master file (Nominated payments)"
                        },
                        new
                        {
                            Id = 57,
                            Code = "2201",
                            Description = "Amount greater than R5000 for Naedo transactions"
                        },
                        new
                        {
                            Id = 58,
                            Code = "2202",
                            Description = "Service Branch. EFT not allowed"
                        },
                        new
                        {
                            Id = 59,
                            Code = "2203",
                            Description = "Zero amount received in mail"
                        },
                        new
                        {
                            Id = 60,
                            Code = "7777",
                            Description = "Technical error on HYPHEN system. Please contact HYPHEN Help Desk"
                        },
                        new
                        {
                            Id = 61,
                            Code = "8888",
                            Description = "Redirect information. Update line of business with new account details"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.HyphenProcessingOption1", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(5)")
                        .HasMaxLength(5);

                    b.HasKey("Id");

                    b.ToTable("HyphenProcessingOption1");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Description = "Creation of single transaction (Itemised)",
                            Name = "I"
                        },
                        new
                        {
                            Id = 2,
                            Description = "Grouping a transaction to any transaction previously created on FACS with the same bank account number, action date and ‘G’ indicator",
                            Name = "G"
                        },
                        new
                        {
                            Id = 3,
                            Description = "Data only to be validated. Not to be created on FACS.",
                            Name = "V"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.HyphenProcessingOption2", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(5)")
                        .HasMaxLength(5);

                    b.HasKey("Id");

                    b.ToTable("HyphenProcessingOption2");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Description = "Create transaction on FACS",
                            Name = "S"
                        },
                        new
                        {
                            Id = 2,
                            Description = "Data only to be validated. Not to be created on FACS.",
                            Name = "V"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.HyphenTransactionDetail", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<DateTimeOffset>("ActionDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("AgencyNumber")
                        .HasColumnType("nvarchar(6)")
                        .HasMaxLength(6);

                    b.Property<string>("AgencyPrefix")
                        .HasColumnType("nvarchar(1)")
                        .HasMaxLength(1);

                    b.Property<double>("Amount")
                        .HasColumnType("float");

                    b.Property<string>("BankAccountNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(17)")
                        .HasMaxLength(17);

                    b.Property<int>("BankId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<string>("ChequeClearanceCode")
                        .HasColumnType("nvarchar(10)")
                        .HasMaxLength(10);

                    b.Property<string>("ChequeNumber")
                        .HasColumnType("nvarchar(9)")
                        .HasMaxLength(9);

                    b.Property<int>("ClientBankAccountHolderTypeId")
                        .HasColumnType("int");

                    b.Property<string>("ClientBankAccountNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(17)")
                        .HasMaxLength(17);

                    b.Property<int>("ClientBankAccountTypeId")
                        .HasColumnType("int");

                    b.Property<string>("ClientBranchCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(6)")
                        .HasMaxLength(6);

                    b.Property<string>("ClientName")
                        .IsRequired()
                        .HasColumnType("nvarchar(80)")
                        .HasMaxLength(80);

                    b.Property<string>("Code1")
                        .HasColumnType("nvarchar(10)")
                        .HasMaxLength(10);

                    b.Property<string>("Code2")
                        .HasColumnType("nvarchar(20)")
                        .HasMaxLength(20);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("DepositType")
                        .HasColumnType("nvarchar(2)")
                        .HasMaxLength(2);

                    b.Property<string>("DocumentNumber")
                        .HasColumnType("nvarchar(8)")
                        .HasMaxLength(8);

                    b.Property<string>("DocumentType")
                        .IsRequired()
                        .HasColumnType("nvarchar(2)")
                        .HasMaxLength(2);

                    b.Property<string>("ErrorCode")
                        .HasColumnType("nvarchar(4)")
                        .HasMaxLength(4);

                    b.Property<bool>("FediIndicator")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<int>("ProcessingOption1Id")
                        .HasColumnType("int");

                    b.Property<int>("ProcessingOption2Id")
                        .HasColumnType("int");

                    b.Property<string>("Reference1")
                        .HasColumnType("nvarchar(2)")
                        .HasMaxLength(2);

                    b.Property<string>("Reference2")
                        .IsRequired()
                        .HasColumnType("nvarchar(20)")
                        .HasMaxLength(20);

                    b.Property<string>("RequisitionNumber")
                        .HasColumnType("nvarchar(9)")
                        .HasMaxLength(9);

                    b.Property<long>("TransactionId")
                        .HasColumnType("bigint");

                    b.Property<Guid>("TransactionReference")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("TransactionTypeId")
                        .HasColumnType("int");

                    b.Property<string>("UniqueUserCode")
                        .HasColumnType("nvarchar(4)")
                        .HasMaxLength(4);

                    b.HasKey("Id");

                    b.HasIndex("BankId");

                    b.HasIndex("ClientBankAccountHolderTypeId");

                    b.HasIndex("ClientBankAccountTypeId");

                    b.HasIndex("ProcessingOption1Id");

                    b.HasIndex("ProcessingOption2Id");

                    b.HasIndex("TransactionId");

                    b.HasIndex("TransactionReference")
                        .IsUnique();

                    b.HasIndex("TransactionTypeId");

                    b.ToTable("HyphenTransactionDetail");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.HyphenTransactionType", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<int>("BankId")
                        .HasColumnType("int");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("nvarchar(5)")
                        .HasMaxLength(5);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(20)")
                        .HasMaxLength(20);

                    b.Property<int>("TransactionTypeId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("BankId");

                    b.HasIndex("TransactionTypeId");

                    b.ToTable("HyphenTransactionType");

                    b.HasData(
                        new
                        {
                            Id = 7,
                            BankId = 0,
                            Code = "SETPT",
                            Name = "Settlement",
                            TransactionTypeId = 7
                        },
                        new
                        {
                            Id = 8,
                            BankId = 0,
                            Code = "DEPPT",
                            Name = "Deposit",
                            TransactionTypeId = 8
                        },
                        new
                        {
                            Id = 9,
                            BankId = 0,
                            Code = "BALPT",
                            Name = "BalancePayment",
                            TransactionTypeId = 9
                        },
                        new
                        {
                            Id = 10,
                            BankId = 0,
                            Code = "SHOPT",
                            Name = "ShortfallPayment",
                            TransactionTypeId = 10
                        },
                        new
                        {
                            Id = 11,
                            BankId = 1,
                            Code = "FNBSP",
                            Name = "FnbSettlement",
                            TransactionTypeId = 7
                        },
                        new
                        {
                            Id = 12,
                            BankId = 1,
                            Code = "FNBDP",
                            Name = "FnbDeposit",
                            TransactionTypeId = 8
                        },
                        new
                        {
                            Id = 13,
                            BankId = 1,
                            Code = "FNBBP",
                            Name = "FnbBalancePayment",
                            TransactionTypeId = 9
                        },
                        new
                        {
                            Id = 14,
                            BankId = 1,
                            Code = "FNBSF",
                            Name = "FnbShortfallPayment",
                            TransactionTypeId = 10
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.Provider", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(20)")
                        .HasMaxLength(20);

                    b.HasKey("Id");

                    b.ToTable("Provider");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Name = "None"
                        },
                        new
                        {
                            Id = 2,
                            Name = "Evolve"
                        },
                        new
                        {
                            Id = 3,
                            Name = "Ozow"
                        },
                        new
                        {
                            Id = 4,
                            Name = "Hyphen"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.Transaction", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<long>("AccountId")
                        .HasColumnType("bigint");

                    b.Property<double>("Amount")
                        .HasColumnType("float");

                    b.Property<int>("BranchId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<string>("BuyLeadReference")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(20)")
                        .HasMaxLength(20)
                        .HasDefaultValue("False");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(3)")
                        .HasMaxLength(3);

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTimeOffset?>("PaymentDate")
                        .IsRequired()
                        .HasColumnType("datetimeoffset");

                    b.Property<int>("ProviderId")
                        .HasColumnType("int");

                    b.Property<string>("ProviderReference")
                        .HasColumnType("nvarchar(50)")
                        .HasMaxLength(50);

                    b.Property<string>("StockNumberReference")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(20)")
                        .HasMaxLength(20)
                        .HasDefaultValue("False");

                    b.Property<string>("TransactionReference")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasMaxLength(50);

                    b.Property<int>("TransactionStatusId")
                        .HasColumnType("int");

                    b.Property<int>("TransactionTypeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.HasKey("Id");

                    b.HasIndex("AccountId");

                    b.HasIndex("BranchId");

                    b.HasIndex("ProviderId");

                    b.HasIndex("TransactionReference")
                        .IsUnique();

                    b.HasIndex("TransactionStatusId");

                    b.HasIndex("TransactionTypeId");

                    b.ToTable("Transaction");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.TransactionStatus", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(20)")
                        .HasMaxLength(20);

                    b.HasKey("Id");

                    b.ToTable("TransactionStatus");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Name = "Complete"
                        },
                        new
                        {
                            Id = 2,
                            Name = "Cancelled"
                        },
                        new
                        {
                            Id = 3,
                            Name = "Error"
                        },
                        new
                        {
                            Id = 4,
                            Name = "Abandoned"
                        },
                        new
                        {
                            Id = 5,
                            Name = "PendingInvestigation"
                        },
                        new
                        {
                            Id = 6,
                            Name = "Pending"
                        },
                        new
                        {
                            Id = 7,
                            Name = "Submitted"
                        },
                        new
                        {
                            Id = 8,
                            Name = "Failed"
                        },
                        new
                        {
                            Id = 9,
                            Name = "Returned"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.TransactionType", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(250)")
                        .HasMaxLength(250);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(20)")
                        .HasMaxLength(20);

                    b.HasKey("Id");

                    b.ToTable("TransactionType");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Description = "Unknown.",
                            Name = "Unknown"
                        },
                        new
                        {
                            Id = 2,
                            Description = "Online Auction transaction e.g. auction deposit.",
                            Name = "Auction"
                        },
                        new
                        {
                            Id = 3,
                            Description = "Buy Now transaction. Stock scheduled for auction.",
                            Name = "BuyNow"
                        },
                        new
                        {
                            Id = 4,
                            Description = "Cash transaction.",
                            Name = "Cash"
                        },
                        new
                        {
                            Id = 5,
                            Description = "Finance transaction.",
                            Name = "Finance"
                        },
                        new
                        {
                            Id = 6,
                            Description = "Refund.",
                            Name = "Refund"
                        },
                        new
                        {
                            Id = 7,
                            Description = "Settlement. Hyphen - Settlement Payment",
                            Name = "Settlement"
                        },
                        new
                        {
                            Id = 8,
                            Description = "Deposit. Hyphen - Deposit Payment",
                            Name = "Deposit"
                        },
                        new
                        {
                            Id = 9,
                            Description = "BalancePayment. Hyphen - Balance Payment",
                            Name = "BalancePayment"
                        },
                        new
                        {
                            Id = 10,
                            Description = "ShortfallPayment. Hyphen - Shortfall Payment",
                            Name = "ShortfallPayment"
                        });
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.Account", b =>
                {
                    b.HasOne("WeBuyCars.Financial.Core.Entities.AccountType", "AccountType")
                        .WithMany()
                        .HasForeignKey("AccountTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BankAccount", b =>
                {
                    b.HasOne("WeBuyCars.Financial.Core.Entities.BankAccountHolder", "BankAccountHolder")
                        .WithMany()
                        .HasForeignKey("BankAccountHolderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.BankAccountType", "BankAccountType")
                        .WithMany()
                        .HasForeignKey("BankAccountTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BankAccountHolder", b =>
                {
                    b.HasOne("WeBuyCars.Financial.Core.Entities.BankAccountHolderType", "BankAccountHolderType")
                        .WithMany()
                        .HasForeignKey("BankAccountHolderTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.BankAccountVerification", b =>
                {
                    b.HasOne("WeBuyCars.Financial.Core.Entities.BankAccount", "BankAccount")
                        .WithMany()
                        .HasForeignKey("BankAccountId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.Provider", "Provider")
                        .WithMany()
                        .HasForeignKey("ProviderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.Consent", b =>
                {
                    b.HasOne("WeBuyCars.Financial.Core.Entities.Transaction", null)
                        .WithMany("Consents")
                        .HasForeignKey("TransactionId");
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.HyphenTransactionDetail", b =>
                {
                    b.HasOne("WeBuyCars.Financial.Core.Entities.Bank", "Bank")
                        .WithMany()
                        .HasForeignKey("BankId");

                    b.HasOne("WeBuyCars.Financial.Core.Entities.BankAccountHolderType", "ClientBankAccountHolderType")
                        .WithMany()
                        .HasForeignKey("ClientBankAccountHolderTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.BankAccountType", "ClientBankAccountType")
                        .WithMany()
                        .HasForeignKey("ClientBankAccountTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.HyphenProcessingOption1", "ProcessingOption1")
                        .WithMany()
                        .HasForeignKey("ProcessingOption1Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.HyphenProcessingOption2", "ProcessingOption2")
                        .WithMany()
                        .HasForeignKey("ProcessingOption2Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.Transaction", "Transaction")
                        .WithMany()
                        .HasForeignKey("TransactionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.HyphenTransactionType", "TransactionType")
                        .WithMany()
                        .HasForeignKey("TransactionTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.HyphenTransactionType", b =>
                {
                    b.HasOne("WeBuyCars.Financial.Core.Entities.Bank", "Bank")
                        .WithMany("HyphenTransactionTypes")
                        .HasForeignKey("BankId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.TransactionType", "TransactionType")
                        .WithMany()
                        .HasForeignKey("TransactionTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();
                });

            modelBuilder.Entity("WeBuyCars.Financial.Core.Entities.Transaction", b =>
                {
                    b.HasOne("WeBuyCars.Financial.Core.Entities.Account", "Account")
                        .WithMany("Transactions")
                        .HasForeignKey("AccountId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.Branch", "Branch")
                        .WithMany()
                        .HasForeignKey("BranchId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.Provider", "Provider")
                        .WithMany()
                        .HasForeignKey("ProviderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.TransactionStatus", "TransactionStatus")
                        .WithMany()
                        .HasForeignKey("TransactionStatusId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WeBuyCars.Financial.Core.Entities.TransactionType", "TransactionType")
                        .WithMany()
                        .HasForeignKey("TransactionTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });
#pragma warning restore 612, 618
        }
    }
}
