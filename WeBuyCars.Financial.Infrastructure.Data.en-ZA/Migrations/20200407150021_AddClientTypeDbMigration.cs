using Microsoft.EntityFrameworkCore.Migrations;

namespace WeBuyCars.Financial.Infrastructure.Data.Migrations
{
    public partial class AddClientTypeDbMigration : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "ClientTypeId",
                schema: "FIN",
                table: "Account",
                nullable: false,
                defaultValue: 1);

            migrationBuilder.CreateTable(
                name: "ClientType",
                schema: "FIN",
                columns: table => new
                {
                    Id = table.Column<int>(nullable: false),
                    Name = table.Column<string>(maxLength: 20, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ClientType", x => x.Id);
                });

            migrationBuilder.InsertData(
                schema: "FIN",
                table: "ClientType",
                columns: new[] { "Id", "Name" },
                values: new object[,]
                {
                    { 1, "Unknown" },
                    { 2, "Private" },
                    { 3, "Dealer" },
                    { 4, "Wholesaler" }
                });

            migrationBuilder.CreateIndex(
                name: "IX_Account_ClientTypeId",
                schema: "FIN",
                table: "Account",
                column: "ClientTypeId");

            migrationBuilder.AddForeignKey(
                name: "FK_Account_ClientType_ClientTypeId",
                schema: "FIN",
                table: "Account",
                column: "ClientTypeId",
                principalSchema: "FIN",
                principalTable: "ClientType",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Account_ClientType_ClientTypeId",
                schema: "FIN",
                table: "Account");

            migrationBuilder.DropTable(
                name: "ClientType",
                schema: "FIN");

            migrationBuilder.DropIndex(
                name: "IX_Account_ClientTypeId",
                schema: "FIN",
                table: "Account");

            migrationBuilder.DropColumn(
                name: "ClientTypeId",
                schema: "FIN",
                table: "Account");
        }
    }
}
