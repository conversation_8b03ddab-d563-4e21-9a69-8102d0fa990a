using Microsoft.EntityFrameworkCore.Migrations;
using WeBuyCars.Financial.Infrastructure.Data.en_ZA.Extensions.MigrationBuilderExtensions;

#nullable disable

namespace WeBuyCars.Financial.Infrastructure.Data.Migrations
{
    public partial class RemoveBankVerification_FirstNameProperty : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "FirstName",
                schema: "FIN",
                table: "BankAccountHolder");
            
            migrationBuilder.Drop_AlterOutstandingPaymentVerificationView_AddFirstName();
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "FirstName",
                schema: "FIN",
                table: "BankAccountHolder",
                maxLength: 60,
                nullable: true);
            
            migrationBuilder.AlterOutstandingPaymentVerificationView_AddFirstName();
        }
    }
}
