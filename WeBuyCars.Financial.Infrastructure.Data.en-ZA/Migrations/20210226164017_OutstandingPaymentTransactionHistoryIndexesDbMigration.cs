using Microsoft.EntityFrameworkCore.Migrations;

namespace WeBuyCars.Financial.Infrastructure.Data.Migrations
{
    public partial class OutstandingPaymentTransactionHistoryIndexesDbMigration : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_OutstandingPaymentTransactionHistory_BuyLeadReference",
                schema: "FIN",
                table: "OutstandingPaymentTransactionHistory",
                column: "BuyLeadReference");

            migrationBuilder.CreateIndex(
                name: "IX_OutstandingPaymentTransactionHistory_TransactionReference",
                schema: "FIN",
                table: "OutstandingPaymentTransactionHistory",
                column: "TransactionReference");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_OutstandingPaymentTransactionHistory_BuyLeadReference",
                schema: "FIN",
                table: "OutstandingPaymentTransactionHistory");

            migrationBuilder.DropIndex(
                name: "IX_OutstandingPaymentTransactionHistory_TransactionReference",
                schema: "FIN",
                table: "OutstandingPaymentTransactionHistory");
        }
    }
}
