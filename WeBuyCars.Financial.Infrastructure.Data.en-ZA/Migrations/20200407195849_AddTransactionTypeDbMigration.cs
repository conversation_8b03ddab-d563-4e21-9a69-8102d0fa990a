using Microsoft.EntityFrameworkCore.Migrations;

namespace WeBuyCars.Financial.Infrastructure.Data.Migrations
{
    public partial class AddTransactionTypeDbMigration : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "TransactionTypeId",
                schema: "FIN",
                table: "Transaction",
                nullable: false,
                defaultValue: 1);

            migrationBuilder.CreateTable(
                name: "TransactionType",
                schema: "FIN",
                columns: table => new
                {
                    Id = table.Column<int>(nullable: false),
                    Name = table.Column<string>(maxLength: 20, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TransactionType", x => x.Id);
                });

            migrationBuilder.InsertData(
                schema: "FIN",
                table: "TransactionType",
                columns: new[] { "Id", "Name" },
                values: new object[,]
                {
                    { 1, "Unknown" },
                    { 2, "Deposit" },
                    { 3, "Refund" },
                    { 5, "BuyNow" }
                });

            migrationBuilder.CreateIndex(
                name: "IX_Transaction_TransactionTypeId",
                schema: "FIN",
                table: "Transaction",
                column: "TransactionTypeId");

            migrationBuilder.AddForeignKey(
                name: "FK_Transaction_TransactionType_TransactionTypeId",
                schema: "FIN",
                table: "Transaction",
                column: "TransactionTypeId",
                principalSchema: "FIN",
                principalTable: "TransactionType",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Transaction_TransactionType_TransactionTypeId",
                schema: "FIN",
                table: "Transaction");

            migrationBuilder.DropTable(
                name: "TransactionType",
                schema: "FIN");

            migrationBuilder.DropIndex(
                name: "IX_Transaction_TransactionTypeId",
                schema: "FIN",
                table: "Transaction");

            migrationBuilder.DropColumn(
                name: "TransactionTypeId",
                schema: "FIN",
                table: "Transaction");
        }
    }
}
