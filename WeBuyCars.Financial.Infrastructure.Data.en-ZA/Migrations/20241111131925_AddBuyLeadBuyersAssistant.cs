using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WeBuyCars.Financial.Infrastructure.Data.Migrations
{
    public partial class AddBuyLeadBuyersAssistant : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<long>(
                name: "BuyLeadBuyersAssistantId",
                schema: "FIN",
                table: "BuyLead",
                type: "bigint",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "BuyLeadBuyersAssistant",
                schema: "FIN",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MobileNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    EmailAddress = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BuyerAssistantId = table.Column<int>(type: "int", nullable: false),
                    Deleted = table.Column<bool>(type: "bit", nullable: false),
                    CreatedOn = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    ModifiedOn = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ModifiedBy = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BuyLeadBuyersAssistant", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_BuyLead_BuyLeadBuyersAssistantId",
                schema: "FIN",
                table: "BuyLead",
                column: "BuyLeadBuyersAssistantId");

            migrationBuilder.AddForeignKey(
                name: "FK_BuyLead_BuyLeadBuyersAssistant_BuyLeadBuyersAssistantId",
                schema: "FIN",
                table: "BuyLead",
                column: "BuyLeadBuyersAssistantId",
                principalSchema: "FIN",
                principalTable: "BuyLeadBuyersAssistant",
                principalColumn: "Id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_BuyLead_BuyLeadBuyersAssistant_BuyLeadBuyersAssistantId",
                schema: "FIN",
                table: "BuyLead");

            migrationBuilder.DropTable(
                name: "BuyLeadBuyersAssistant",
                schema: "FIN");

            migrationBuilder.DropIndex(
                name: "IX_BuyLead_BuyLeadBuyersAssistantId",
                schema: "FIN",
                table: "BuyLead");

            migrationBuilder.DropColumn(
                name: "BuyLeadBuyersAssistantId",
                schema: "FIN",
                table: "BuyLead");
        }
    }
}
