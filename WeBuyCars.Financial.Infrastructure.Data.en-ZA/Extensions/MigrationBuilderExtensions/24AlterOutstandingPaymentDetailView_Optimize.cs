using Microsoft.EntityFrameworkCore.Migrations;

namespace WeBuyCars.Financial.Infrastructure.Data.en_ZA.Extensions.MigrationBuilderExtensions
{
    public static partial class MigrationBuilderExtensions 
    {
        public static void AlterOutstandingPaymentDetailView_Optimize(this MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
				ALTER VIEW [FIN].[OutstandingPaymentDetailView] AS					
					WITH CTE_OutstandingPayments AS
					(
					    SELECT
					        bl.Id,
					        CAST(ISNULL(SUM(ctr.[Amount] * 1.00), 0.00) AS DECIMAL(18,2)) AS BuyLeadCompletedTotal,
					        CAST(blv.[PurchasePrice] - ISNULL(SUM(ctr.[Amount] * 1.00), 0.00) AS DECIMAL(18,2)) AS BuyLeadCompletedOutstanding
					    FROM
					        [FIN].[BuyLead] bl
					        INNER JOIN [FIN].[BuyLeadVehicle] blv ON
					            bl.[Id] = blv.[BuyLeadId]
					        LEFT JOIN [FIN].[Transaction] ctr ON
					            ctr.[BuyLeadReference] != '' AND
					            ctr.[BuyLeadReference] IS NOT NULL AND
					            bl.[BuyLeadCode] = ctr.[BuyLeadReference] AND
					            ctr.TransactionStatusId = 1 AND
					            ctr.[ProviderId] IN (4, 5)
					    GROUP BY
					        bl.Id,
					        blv.PurchasePrice
					),
					CTE_BranchCodeBank AS
					(
					    SELECT '31%'	AS [BranchCodePrefix] ,'ABSA' AS [BranchBank] UNION
					    SELECT '33%'	AS [BranchCodePrefix] ,'ABSA' AS [BranchBank] UNION
					    SELECT '42%'	AS [BranchCodePrefix] ,'ABSA' AS [BranchBank] UNION
					    SELECT '50%'	AS [BranchCodePrefix] ,'ABSA' AS [BranchBank] UNION
					    SELECT '51%'	AS [BranchCodePrefix] ,'ABSA' AS [BranchBank] UNION
					    SELECT '52%'	AS [BranchCodePrefix] ,'ABSA' AS [BranchBank] UNION
					    SELECT '63%'	AS [BranchCodePrefix] ,'ABSA' AS [BranchBank] UNION
					    SELECT '430000'	AS [BranchCodePrefix] ,'AFRICAN BANK LIMITED' UNION
					    SELECT '888000'	AS [BranchCodePrefix] ,'BANK ZERO' AS [BranchBank] UNION
					    SELECT '462005'	AS [BranchCodePrefix] ,'BIDVEST BANK LIMITED' AS [BranchBank] UNION
					    SELECT '470010'	AS [BranchCodePrefix] ,'CAPITEC BANK LIMITED' AS [BranchBank] UNION
					    SELECT '586666'	AS [BranchCodePrefix] ,'CHINA CONSTRUCTION BANK' AS [BranchBank] UNION
					    SELECT '350005'	AS [BranchCodePrefix] ,'CITIBANK NA SOUTH AFRICA' AS [BranchBank] UNION
					    SELECT '679000'	AS [BranchCodePrefix] ,'DISCOVERY BANK LIMITED' AS [BranchBank] UNION
					    SELECT '2%'		AS [BranchCodePrefix] ,'FIRSTRAND BANK' AS [BranchBank] UNION
					    SELECT '584000'	AS [BranchCodePrefix] ,'GRINDROD BANK LIMITED' AS [BranchBank] UNION
					    SELECT '580105'	AS [BranchCodePrefix] ,'INVESTEC BANK LIMITED' AS [BranchBank] UNION
					    SELECT '45%'	AS [BranchCodePrefix] ,'MERCANTILE BANK LIMITED' AS [BranchBank] UNION
					    SELECT '1%'		AS [BranchCodePrefix] ,'NEDBANK LIMITED' AS [BranchBank] UNION
					    SELECT '760005'	AS [BranchCodePrefix] ,'PERMANENT BNK CBD BUS SUP B992' AS [BranchBank] UNION
					    SELECT '41%'	AS [BranchCodePrefix] ,'S.A. BANK OF ATHENS LTD.' AS [BranchBank] UNION
					    SELECT '683000'	AS [BranchCodePrefix] ,'SASFIN BANK LIMITED' AS [BranchBank] UNION
					    SELECT '460005'	AS [BranchCodePrefix] ,'SOUTH AFRICAN POST OFFICE ' AS [BranchBank] UNION
					    SELECT '0%'		AS [BranchCodePrefix] ,'STANDARD BANK OF S.A. LTD.' AS [BranchBank] UNION
					    SELECT '730%'	AS [BranchCodePrefix] ,'STANDARD CHARTERED PERS LOAN ' AS [BranchBank] UNION
					    SELECT '9%'		AS [BranchCodePrefix] ,'S.A. RESERVE BANK.' AS [BranchBank] UNION
					    SELECT '678910'	AS [BranchCodePrefix] ,'TYME BANK' AS [BranchBank] UNION
					    SELECT '431010'	AS [BranchCodePrefix] ,'UBANK LTD' AS [BranchBank]
					)
					SELECT
					    CAST(bl.[BuyLeadCode] AS VARCHAR(40)) AS [BuyLeadCode],
					    bl.[LeadCreationDateTime] AS BuyLeadCreationDateTime,
					    bl.[CreatedOn] AS BuyLeadRefCreatedOn,
					    CAST(IIF(bl.[LastAcceptedBy] IS NULL, 0, 1) AS BIT) AS BuyLeadAccepted,
					    bl.[LastAcceptedBy] AS BuyLeadLastAcceptedBy,
					    bl.[LastAcceptedOn] AS BuyLeadLastAcceptedOn,
					    blcs.[Name] AS BuyLeadCompleteStatus,
					    bl.[LastCompletedBy] AS BuyLeadLastCompletedBy,
					    bl.[LastCompletedOn] AS BuyLeadLastCompletedOn,
					    bl.[BankAccountsManuallyVerified] AS BuyLeadBankAccountsManuallyVerified,
					    bl.[BankAccountsManuallyVerifiedBy] AS BuyLeadBankAccountsManuallyVerifiedBy,
					    bl.[BankAccountsManuallyVerifiedOn] AS BuyLeadBankAccountsManuallyVerifiedOn,
					    bl.[DocumentsApproved] AS BuyLeadDocumentsApproved,
					    bl.[DocumentsApprovedBy] AS BuyLeadDocumentsApprovedBy,
					    bl.[DocumentsApprovedOn] AS BuyLeadDocumentsApprovedOn,
					    CAST(blv.[Year] AS VARCHAR(4)) AS BuyLeadVehicleYear,
					    blv.[Make] AS BuyLeadVehicleMake,
					    blv.[Model] AS BuyLeadVehicleModel,
					    blv.[Variant] AS BuyLeadVehicleVariant,
					    blv.[VinNumber] AS BuyLeadVehicleVinNumber,
					    blv.[RegisterNumber] AS BuyLeadVehicleRegisterNumber,
					    blv.[EngineNumber] AS BuyLeadVehicleEngineNumber,
					    blv.[LicensePlateNumber] AS BuyLeadVehicleLicensePlateNumber,
					    blv.[MmCode] AS BuyLeadVehicleMmCode,
					    CAST(blv.[PurchasePrice] AS DECIMAL(18,2)) AS BuyLeadVehiclePurchasePrice,
					    blv.[PurchaseDateTime] AS BuyLeadVehiclePurchaseDateTime,
					    blc.[Name] AS BuyLeadClientName,
					    blc.[Surname] AS BuyLeadClientSurname,
					    blb.[Name] AS BuyerName,
					    blb.[MobileNumber] AS BuyerMobileNumber,
					    blb.[EmailAddress] AS BuyerEmailAddress,
					    sic.[AccountNumber] AS CustomerFinancialAccountNumber,
					    sic.[Initials] AS [CustomerInitials],
					    sic.[FirstName] AS [CustomerFirstName],
					    sic.[LastName] AS [CustomerLastName],
					    sic.[BusinessName] AS [CustomerBusinessName],
					    COALESCE(CONCAT(sic.[FirstName], ' ', sic.[LastName]),sic.[BusinessName]) AS [CustomerName],
					    sic.[IdNumber] AS [CustomerIdNumber],
					    sic.[PassportNumber] AS [CustomerPassportNumber],
					    sic.[RegisterNumber] AS [CustomerRegisterNumber],
					    sic.[BusinessRegistrationNumber] AS [CustomerBusinessRegistrationNumber],
					    sic.[CompanyRegistrationNumber] AS [CustomerCompanyRegistrationNumber],
					    COALESCE(sic.[IdNumber],COALESCE(sic.[PassportNumber],COALESCE(sic.[CompanyRegistrationNumber],sic.[BusinessRegistrationNumber]))) AS [CustomerIdentification],
					    CASE WHEN sic.[CustomerType] = 0 THEN 'Unknown'
					         WHEN sic.[CustomerType] = 1 THEN 'Private'
					         WHEN sic.[CustomerType] = 2 THEN 'Dealership'
					         WHEN sic.[CustomerType] = 3 THEN 'Wholesale'
					         WHEN sic.[CustomerType] = 4 THEN 'Supplier'
					         WHEN sic.[CustomerType] = 5 THEN 'Company'
					         WHEN sic.[CustomerType] = 99 THEN 'Prospect'
					        END AS [CustomerType],
					    op.[BuyLeadCompletedTotal],
					    op.[BuyLeadCompletedOutstanding],
					    htd.[ClientBankAccountNumber] AS TransactionBankAccountNumber,
					    htd.[ClientBranchCode] AS TransactionBankAccountBranchCode,
					    bcbt.[BranchBank] AS TransactionBankAccountBranchBank,
					    htd.[ClientName] AS HyphenTransactionClientName,
					    htt.[Code] AS HyphenTransactionTypeCode,
					    htd.[Code2] AS HyphenTransactionReference,
					    baht.[Name] AS TransactionBankAccountHolderType,
					    bat.[Name] AS TransactionBankAccountType,
					    p.[Name] AS [TransactionProvider],
					    tt.[Name] AS TransactionType,
					    t.[CurrencyCode] AS TransactionCurrencyCode,
					    CAST(t.[Amount] AS DECIMAL(18,2)) AS TransactionAmount,
					    IIF(ts.[Name] = 'Complete', 'Completed', ts.[Name]) AS TransactionStatus,
					    t.[ProviderReference] AS TransactionProviderReference,
					    t.[TransactionReference],
					    tcr.[Name] AS [TransactionCancellationReason],
					    tcr.[Description] AS [TransactionCancellationReasonDescription],
					    t.[PaymentDate] AS [TransactionPaymentDate],
					    t.[CreatedOn] AS TransactionCreatedOn,
					    t.[CreatedBy] AS TransactionCreatedBy,
					    t.[ModifiedOn] AS TransactionModifiedOn,
					    t.[ModifiedBy] AS TransactionModifiedBy,
					    fh.[Name] AS [TransactionFinanceHouseName]
					FROM
					    [FIN].[BuyLead] bl
					    INNER JOIN [FIN].[BuyLeadVehicle] blv ON
					        bl.[Id] = blv.[BuyLeadId]
					    INNER JOIN [FIN].[BuyLeadClient] blc ON
					        bl.[Id] = blc.[BuyLeadId]
					    INNER JOIN [FIN].[BuyLeadBuyer] blb ON
					        bl.[BuyLeadBuyerId] = blb.[Id]
					    INNER JOIN CTE_OutstandingPayments AS op ON
					        bl.Id = op.Id
					    LEFT JOIN [FIN].[BuyLeadCompleteStatus] blcs ON
					        bl.[BuyLeadCompleteStatusId] = blcs.[Id]
					    LEFT JOIN [IMS].StockItem si ON
					        bl.BuyLeadCode = si.BuyLeadCode
					    LEFT JOIN [CRM].Customer sic ON
					        si.CrmSellerAccNumber = sic.AccountNumber
					    LEFT JOIN [FIN].[Transaction] t ON
					            t.BuyLeadReference != '' AND
					            t.BuyLeadReference IS NOT NULL AND
					            bl.BuyLeadCode = t.BuyLeadReference
					    LEFT JOIN [FIN].[TransactionType] tt ON
					        t.TransactionTypeId = tt.Id
					    LEFT JOIN [FIN].[TransactionStatus] ts ON
					        t.TransactionStatusId = ts.Id
					    LEFT JOIN [FIN].[Provider] p ON
					        t.ProviderId = p.Id
					    LEFT JOIN [FIN].[TransactionCancellationReason] tcr ON
					        t.TransactionCancellationReasonId = tcr.Id
					    LEFT JOIN [FIN].[HyphenTransactionDetail] htd ON
					            t.ProviderId = 4 AND
					            htd.TransactionId = t.Id
					    LEFT JOIN CTE_BranchCodeBank bcbt ON
					        htd.[ClientBranchCode] LIKE bcbt.[BranchCodePrefix]
					    LEFT JOIN [FIN].[HyphenTransactionType] htt ON
					        htd.TransactionTypeId = htt.Id
					    LEFT JOIN [FIN].[BankAccountType] bat ON
					        bat.Id = htd.ClientBankAccountTypeId
					    LEFT JOIN [FIN].[BankAccountHolderType] baht ON
					        baht.Id = htd.ClientBankAccountHolderTypeId
					    LEFT JOIN [FIN].FinanceHouse fh ON
					        (htd.TransactionTypeId = 7 OR
					         htd.TransactionTypeId = 11) AND
					        RIGHT(htd.ClientBankAccountNumber, 13) = RIGHT('***************'+fh.BankAccountNumber, 13) AND
					        htd.ClientBranchCode = RIGHT('000000'+fh.BankBranchCode, 6)
					-- WHERE bl.BuyLeadCode = 'LQL290306'
					-- WHERE bl.BuyLeadCode = 'LUD151261MIDWH'
					-- WHERE bl.BuyLeadCode = 'LVB240145'
			");
        }

        public static void Drop_AlterOutstandingPaymentDetailView_Optimize(this MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
				ALTER VIEW [FIN].[OutstandingPaymentDetailView] AS					
					WITH CTE_OutstandingPayments AS (
					    SELECT
					        CAST(bl.[BuyLeadCode] AS VARCHAR(40)) AS [BuyLeadCode],
					        bl.[LeadCreationDateTime] AS BuyLeadCreationDateTime,
					        bl.[CreatedOn] AS BuyLeadRefCreatedOn,
					        CAST(IIF(bl.[LastAcceptedBy] IS NULL, 0, 1) AS BIT) AS BuyLeadAccepted,
					        bl.[LastAcceptedBy] AS BuyLeadLastAcceptedBy,
					        bl.[LastAcceptedOn] AS BuyLeadLastAcceptedOn,
					        blcs.[Name] AS BuyLeadCompleteStatus,
					        bl.[LastCompletedBy] AS BuyLeadLastCompletedBy,
					        bl.[LastCompletedOn] AS BuyLeadLastCompletedOn,
					        bl.[BankAccountsManuallyVerified] AS BuyLeadBankAccountsManuallyVerified,
					        bl.[BankAccountsManuallyVerifiedBy] AS BuyLeadBankAccountsManuallyVerifiedBy,
					        bl.[BankAccountsManuallyVerifiedOn] AS BuyLeadBankAccountsManuallyVerifiedOn,
					        bl.[DocumentsApproved] AS BuyLeadDocumentsApproved,
					        bl.[DocumentsApprovedBy] AS BuyLeadDocumentsApprovedBy,
					        bl.[DocumentsApprovedOn] AS BuyLeadDocumentsApprovedOn,
					        CAST(blv.[Year] AS VARCHAR(4)) AS BuyLeadVehicleYear,
					        blv.[Make] AS BuyLeadVehicleMake,
					        blv.[Model] AS BuyLeadVehicleModel,
					        blv.[Variant] AS BuyLeadVehicleVariant,
					        blv.[VinNumber] AS BuyLeadVehicleVinNumber,
					        blv.[RegisterNumber] AS BuyLeadVehicleRegisterNumber,
					        blv.[EngineNumber] AS BuyLeadVehicleEngineNumber,
					        blv.[LicensePlateNumber] AS BuyLeadVehicleLicensePlateNumber,
					        blv.[MmCode] AS BuyLeadVehicleMmCode,
					        CAST(blv.[PurchasePrice] AS DECIMAL(18,2)) AS BuyLeadVehiclePurchasePrice,
					        blv.[PurchaseDateTime] AS BuyLeadVehiclePurchaseDateTime,
					        blc.[Name] AS BuyLeadClientName,
					        blc.[Surname] AS BuyLeadClientSurname,
					        blb.[Name] AS BuyerName,
					        blb.[MobileNumber] AS BuyerMobileNumber,
					        blb.[EmailAddress] AS BuyerEmailAddress,
					        CAST(ISNULL(SUM(ctr.[Amount] * 1.00), 0.00) AS DECIMAL(18,2)) AS BuyLeadCompletedTotal,
					        CAST(blv.[PurchasePrice] - ISNULL(SUM(ctr.[Amount] * 1.00), 0.00) AS DECIMAL(18,2)) AS BuyLeadCompletedOutstanding
					    FROM
					        [FIN].[BuyLead] bl
					            INNER JOIN [FIN].[BuyLeadVehicle] blv ON
					                bl.[Id] = blv.[BuyLeadId]
					            INNER JOIN [FIN].[BuyLeadClient] blc ON
					                bl.[Id] = blc.[BuyLeadId]
					            INNER JOIN [FIN].[BuyLeadBuyer] blb ON
					                bl.[BuyLeadBuyerId] = blb.[Id]
					            LEFT JOIN [FIN].[BuyLeadCompleteStatus] blcs ON
					                bl.[BuyLeadCompleteStatusId] = blcs.[Id]
					            LEFT JOIN [FIN].[Transaction] ctr ON
					                    ctr.[BuyLeadReference] != '' AND
					                    ctr.[BuyLeadReference] IS NOT NULL AND
					                    bl.[BuyLeadCode] = ctr.[BuyLeadReference] AND
					                    ctr.TransactionStatusId = 1 AND
					                    ctr.[ProviderId] IN (4, 5)
					    GROUP BY
					        bl.[BuyLeadCode],
					        bl.[LeadCreationDateTime],
					        bl.[CreatedOn],
					        bl.[LastAcceptedBy],
					        bl.[LastAcceptedOn],
					        blcs.[Name],
					        bl.[LastCompletedBy],
					        bl.[LastCompletedOn],
					        bl.[BankAccountsManuallyVerified],
					        bl.[BankAccountsManuallyVerifiedBy],
					        bl.[BankAccountsManuallyVerifiedOn],
					        bl.[DocumentsApproved],
					        bl.[DocumentsApprovedBy],
					        bl.[DocumentsApprovedOn],
					        blv.[Year],
					        blv.[Make],
					        blv.[Model],
					        blv.[Variant],
					        blv.[VinNumber],
					        blv.[RegisterNumber],
					        blv.[EngineNumber],
					        blv.[LicensePlateNumber],
					        blv.[MmCode],
					        blv.[PurchasePrice],
					        blv.[PurchaseDateTime],
					        blc.[Name],
					        blc.[Surname],
					        blb.[Name],
					        blb.[MobileNumber],
					        blb.[EmailAddress]
					),
					     CTE_BankAccountVerificationSetting AS (
					         SELECT
					             bavs.VerificationExpirationDays,
					             MAX(bavs.CreatedOn) AS CreatedOn
					         FROM
					             [FIN].[BankAccountVerificationSetting] bavs
					         GROUP BY
					             bavs.VerificationExpirationDays
					     ),
					     CTE_BranchCodeBank AS (
					         SELECT '31%'	AS [BranchCodePrefix] ,'ABSA' AS [BranchBank] UNION
					         SELECT '33%'	AS [BranchCodePrefix] ,'ABSA' AS [BranchBank] UNION
					         SELECT '42%'	AS [BranchCodePrefix] ,'ABSA' AS [BranchBank] UNION
					         SELECT '50%'	AS [BranchCodePrefix] ,'ABSA' AS [BranchBank] UNION
					         SELECT '51%'	AS [BranchCodePrefix] ,'ABSA' AS [BranchBank] UNION
					         SELECT '52%'	AS [BranchCodePrefix] ,'ABSA' AS [BranchBank] UNION
					         SELECT '63%'	AS [BranchCodePrefix] ,'ABSA' AS [BranchBank] UNION
					         SELECT '430000'	AS [BranchCodePrefix] ,'AFRICAN BANK LIMITED' UNION
					         SELECT '888000'	AS [BranchCodePrefix] ,'BANK ZERO' AS [BranchBank] UNION
					         SELECT '462005'	AS [BranchCodePrefix] ,'BIDVEST BANK LIMITED' AS [BranchBank] UNION
					         SELECT '470010'	AS [BranchCodePrefix] ,'CAPITEC BANK LIMITED' AS [BranchBank] UNION
					         SELECT '586666'	AS [BranchCodePrefix] ,'CHINA CONSTRUCTION BANK' AS [BranchBank] UNION
					         SELECT '350005'	AS [BranchCodePrefix] ,'CITIBANK NA SOUTH AFRICA' AS [BranchBank] UNION
					         SELECT '679000'	AS [BranchCodePrefix] ,'DISCOVERY BANK LIMITED' AS [BranchBank] UNION
					         SELECT '2%'		AS [BranchCodePrefix] ,'FIRSTRAND BANK' AS [BranchBank] UNION
					         SELECT '584000'	AS [BranchCodePrefix] ,'GRINDROD BANK LIMITED' AS [BranchBank] UNION
					         SELECT '580105'	AS [BranchCodePrefix] ,'INVESTEC BANK LIMITED' AS [BranchBank] UNION
					         SELECT '45%'	AS [BranchCodePrefix] ,'MERCANTILE BANK LIMITED' AS [BranchBank] UNION
					         SELECT '1%'		AS [BranchCodePrefix] ,'NEDBANK LIMITED' AS [BranchBank] UNION
					         SELECT '760005'	AS [BranchCodePrefix] ,'PERMANENT BNK CBD BUS SUP B992' AS [BranchBank] UNION
					         SELECT '41%'	AS [BranchCodePrefix] ,'S.A. BANK OF ATHENS LTD.' AS [BranchBank] UNION
					         SELECT '683000'	AS [BranchCodePrefix] ,'SASFIN BANK LIMITED' AS [BranchBank] UNION
					         SELECT '460005'	AS [BranchCodePrefix] ,'SOUTH AFRICAN POST OFFICE ' AS [BranchBank] UNION
					         SELECT '0%'		AS [BranchCodePrefix] ,'STANDARD BANK OF S.A. LTD.' AS [BranchBank] UNION
					         SELECT '730%'	AS [BranchCodePrefix] ,'STANDARD CHARTERED PERS LOAN ' AS [BranchBank] UNION
					         SELECT '9%'		AS [BranchCodePrefix] ,'S.A. RESERVE BANK.' AS [BranchBank] UNION
					         SELECT '678910'	AS [BranchCodePrefix] ,'TYME BANK' AS [BranchBank] UNION
					         SELECT '431010'	AS [BranchCodePrefix] ,'UBANK LTD' AS [BranchBank]
					     )
					SELECT
					    op.[BuyLeadCode],
					    op.[BuyLeadCreationDateTime],
					    op.[BuyLeadRefCreatedOn],
					    op.[BuyLeadAccepted],
					    op.[BuyLeadLastAcceptedBy],
					    op.[BuyLeadLastAcceptedOn],
					    op.[BuyLeadCompleteStatus],
					    op.[BuyLeadLastCompletedBy],
					    op.[BuyLeadLastCompletedOn],
					    op.[BuyLeadBankAccountsManuallyVerified],
					    op.[BuyLeadBankAccountsManuallyVerifiedBy],
					    op.[BuyLeadBankAccountsManuallyVerifiedOn],
					    op.[BuyLeadDocumentsApproved],
					    op.[BuyLeadDocumentsApprovedBy],
					    op.[BuyLeadDocumentsApprovedOn],
					    op.[BuyLeadVehicleYear],
					    op.[BuyLeadVehicleMake],
					    op.[BuyLeadVehicleModel],
					    op.[BuyLeadVehicleVariant],
					    op.[BuyLeadVehicleVinNumber],
					    op.[BuyLeadVehicleRegisterNumber],
					    op.[BuyLeadVehicleEngineNumber],
					    op.[BuyLeadVehicleLicensePlateNumber],
					    op.[BuyLeadVehicleMmCode],
					    op.[BuyLeadVehiclePurchasePrice],
					    op.[BuyLeadVehiclePurchaseDateTime],
					    op.[BuyLeadClientName],
					    op.[BuyLeadClientSurname],
					    op.[BuyerName],
					    op.[BuyerMobileNumber],
					    op.[BuyerEmailAddress],
					    sic.[AccountNumber] AS CustomerFinancialAccountNumber,
					    sic.[Initials] AS [CustomerInitials],
					    sic.[FirstName] AS [CustomerFirstName],
					    sic.[LastName] AS [CustomerLastName],
					    sic.[BusinessName] AS [CustomerBusinessName],
					    COALESCE(CONCAT(sic.[FirstName], ' ', sic.[LastName]),sic.[BusinessName]) AS [CustomerName],
					    sic.[IdNumber] AS [CustomerIdNumber],
					    sic.[PassportNumber] AS [CustomerPassportNumber],
					    sic.[RegisterNumber] AS [CustomerRegisterNumber],
					    sic.[BusinessRegistrationNumber] AS [CustomerBusinessRegistrationNumber],
					    sic.[CompanyRegistrationNumber] AS [CustomerCompanyRegistrationNumber],
					    COALESCE(sic.[IdNumber],COALESCE(sic.[PassportNumber],COALESCE(sic.[CompanyRegistrationNumber],sic.[BusinessRegistrationNumber]))) AS [CustomerIdentification],
					    CASE WHEN sic.[CustomerType] = 0 THEN 'Unknown'
					         WHEN sic.[CustomerType] = 1 THEN 'Private'
					         WHEN sic.[CustomerType] = 2 THEN 'Dealership'
					         WHEN sic.[CustomerType] = 3 THEN 'Wholesale'
					         WHEN sic.[CustomerType] = 4 THEN 'Supplier'
					         WHEN sic.[CustomerType] = 5 THEN 'Company'
					         WHEN sic.[CustomerType] = 99 THEN 'Prospect'
					        END AS [CustomerType],
					    op.[BuyLeadCompletedTotal],
					    op.[BuyLeadCompletedOutstanding],
					    htd.[ClientBankAccountNumber] AS TransactionBankAccountNumber,
					    htd.[ClientBranchCode] AS TransactionBankAccountBranchCode,
					    bcbt.[BranchBank] AS TransactionBankAccountBranchBank,
					    htd.[ClientName] AS HyphenTransactionClientName,
					    htt.[Code] AS HyphenTransactionTypeCode,
					    htd.[Code2] AS HyphenTransactionReference,
					    baht.[Name] AS TransactionBankAccountHolderType,
					    bat.[Name] AS TransactionBankAccountType,
					    p.[Name] AS [TransactionProvider],
					    tt.[Name] AS TransactionType,
					    t.[CurrencyCode] AS TransactionCurrencyCode,
					    CAST(t.[Amount] AS DECIMAL(18,2)) AS TransactionAmount,
					    IIF(ts.[Name] = 'Complete', 'Completed', ts.[Name]) AS TransactionStatus,
					    t.[ProviderReference] AS TransactionProviderReference,
					    t.[TransactionReference],
					    tcr.[Name] AS [TransactionCancellationReason],
					    tcr.[Description] AS [TransactionCancellationReasonDescription],
					    t.[PaymentDate] AS [TransactionPaymentDate],
					    t.[CreatedOn] AS TransactionCreatedOn,
					    t.[CreatedBy] AS TransactionCreatedBy,
					    t.[ModifiedOn] AS TransactionModifiedOn,
					    t.[ModifiedBy] AS TransactionModifiedBy,
					    fh.[Name] AS [TransactionFinanceHouseName],
					    bav_ss.[BankAccountVerificationUserReference],
					    bav_ss.[BankAccountVerificationRequestDateTime],
					    bav_ss.[BankAccountVerificationProvider],
					    bav_ss.[BankAccountHolderType],
					    bav_ss.[BankAccountType],
					    bav_ss.[BankAccountVerificationBankAccountNumber],
					    bav_ss.[BankAccountVerificationBranchCode],
					    bcbv.[BranchBank] AS [BankAccountVerificationBranchBank],
					    bav_ss.[BankAccountVerificationIdNumber],
					    bav_ss.[BankAccountVerificationPassportNumber],
					    bav_ss.[BankAccountVerificationCompanyRegistrationNumber],
					    bav_ss.[BankAccountVerificationEstateNumber],
					    bav_ss.[BankAccountVerificationIdentification],
					    bav_ss.[BankAccountVerificationInitials],
					    bav_ss.[BankAccountVerificationLastName],
					    bav_ss.[BankAccountVerificationCompanyName],
					    bav_ss.[BankAccountVerificationEstateName],
					    bav_ss.[BankAccountVerificationIdentificationName],
					    bav_ss.[BankAccountVerificationPhoneNumber],
					    bav_ss.[BankAccountVerificationEmailAddress],
					    bav_ss.[AccountIdNumberMatchCode],
					    bav_ss.[AccountIdNumberMatch],
					    bav_ss.[AccountPassportNumberMatchCode],
					    bav_ss.[AccountPassportNumberMatch],
					    bav_ss.[AccountCompanyRegistrationNumberMatchCode],
					    bav_ss.[AccountCompanyRegistrationNumberMatch],
					    bav_ss.[AccountEstateNumberMatchCode],
					    bav_ss.[AccountEstateNumberMatch],
					    bav_ss.[InitialMatchCode],
					    bav_ss.[InitialMatch],
					    bav_ss.[LastNameMatchCode],
					    bav_ss.[LastNameMatch],
					    bav_ss.[CompanyNameMatchCode],
					    bav_ss.[CompanyNameMatch],
					    bav_ss.[EstateNameMatchCode],
					    bav_ss.[EstateNameMatch],
					    bav_ss.[AccountExistsCode],
					    bav_ss.[AccountExists],
					    bav_ss.[AccountOpenCode],
					    bav_ss.[AccountOpen],
					    bav_ss.[AccountAcceptsCreditsCode],
					    bav_ss.[AccountAcceptsCredits],
					    bav_ss.[AccountAcceptsDebitsCode],
					    bav_ss.[AccountAcceptsDebits],
					    bav_ss.[AccountOpenGtThreeMonthsCode],
					    bav_ss.[AccountOpenGtThreeMonths],
					    bav_ss.[PhoneValidCode],
					    bav_ss.[PhoneValid],
					    bav_ss.[EmailValidCode],
					    bav_ss.[EmailValid],
					    bav_ss.[AccountTypeValidCode],
					    bav_ss.[AccountTypeValid],
					    bav_ss.[BankAccountVerificationMessageCode],
					    bav_ss.[BankAccountVerificationMessageDescription]
					FROM
					    CTE_OutstandingPayments op
					        LEFT JOIN [FIN].[Transaction] t ON
					                t.BuyLeadReference != '' AND
					                t.BuyLeadReference IS NOT NULL AND
					                op.BuyLeadCode = t.BuyLeadReference
					        LEFT JOIN [IMS].StockItem si ON
					            op.BuyLeadCode = si.BuyLeadCode
					        LEFT JOIN [CRM].Customer sic ON
					            si.CrmSellerAccNumber = sic.AccountNumber
					        LEFT JOIN [FIN].[TransactionType] tt ON
					            t.TransactionTypeId = tt.Id
					        LEFT JOIN [FIN].[TransactionStatus] ts ON
					            t.TransactionStatusId = ts.Id
					        LEFT JOIN [FIN].[Provider] p ON
					            t.ProviderId = p.Id
					        LEFT JOIN [FIN].[TransactionCancellationReason] tcr ON
					            t.TransactionCancellationReasonId = tcr.Id
					        LEFT JOIN [FIN].[HyphenTransactionDetail] htd ON
					                t.ProviderId = 4 AND
					                htd.TransactionId = t.Id
					        LEFT JOIN CTE_BranchCodeBank bcbt ON
					            htd.[ClientBranchCode] LIKE bcbt.[BranchCodePrefix]
					        LEFT JOIN [FIN].[HyphenTransactionType] htt ON
					            htd.TransactionTypeId = htt.Id
					        LEFT JOIN [FIN].[BankAccountType] bat ON
					            bat.Id = htd.ClientBankAccountTypeId
					        LEFT JOIN [FIN].[BankAccountHolderType] baht ON
					            baht.Id = htd.ClientBankAccountHolderTypeId
					        LEFT JOIN [FIN].FinanceHouse fh ON
					            (htd.TransactionTypeId = 7 OR
					             htd.TransactionTypeId = 11) AND
					            RIGHT(htd.ClientBankAccountNumber, 13) = RIGHT('***************'+fh.BankAccountNumber, 13) AND
					            htd.ClientBranchCode = RIGHT('000000'+fh.BankBranchCode, 6)
					        LEFT JOIN (
					        SELECT
					            CAST(lbav.BuyLeadReference AS NVARCHAR(40)) AS [BankAccountVerificationBuyLeadReference],
					            bav.ClientReference AS [BankAccountVerificationUserReference],
					            MAX(bav.[RequestDateTime]) AS [BankAccountVerificationRequestDateTime],
					            p.[Name] AS [BankAccountVerificationProvider],
					            bah.BankAccountHolderTypeId,
					            baht.[Name] AS [BankAccountHolderType],
					            bat.[Name] AS [BankAccountType],
					            ba.[AccountNumber] AS [BankAccountVerificationBankAccountNumber],
					            ba.[BranchCode] AS [BankAccountVerificationBranchCode],
					            CASE
					                WHEN baht.Id = 1
					                    THEN bah.[IdNumber]
					                WHEN baht.Id = 2
					                    THEN bah.[PassportNumber]
					                WHEN baht.Id = 3
					                    THEN bah.[CompanyRegistrationNumber]
					                WHEN baht.Id = 4
					                    THEN bah.[EstateNumber]
					                WHEN baht.Id = 0
					                    THEN COALESCE(bah.[IdNumber],COALESCE(bah.[PassportNumber],COALESCE(bah.[CompanyRegistrationNumber],bah.[EstateNumber])))
					                END AS [BankAccountVerificationIdentification],
					            bah.[IdNumber] AS [BankAccountVerificationIdNumber],
					            bah.[PassportNumber] AS [BankAccountVerificationPassportNumber],
					            bah.[CompanyRegistrationNumber] AS [BankAccountVerificationCompanyRegistrationNumber],
					            bah.[EstateNumber] AS [BankAccountVerificationEstateNumber],
					            CASE
					                WHEN baht.Id = 1 OR baht.Id = 2
					                    THEN CONCAT(bah.[Initials], ' ', bah.[LastName])
					                WHEN baht.Id = 3
					                    THEN bah.[CompanyName]
					                WHEN baht.Id = 4
					                    THEN bah.[EstateName]
					                WHEN baht.Id = 0
					                    THEN COALESCE(CONCAT(bah.[Initials], ' ', bah.[LastName]),COALESCE(bah.[CompanyName],bah.[EstateName]))
					                END AS [BankAccountVerificationIdentificationName] ,
					            bah.[Initials] AS [BankAccountVerificationInitials],
					            bah.[LastName] AS [BankAccountVerificationLastName],
					            bah.[CompanyName] AS [BankAccountVerificationCompanyName],
					            bah.[EstateName] AS [BankAccountVerificationEstateName],
					            bah.[PhoneNumber] AS [BankAccountVerificationPhoneNumber],
					            bah.[EmailAddress] AS [BankAccountVerificationEmailAddress],
					            bav.[AccountIdNumberMatch] AS AccountIdNumberMatchCode,
					            bavmtidnm.[Name] AS AccountIdNumberMatch,
					            bav.[AccountPassportNumberMatch] AS AccountPassportNumberMatchCode,
					            bavmtpnm.[Name] AS AccountPassportNumberMatch,
					            bav.[AccountCompanyRegistrationNumberMatch] AS AccountCompanyRegistrationNumberMatchCode,
					            bavmtcrnm.[Name] AS AccountCompanyRegistrationNumberMatch,
					            bav.[AccountEstateNumberMatch] AS AccountEstateNumberMatchCode,
					            bavmtenom.[Name] AS AccountEstateNumberMatch,
					            bav.[InitialMatch] AS InitialMatchCode,
					            bavmtim.[Name] AS InitialMatch,
					            bav.[LastNameMatch] AS LastNameMatchCode,
					            bavmtlnm.[Name] AS LastNameMatch,
					            bav.[CompanyNameMatch] AS CompanyNameMatchCode,
					            bavmtcnm.[Name] AS CompanyNameMatch,
					            bav.[EstateNameMatch] AS EstateNameMatchCode,
					            bavmtenm.[Name] AS EstateNameMatch,
					            bav.[AccountExists] AS AccountExistsCode,
					            bavmtaem.[Name] AS AccountExists,
					            bav.[AccountOpen] AS AccountOpenCode,
					            bavmtaom.[Name] AS AccountOpen,
					            bav.[AccountAcceptsCredits] AS AccountAcceptsCreditsCode,
					            bavmtaacm.[Name] AS AccountAcceptsCredits,
					            bav.[AccountAcceptsDebits] AS AccountAcceptsDebitsCode,
					            bavmtaadm.[Name] AS AccountAcceptsDebits,
					            bav.[AccountOpenGtThreeMonths] AS AccountOpenGtThreeMonthsCode,
					            bavmtaogtm.[Name] AS AccountOpenGtThreeMonths,
					            bav.[PhoneValid] AS PhoneValidCode,
					            bavmtpm.[Name] AS PhoneValid,
					            bav.[EmailValid] AS EmailValidCode,
					            bavmtem.[Name] AS EmailValid,
					            bav.[AccountTypeValid] AS AccountTypeValidCode,
					            bavmtatm.[Name] AS AccountTypeValid,
					            bav.MessageCode AS [BankAccountVerificationMessageCode],
					            bav.MessageDescription AS [BankAccountVerificationMessageDescription]
					        FROM
					            CTE_BankAccountVerificationSetting bavs,
					            [FIN].[BankAccount] ba
					                INNER JOIN [FIN].[BankAccountHolder] bah ON ba.BankAccountHolderId = bah.Id
					                INNER JOIN [FIN].[BankAccountHolderType] baht ON bah.BankAccountHolderTypeId = baht.Id
					                INNER JOIN [FIN].[BankAccountVerification] bav ON ba.Id = bav.BankAccountId
					                    LEFT JOIN [FIN].[BankAccountVerificationBuyLead] bavbl ON bav.Id = bavbl.BankAccountVerificationId
					                INNER JOIN (
					                    SELECT
					                        CAST(VerificationBuyLead.BuyLeadReference AS NVARCHAR(40)) AS [BuyLeadReference],
					                        MAX(bav.[RequestDateTime]) AS [MaxRequestDateTime]
					                    FROM
					                        [FIN].[BankAccount] ba
					                            INNER JOIN [FIN].[BankAccountHolder] bah ON ba.BankAccountHolderId = bah.Id
					                            INNER JOIN [FIN].[BankAccountHolderType] baht ON bah.BankAccountHolderTypeId = baht.Id
					                            INNER JOIN [FIN].[BankAccountVerification] bav ON ba.Id = bav.BankAccountId
					                            INNER JOIN (
					                            SELECT
					                                bav.Id,
					                                CAST(
					                                        COALESCE(
					                                                NULLIF(bav.BuyLeadReference, ''),
					                                                bavbl.BuyLeadCode
					                                            ) AS NVARCHAR(40)
					                                    ) AS [BuyLeadReference]
					                            FROM [FIN].[BankAccountVerification] bav
					                                     LEFT JOIN [FIN].[BankAccountVerificationBuyLead] bavbl ON bav.Id = bavbl.BankAccountVerificationId
					                        ) AS VerificationBuyLead ON VerificationBuyLead.Id = bav.Id
					                    WHERE
					                        VerificationBuyLead.BuyLeadReference IS NOT NULL
					                    GROUP BY
					                        VerificationBuyLead.BuyLeadReference
					                ) lbav ON (bav.BuyLeadReference = lbav.BuyLeadReference OR bavbl.BuyLeadCode = lbav.BuyLeadReference)
					                    AND bav.RequestDateTime = lbav.MaxRequestDateTime
					                INNER JOIN [FIN].[Provider] p ON p.Id = bav.ProviderId
					                INNER JOIN [FIN].[BankAccountType] bat ON ba.BankAccountTypeId = bat.Id
					                LEFT JOIN [FIN].[BankAccountVerificationMatchType] bavmtidnm ON bav.AccountIdNumberMatch = bavmtidnm.Id
					                LEFT JOIN [FIN].[BankAccountVerificationMatchType] bavmtpnm ON bav.AccountPassportNumberMatch = bavmtpnm.Id
					                LEFT JOIN [FIN].[BankAccountVerificationMatchType] bavmtcrnm ON bav.AccountCompanyRegistrationNumberMatch = bavmtcrnm.Id
					                LEFT JOIN [FIN].[BankAccountVerificationMatchType] bavmtenom ON bav.AccountEstateNumberMatch = bavmtenom.Id
					                LEFT JOIN [FIN].[BankAccountVerificationMatchType] bavmtim ON bav.InitialMatch = bavmtim.Id
					                LEFT JOIN [FIN].[BankAccountVerificationMatchType] bavmtlnm ON bav.LastNameMatch = bavmtlnm.Id
					                LEFT JOIN [FIN].[BankAccountVerificationMatchType] bavmtcnm ON bav.CompanyNameMatch = bavmtcnm.Id
					                LEFT JOIN [FIN].[BankAccountVerificationMatchType] bavmtenm ON bav.EstateNameMatch = bavmtenm.Id
					                LEFT JOIN [FIN].[BankAccountVerificationMatchType] bavmtaem ON bav.AccountExists = bavmtaem.Id
					                LEFT JOIN [FIN].[BankAccountVerificationMatchType] bavmtaom ON bav.AccountOpen = bavmtaom.Id
					                LEFT JOIN [FIN].[BankAccountVerificationMatchType] bavmtaacm ON bav.AccountAcceptsCredits = bavmtaacm.Id
					                LEFT JOIN [FIN].[BankAccountVerificationMatchType] bavmtaadm ON bav.AccountAcceptsDebits = bavmtaadm.Id
					                LEFT JOIN [FIN].[BankAccountVerificationMatchType] bavmtaogtm ON bav.AccountOpenGtThreeMonths = bavmtaogtm.Id
					                LEFT JOIN [FIN].[BankAccountVerificationMatchType] bavmtpm ON bav.PhoneValid = bavmtpm.Id
					                LEFT JOIN [FIN].[BankAccountVerificationMatchType] bavmtem ON bav.EmailValid = bavmtem.Id
					                LEFT JOIN [FIN].[BankAccountVerificationMatchType] bavmtatm ON bav.AccountTypeValid = bavmtatm.Id
					        GROUP BY
					            lbav.BuyLeadReference,
					            bav.ClientReference,
					            bavs.VerificationExpirationDays,
					            p.[Name],
					            bah.BankAccountHolderTypeId,
					            baht.[Name],
					            baht.Id,
					            bat.[Name],
					            ba.[AccountNumber],
					            ba.[BranchCode],
					            bah.[IdNumber],
					            bah.[PassportNumber],
					            bah.[CompanyRegistrationNumber],
					            bah.[EstateNumber],
					            bah.[Initials],
					            bah.[LastName],
					            bah.[CompanyName],
					            bah.[EstateName],
					            bah.[PhoneNumber],
					            bah.[EmailAddress],
					            bav.[AccountIdNumberMatch],
					            bavmtidnm.[Name],
					            bav.[AccountPassportNumberMatch],
					            bavmtpnm.[Name],
					            bav.[AccountCompanyRegistrationNumberMatch],
					            bavmtcrnm.[Name],
					            bav.[AccountEstateNumberMatch],
					            bavmtenom.[Name],
					            bav.[InitialMatch],
					            bavmtim.[Name],
					            bav.[LastNameMatch],
					            bavmtlnm.[Name],
					            bav.[CompanyNameMatch],
					            bavmtcnm.[Name],
					            bav.[EstateNameMatch],
					            bavmtenm.[Name],
					            bav.[AccountExists],
					            bavmtaem.[Name],
					            bav.[AccountOpen],
					            bavmtaom.[Name],
					            bav.[AccountAcceptsCredits],
					            bavmtaacm.[Name],
					            bav.[AccountAcceptsDebits],
					            bavmtaadm.[Name],
					            bav.[AccountOpenGtThreeMonths],
					            bavmtaogtm.[Name],
					            bav.[PhoneValid],
					            bavmtpm.[Name],
					            bav.[EmailValid],
					            bavmtem.[Name],
					            bav.[AccountTypeValid],
					            bavmtatm.[Name],
					            bav.MessageCode,
					            bav.MessageDescription
					        HAVING MAX(bav.RequestDateTime) > DATEADD(DAY, -(bavs.VerificationExpirationDays), GETUTCDATE())
					    ) bav_ss ON
					                htd.TransactionTypeId != 7 AND
					                htd.TransactionTypeId != 11 AND
					                RIGHT(htd.ClientBankAccountNumber, 13) = RIGHT(bav_ss.[BankAccountVerificationBankAccountNumber], 13) AND
					                htd.ClientBranchCode = bav_ss.BankAccountVerificationBranchCode AND
					                bav_ss.BankAccountVerificationBuyLeadReference = op.[BuyLeadCode]
					        LEFT JOIN CTE_BranchCodeBank bcbv ON
					            bav_ss.[BankAccountVerificationBranchCode] LIKE bcbv.[BranchCodePrefix]
					--WHERE op.BuyLeadCode = 'LUL070407'
			");
        }
    }
}