using Microsoft.EntityFrameworkCore.Migrations;

namespace WeBuyCars.Financial.Infrastructure.Data.en_ZA.Extensions.MigrationBuilderExtensions;

public static partial class MigrationBuilderExtensions
{
    public static void AlterOutstandingPaymentDetailView_FixHbzBank(this MigrationBuilder migrationBuilder)
    {
	    //language=azure
        migrationBuilder.Sql(@"
			ALTER VIEW [FIN].[OutstandingPaymentDetailView] AS					
				WITH CTE_OutstandingPayments AS
				(
				    SELECT
				        bl.Id,
				        CAST(ISNULL(SUM(ctr.[Amount] * 1.00), 0.00) AS DECIMAL(18,2)) AS BuyLeadCompletedTotal,
				        CAST(blv.[PurchasePrice] - ISNULL(SUM(ctr.[Amount] * 1.00), 0.00) AS DECIMAL(18,2)) AS BuyLeadCompletedOutstanding
				    FROM
				        [FIN].[BuyLead] bl
				        INNER JOIN [FIN].[BuyLeadVehicle] blv ON
				            bl.[Id] = blv.[BuyLeadId]
				        LEFT JOIN [FIN].[Transaction] ctr ON
				            ctr.[BuyLeadReference] != '' AND
				            ctr.[BuyLeadReference] IS NOT NULL AND
				            bl.[BuyLeadCode] = ctr.[BuyLeadReference] AND
				            ctr.TransactionStatusId = 1 AND
				            ctr.[ProviderId] IN (4, 5)
				    GROUP BY
				        bl.Id,
				        blv.PurchasePrice
				),
				CTE_BranchCodeBank AS
				(
				    SELECT '31%'	AS [BranchCodePrefix] ,CAST('ABSA' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '33%'	AS [BranchCodePrefix] ,CAST('ABSA' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '42%'	AS [BranchCodePrefix] ,CAST('ABSA' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '50%'	AS [BranchCodePrefix] ,CAST('ABSA' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '51%'	AS [BranchCodePrefix] ,CAST('ABSA' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '52%'	AS [BranchCodePrefix] ,CAST('ABSA' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '63%'	AS [BranchCodePrefix] ,CAST('ABSA' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '430000'	AS [BranchCodePrefix] ,CAST('AFRICAN BANK LIMITED' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '888000'	AS [BranchCodePrefix] ,CAST('BANK ZERO' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '462005'	AS [BranchCodePrefix] ,CAST('BIDVEST BANK LIMITED' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '470010'	AS [BranchCodePrefix] ,CAST('CAPITEC BANK LIMITED' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '586666'	AS [BranchCodePrefix] ,CAST('CHINA CONSTRUCTION BANK' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '350005'	AS [BranchCodePrefix] ,CAST('CITIBANK NA SOUTH AFRICA' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '679000'	AS [BranchCodePrefix] ,CAST('DISCOVERY BANK LIMITED' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '2%'		AS [BranchCodePrefix] ,CAST('FNB/RMB' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '584000'	AS [BranchCodePrefix] ,CAST('GRINDROD BANK LIMITED' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '580105'	AS [BranchCodePrefix] ,CAST('INVESTEC BANK LIMITED' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '45%'	AS [BranchCodePrefix] ,CAST('MERCANTILE BANK LIMITED' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '1%'		AS [BranchCodePrefix] ,CAST('NEDBANK LIMITED' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '760005'	AS [BranchCodePrefix] ,CAST('PERMANENT BNK CBD BUS SUP B992' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '41%'	AS [BranchCodePrefix] ,CAST('S.A. BANK OF ATHENS LTD.' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '683000'	AS [BranchCodePrefix] ,CAST('SASFIN BANK LIMITED' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '460005'	AS [BranchCodePrefix] ,CAST('SOUTH AFRICAN POST OFFICE ' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '0%'		AS [BranchCodePrefix] ,CAST('STANDARD BANK OF S.A. LTD.' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '730%'	AS [BranchCodePrefix] ,CAST('STANDARD CHARTERED PERS LOAN ' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '9%'		AS [BranchCodePrefix] ,CAST('S.A. RESERVE BANK.' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '678910'	AS [BranchCodePrefix] ,CAST('TYME BANK' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '431010'	AS [BranchCodePrefix] ,CAST('UBANK LTD' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '587000' AS [BranchCodePrefix] ,CAST('HSBC BANK' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '570%'   AS [BranchCodePrefix] ,CAST('HBZ BANK LIMITED' AS NVARCHAR) AS [BranchBank]
				)
				SELECT
				    bl.[BuyLeadCode] AS [BuyLeadCode],
				    bl.[LeadCreationDateTime] AS BuyLeadCreationDateTime,
				    bl.[CreatedOn] AS BuyLeadRefCreatedOn,
				    CAST(IIF(bl.[LastAcceptedBy] IS NULL, 0, 1) AS BIT) AS BuyLeadAccepted,
				    bl.[LastAcceptedBy] AS BuyLeadLastAcceptedBy,
				    bl.[LastAcceptedOn] AS BuyLeadLastAcceptedOn,
				    blcs.[Name] AS BuyLeadCompleteStatus,
				    bl.[LastCompletedBy] AS BuyLeadLastCompletedBy,
				    bl.[LastCompletedOn] AS BuyLeadLastCompletedOn,
				    bl.[BankAccountsManuallyVerified] AS BuyLeadBankAccountsManuallyVerified,
				    bl.[BankAccountsManuallyVerifiedBy] AS BuyLeadBankAccountsManuallyVerifiedBy,
				    bl.[BankAccountsManuallyVerifiedOn] AS BuyLeadBankAccountsManuallyVerifiedOn,
				    bl.[DocumentsApproved] AS BuyLeadDocumentsApproved,
				    bl.[DocumentsApprovedBy] AS BuyLeadDocumentsApprovedBy,
				    bl.[DocumentsApprovedOn] AS BuyLeadDocumentsApprovedOn,
				    CAST(blv.[Year] AS NVARCHAR(4)) AS BuyLeadVehicleYear,
				    blv.[Make] AS BuyLeadVehicleMake,
				    blv.[Model] AS BuyLeadVehicleModel,
				    blv.[Variant] AS BuyLeadVehicleVariant,
				    blv.[VinNumber] AS BuyLeadVehicleVinNumber,
				    blv.[RegisterNumber] AS BuyLeadVehicleRegisterNumber,
				    blv.[EngineNumber] AS BuyLeadVehicleEngineNumber,
				    blv.[LicensePlateNumber] AS BuyLeadVehicleLicensePlateNumber,
				    blv.[MmCode] AS BuyLeadVehicleMmCode,
				    CAST(blv.[PurchasePrice] AS DECIMAL(18,2)) AS BuyLeadVehiclePurchasePrice,
				    blv.[PurchaseDateTime] AS BuyLeadVehiclePurchaseDateTime,
				    blc.[Name] AS BuyLeadClientName,
				    blc.[Surname] AS BuyLeadClientSurname,
				    blb.[Name] AS BuyerName,
				    blb.[MobileNumber] AS BuyerMobileNumber,
				    blb.[EmailAddress] AS BuyerEmailAddress,
					blb.BuyerId,
				    n.NatisStatusId AS NatisStatusId,
					blp.ProcessDefinitionExternalId,
				    sic.[AccountNumber] AS CustomerFinancialAccountNumber,
				    sic.[Initials] AS [CustomerInitials],
				    sic.[FirstName] AS [CustomerFirstName],
				    sic.[LastName] AS [CustomerLastName],
				    sic.[BusinessName] AS [CustomerBusinessName],
				    COALESCE(CONCAT(sic.[FirstName], ' ', sic.[LastName]),sic.[BusinessName]) AS [CustomerName],
				    sic.[IdNumber] AS [CustomerIdNumber],
				    sic.[PassportNumber] AS [CustomerPassportNumber],
				    sic.[RegisterNumber] AS [CustomerRegisterNumber],
				    sic.[BusinessRegistrationNumber] AS [CustomerBusinessRegistrationNumber],
				    sic.[CompanyRegistrationNumber] AS [CustomerCompanyRegistrationNumber],
				    COALESCE(sic.[IdNumber],COALESCE(sic.[PassportNumber],COALESCE(sic.[CompanyRegistrationNumber],sic.[BusinessRegistrationNumber]))) AS [CustomerIdentification],
				    CASE WHEN sic.[CustomerType] = 0 THEN 'Unknown'
				         WHEN sic.[CustomerType] = 1 THEN 'Private'
				         WHEN sic.[CustomerType] = 2 THEN 'Dealership'
				         WHEN sic.[CustomerType] = 3 THEN 'Wholesale'
				         WHEN sic.[CustomerType] = 4 THEN 'Supplier'
				         WHEN sic.[CustomerType] = 5 THEN 'Company'
				         WHEN sic.[CustomerType] = 99 THEN 'Prospect'
				        END AS [CustomerType],
				    op.[BuyLeadCompletedTotal],
				    op.[BuyLeadCompletedOutstanding],
				    htd.[ClientBankAccountNumber] AS TransactionBankAccountNumber,
				    htd.[ClientBranchCode] AS TransactionBankAccountBranchCode,
				    bcbt.[BranchBank] AS TransactionBankAccountBranchBank,
				    htd.[ClientName] AS HyphenTransactionClientName,
				    htt.[Code] AS HyphenTransactionTypeCode,
				    htd.[Code2] AS HyphenTransactionReference,
				    baht.[Name] AS TransactionBankAccountHolderType,
				    bat.[Name] AS TransactionBankAccountType,
				    p.[Name] AS [TransactionProvider],
					t.TransactionTypeId AS TransactionTypeId,
				    tt.[Name] AS TransactionType,
				    t.[CurrencyCode] AS TransactionCurrencyCode,
				    CAST(t.[Amount] AS DECIMAL(18,2)) AS TransactionAmount,
				    IIF(ts.[Name] = 'Complete', 'Completed', ts.[Name]) AS TransactionStatus,
				    t.[ProviderReference] AS TransactionProviderReference,
				    t.[TransactionReference],
				    tcr.[Name] AS [TransactionCancellationReason],
				    tcr.[Description] AS [TransactionCancellationReasonDescription],
				    t.[PaymentDate] AS [TransactionPaymentDate],
				    t.[CreatedOn] AS TransactionCreatedOn,
				    t.[CreatedBy] AS TransactionCreatedBy,
				    t.[ModifiedOn] AS TransactionModifiedOn,
				    t.[ModifiedBy] AS TransactionModifiedBy,
				    fh.[Name] AS [TransactionFinanceHouseName]
				FROM
				    [FIN].[BuyLead] bl
				    INNER JOIN [FIN].[BuyLeadVehicle] blv ON
				        bl.[Id] = blv.[BuyLeadId]
				    INNER JOIN [FIN].[BuyLeadClient] blc ON
				        bl.[Id] = blc.[BuyLeadId]
				    INNER JOIN [FIN].[BuyLeadBuyer] blb ON
				        bl.[BuyLeadBuyerId] = blb.[Id]
				    INNER JOIN CTE_OutstandingPayments AS op ON
				        bl.Id = op.Id
					LEFT JOIN FIN.BuyLeadProcess blp ON
							bl.BuyLeadProcessId = blp.Id
				    LEFT JOIN [FIN].[BuyLeadCompleteStatus] blcs ON
				        bl.[BuyLeadCompleteStatusId] = blcs.[Id]
				    LEFT JOIN [FIN].Natis n ON bl.NatisId = n.Id
				    LEFT JOIN [IMS].StockItem si ON
				        bl.BuyLeadCode = si.BuyLeadCode
				    LEFT JOIN [CRM].Customer sic ON
				        si.CrmSellerAccNumber = sic.AccountNumber
				    LEFT JOIN [FIN].[Transaction] t ON
				            t.BuyLeadReference != '' AND
				            t.BuyLeadReference IS NOT NULL AND
				            bl.BuyLeadCode = t.BuyLeadReference
				    LEFT JOIN [FIN].[TransactionType] tt ON
				        t.TransactionTypeId = tt.Id
				    LEFT JOIN [FIN].[TransactionStatus] ts ON
				        t.TransactionStatusId = ts.Id
				    LEFT JOIN [FIN].[Provider] p ON
				        t.ProviderId = p.Id
				    LEFT JOIN [FIN].[TransactionCancellationReason] tcr ON
				        t.TransactionCancellationReasonId = tcr.Id
				    LEFT JOIN [FIN].[HyphenTransactionDetail] htd ON
				            t.ProviderId = 4 AND
				            htd.TransactionId = t.Id
				    LEFT JOIN CTE_BranchCodeBank bcbt ON
				        htd.[ClientBranchCode] LIKE bcbt.[BranchCodePrefix]
				    LEFT JOIN [FIN].[HyphenTransactionType] htt ON
				        htd.TransactionTypeId = htt.Id
				    LEFT JOIN [FIN].[BankAccountType] bat ON
				        bat.Id = htd.ClientBankAccountTypeId
				    LEFT JOIN [FIN].[BankAccountHolderType] baht ON
				        baht.Id = htd.ClientBankAccountHolderTypeId
				    LEFT JOIN [FIN].FinanceHouse fh ON
				        (htd.TransactionTypeId = 7 OR
				         htd.TransactionTypeId = 11) AND
				        RIGHT(htd.ClientBankAccountNumber, 13) = RIGHT('***************'+fh.BankAccountNumber, 13) AND
				        htd.ClientBranchCode = RIGHT('000000'+fh.BankBranchCode, 6)
				-- WHERE bl.BuyLeadCode = 'LQL290306'
				-- WHERE bl.BuyLeadCode = 'LUD151261MIDWH'
				-- WHERE bl.BuyLeadCode = 'LVB240145'
        ");
    }
    
    public static void DropAlterOutstandingPaymentDetailView_FixHbzBank(this MigrationBuilder migrationBuilder)
    {
        //language=azure
        migrationBuilder.Sql(@"
			ALTER VIEW [FIN].[OutstandingPaymentDetailView] AS					
				WITH CTE_OutstandingPayments AS
				(
				    SELECT
				        bl.Id,
				        CAST(ISNULL(SUM(ctr.[Amount] * 1.00), 0.00) AS DECIMAL(18,2)) AS BuyLeadCompletedTotal,
				        CAST(blv.[PurchasePrice] - ISNULL(SUM(ctr.[Amount] * 1.00), 0.00) AS DECIMAL(18,2)) AS BuyLeadCompletedOutstanding
				    FROM
				        [FIN].[BuyLead] bl
				        INNER JOIN [FIN].[BuyLeadVehicle] blv ON
				            bl.[Id] = blv.[BuyLeadId]
				        LEFT JOIN [FIN].[Transaction] ctr ON
				            ctr.[BuyLeadReference] != '' AND
				            ctr.[BuyLeadReference] IS NOT NULL AND
				            bl.[BuyLeadCode] = ctr.[BuyLeadReference] AND
				            ctr.TransactionStatusId = 1 AND
				            ctr.[ProviderId] IN (4, 5)
				    GROUP BY
				        bl.Id,
				        blv.PurchasePrice
				),
				CTE_BranchCodeBank AS
				(
				    SELECT '31%'	AS [BranchCodePrefix] ,CAST('ABSA' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '33%'	AS [BranchCodePrefix] ,CAST('ABSA' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '42%'	AS [BranchCodePrefix] ,CAST('ABSA' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '50%'	AS [BranchCodePrefix] ,CAST('ABSA' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '51%'	AS [BranchCodePrefix] ,CAST('ABSA' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '52%'	AS [BranchCodePrefix] ,CAST('ABSA' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '63%'	AS [BranchCodePrefix] ,CAST('ABSA' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '430000'	AS [BranchCodePrefix] ,CAST('AFRICAN BANK LIMITED' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '888000'	AS [BranchCodePrefix] ,CAST('BANK ZERO' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '462005'	AS [BranchCodePrefix] ,CAST('BIDVEST BANK LIMITED' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '470010'	AS [BranchCodePrefix] ,CAST('CAPITEC BANK LIMITED' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '586666'	AS [BranchCodePrefix] ,CAST('CHINA CONSTRUCTION BANK' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '350005'	AS [BranchCodePrefix] ,CAST('CITIBANK NA SOUTH AFRICA' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '679000'	AS [BranchCodePrefix] ,CAST('DISCOVERY BANK LIMITED' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '2%'		AS [BranchCodePrefix] ,CAST('FNB/RMB' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '584000'	AS [BranchCodePrefix] ,CAST('GRINDROD BANK LIMITED' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '580105'	AS [BranchCodePrefix] ,CAST('INVESTEC BANK LIMITED' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '45%'	AS [BranchCodePrefix] ,CAST('MERCANTILE BANK LIMITED' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '1%'		AS [BranchCodePrefix] ,CAST('NEDBANK LIMITED' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '760005'	AS [BranchCodePrefix] ,CAST('PERMANENT BNK CBD BUS SUP B992' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '41%'	AS [BranchCodePrefix] ,CAST('S.A. BANK OF ATHENS LTD.' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '683000'	AS [BranchCodePrefix] ,CAST('SASFIN BANK LIMITED' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '460005'	AS [BranchCodePrefix] ,CAST('SOUTH AFRICAN POST OFFICE ' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '0%'		AS [BranchCodePrefix] ,CAST('STANDARD BANK OF S.A. LTD.' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '730%'	AS [BranchCodePrefix] ,CAST('STANDARD CHARTERED PERS LOAN ' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '9%'		AS [BranchCodePrefix] ,CAST('S.A. RESERVE BANK.' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '678910'	AS [BranchCodePrefix] ,CAST('TYME BANK' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '431010'	AS [BranchCodePrefix] ,CAST('UBANK LTD' AS NVARCHAR) AS [BranchBank] UNION
				    SELECT '587000' AS [BranchCodePrefix] ,CAST('HSBC BANK' AS NVARCHAR) AS [BranchBank]
				)
				SELECT
				    bl.[BuyLeadCode] AS [BuyLeadCode],
				    bl.[LeadCreationDateTime] AS BuyLeadCreationDateTime,
				    bl.[CreatedOn] AS BuyLeadRefCreatedOn,
				    CAST(IIF(bl.[LastAcceptedBy] IS NULL, 0, 1) AS BIT) AS BuyLeadAccepted,
				    bl.[LastAcceptedBy] AS BuyLeadLastAcceptedBy,
				    bl.[LastAcceptedOn] AS BuyLeadLastAcceptedOn,
				    blcs.[Name] AS BuyLeadCompleteStatus,
				    bl.[LastCompletedBy] AS BuyLeadLastCompletedBy,
				    bl.[LastCompletedOn] AS BuyLeadLastCompletedOn,
				    bl.[BankAccountsManuallyVerified] AS BuyLeadBankAccountsManuallyVerified,
				    bl.[BankAccountsManuallyVerifiedBy] AS BuyLeadBankAccountsManuallyVerifiedBy,
				    bl.[BankAccountsManuallyVerifiedOn] AS BuyLeadBankAccountsManuallyVerifiedOn,
				    bl.[DocumentsApproved] AS BuyLeadDocumentsApproved,
				    bl.[DocumentsApprovedBy] AS BuyLeadDocumentsApprovedBy,
				    bl.[DocumentsApprovedOn] AS BuyLeadDocumentsApprovedOn,
				    CAST(blv.[Year] AS NVARCHAR(4)) AS BuyLeadVehicleYear,
				    blv.[Make] AS BuyLeadVehicleMake,
				    blv.[Model] AS BuyLeadVehicleModel,
				    blv.[Variant] AS BuyLeadVehicleVariant,
				    blv.[VinNumber] AS BuyLeadVehicleVinNumber,
				    blv.[RegisterNumber] AS BuyLeadVehicleRegisterNumber,
				    blv.[EngineNumber] AS BuyLeadVehicleEngineNumber,
				    blv.[LicensePlateNumber] AS BuyLeadVehicleLicensePlateNumber,
				    blv.[MmCode] AS BuyLeadVehicleMmCode,
				    CAST(blv.[PurchasePrice] AS DECIMAL(18,2)) AS BuyLeadVehiclePurchasePrice,
				    blv.[PurchaseDateTime] AS BuyLeadVehiclePurchaseDateTime,
				    blc.[Name] AS BuyLeadClientName,
				    blc.[Surname] AS BuyLeadClientSurname,
				    blb.[Name] AS BuyerName,
				    blb.[MobileNumber] AS BuyerMobileNumber,
				    blb.[EmailAddress] AS BuyerEmailAddress,
					blb.BuyerId,
				    n.NatisStatusId AS NatisStatusId,
					blp.ProcessDefinitionExternalId,
				    sic.[AccountNumber] AS CustomerFinancialAccountNumber,
				    sic.[Initials] AS [CustomerInitials],
				    sic.[FirstName] AS [CustomerFirstName],
				    sic.[LastName] AS [CustomerLastName],
				    sic.[BusinessName] AS [CustomerBusinessName],
				    COALESCE(CONCAT(sic.[FirstName], ' ', sic.[LastName]),sic.[BusinessName]) AS [CustomerName],
				    sic.[IdNumber] AS [CustomerIdNumber],
				    sic.[PassportNumber] AS [CustomerPassportNumber],
				    sic.[RegisterNumber] AS [CustomerRegisterNumber],
				    sic.[BusinessRegistrationNumber] AS [CustomerBusinessRegistrationNumber],
				    sic.[CompanyRegistrationNumber] AS [CustomerCompanyRegistrationNumber],
				    COALESCE(sic.[IdNumber],COALESCE(sic.[PassportNumber],COALESCE(sic.[CompanyRegistrationNumber],sic.[BusinessRegistrationNumber]))) AS [CustomerIdentification],
				    CASE WHEN sic.[CustomerType] = 0 THEN 'Unknown'
				         WHEN sic.[CustomerType] = 1 THEN 'Private'
				         WHEN sic.[CustomerType] = 2 THEN 'Dealership'
				         WHEN sic.[CustomerType] = 3 THEN 'Wholesale'
				         WHEN sic.[CustomerType] = 4 THEN 'Supplier'
				         WHEN sic.[CustomerType] = 5 THEN 'Company'
				         WHEN sic.[CustomerType] = 99 THEN 'Prospect'
				        END AS [CustomerType],
				    op.[BuyLeadCompletedTotal],
				    op.[BuyLeadCompletedOutstanding],
				    htd.[ClientBankAccountNumber] AS TransactionBankAccountNumber,
				    htd.[ClientBranchCode] AS TransactionBankAccountBranchCode,
				    bcbt.[BranchBank] AS TransactionBankAccountBranchBank,
				    htd.[ClientName] AS HyphenTransactionClientName,
				    htt.[Code] AS HyphenTransactionTypeCode,
				    htd.[Code2] AS HyphenTransactionReference,
				    baht.[Name] AS TransactionBankAccountHolderType,
				    bat.[Name] AS TransactionBankAccountType,
				    p.[Name] AS [TransactionProvider],
					t.TransactionTypeId AS TransactionTypeId,
				    tt.[Name] AS TransactionType,
				    t.[CurrencyCode] AS TransactionCurrencyCode,
				    CAST(t.[Amount] AS DECIMAL(18,2)) AS TransactionAmount,
				    IIF(ts.[Name] = 'Complete', 'Completed', ts.[Name]) AS TransactionStatus,
				    t.[ProviderReference] AS TransactionProviderReference,
				    t.[TransactionReference],
				    tcr.[Name] AS [TransactionCancellationReason],
				    tcr.[Description] AS [TransactionCancellationReasonDescription],
				    t.[PaymentDate] AS [TransactionPaymentDate],
				    t.[CreatedOn] AS TransactionCreatedOn,
				    t.[CreatedBy] AS TransactionCreatedBy,
				    t.[ModifiedOn] AS TransactionModifiedOn,
				    t.[ModifiedBy] AS TransactionModifiedBy,
				    fh.[Name] AS [TransactionFinanceHouseName]
				FROM
				    [FIN].[BuyLead] bl
				    INNER JOIN [FIN].[BuyLeadVehicle] blv ON
				        bl.[Id] = blv.[BuyLeadId]
				    INNER JOIN [FIN].[BuyLeadClient] blc ON
				        bl.[Id] = blc.[BuyLeadId]
				    INNER JOIN [FIN].[BuyLeadBuyer] blb ON
				        bl.[BuyLeadBuyerId] = blb.[Id]
				    INNER JOIN CTE_OutstandingPayments AS op ON
				        bl.Id = op.Id
					LEFT JOIN FIN.BuyLeadProcess blp ON
							bl.BuyLeadProcessId = blp.Id
				    LEFT JOIN [FIN].[BuyLeadCompleteStatus] blcs ON
				        bl.[BuyLeadCompleteStatusId] = blcs.[Id]
				    LEFT JOIN [FIN].Natis n ON bl.NatisId = n.Id
				    LEFT JOIN [IMS].StockItem si ON
				        bl.BuyLeadCode = si.BuyLeadCode
				    LEFT JOIN [CRM].Customer sic ON
				        si.CrmSellerAccNumber = sic.AccountNumber
				    LEFT JOIN [FIN].[Transaction] t ON
				            t.BuyLeadReference != '' AND
				            t.BuyLeadReference IS NOT NULL AND
				            bl.BuyLeadCode = t.BuyLeadReference
				    LEFT JOIN [FIN].[TransactionType] tt ON
				        t.TransactionTypeId = tt.Id
				    LEFT JOIN [FIN].[TransactionStatus] ts ON
				        t.TransactionStatusId = ts.Id
				    LEFT JOIN [FIN].[Provider] p ON
				        t.ProviderId = p.Id
				    LEFT JOIN [FIN].[TransactionCancellationReason] tcr ON
				        t.TransactionCancellationReasonId = tcr.Id
				    LEFT JOIN [FIN].[HyphenTransactionDetail] htd ON
				            t.ProviderId = 4 AND
				            htd.TransactionId = t.Id
				    LEFT JOIN CTE_BranchCodeBank bcbt ON
				        htd.[ClientBranchCode] LIKE bcbt.[BranchCodePrefix]
				    LEFT JOIN [FIN].[HyphenTransactionType] htt ON
				        htd.TransactionTypeId = htt.Id
				    LEFT JOIN [FIN].[BankAccountType] bat ON
				        bat.Id = htd.ClientBankAccountTypeId
				    LEFT JOIN [FIN].[BankAccountHolderType] baht ON
				        baht.Id = htd.ClientBankAccountHolderTypeId
				    LEFT JOIN [FIN].FinanceHouse fh ON
				        (htd.TransactionTypeId = 7 OR
				         htd.TransactionTypeId = 11) AND
				        RIGHT(htd.ClientBankAccountNumber, 13) = RIGHT('***************'+fh.BankAccountNumber, 13) AND
				        htd.ClientBranchCode = RIGHT('000000'+fh.BankBranchCode, 6)
				-- WHERE bl.BuyLeadCode = 'LQL290306'
				-- WHERE bl.BuyLeadCode = 'LUD151261MIDWH'
				-- WHERE bl.BuyLeadCode = 'LVB240145'
        ");
    }
}