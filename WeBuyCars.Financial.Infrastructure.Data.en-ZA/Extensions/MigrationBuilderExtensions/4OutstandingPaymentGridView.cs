using Microsoft.EntityFrameworkCore.Migrations;

namespace WeBuyCars.Financial.Infrastructure.Data.en_ZA.Extensions.MigrationBuilderExtensions
{
    public static partial class MigrationBuilderExtensions
    {
        public static void CreateOutstandingPaymentGridView(this MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
				CREATE VIEW [FIN].[OutstandingPaymentGridView] AS
					WITH CTE_OutstandingPayments AS (
						SELECT 
							bl.[BuyLeadCode],
							bl.[LeadCreationDateTime] AS BuyLeadCreationDateTime,
							bl.[CreatedOn] AS BuyLeadRefCreatedOn,
							CAST(IIF(bl.[LastAcceptedBy] IS NULL, 0, 1) AS BIT) AS BuyLeadAccepted,
							bl.[LastAcceptedBy] AS BuyLeadLastAcceptedBy,
							bl.[LastAcceptedOn] AS BuyLeadLastAcceptedOn,
							blcs.[Name] AS BuyLeadCompleteStatus,
							bl.[LastCompletedBy] AS BuyLeadLastCompletedBy,
							bl.[LastCompletedOn] AS BuyLeadLastCompletedOn,
							bl.[BankAccountsManuallyVerified] AS BuyLeadBankAccountsManuallyVerified,
							bl.[BankAccountsManuallyVerifiedBy] AS BuyLeadBankAccountsManuallyVerifiedBy,
							bl.[BankAccountsManuallyVerifiedOn] AS BuyLeadBankAccountsManuallyVerifiedOn,
							CAST(blv.[Year] AS VARCHAR(4)) AS BuyLeadVehicleYear,
							blv.[Make] AS BuyLeadVehicleMake,
							blv.[Model] AS BuyLeadVehicleModel,
							blv.[Variant] AS BuyLeadVehicleVariant,
							blv.[VinNumber] AS BuyLeadVehicleVinNumber,
							blv.[RegisterNumber] AS BuyLeadVehicleRegisterNumber,
							blv.[EngineNumber] AS BuyLeadVehicleEngineNumber,
							blv.[LicensePlateNumber] AS BuyLeadVehicleLicensePlateNumber,
							blv.[MmCode] AS BuyLeadVehicleMmCode,
							CAST(blv.[PurchasePrice] AS DECIMAL(18,2)) AS BuyLeadVehiclePurchasePrice,
							blv.[PurchaseDateTime] AS BuyLeadVehiclePurchaseDateTime,
							blc.[Name] AS BuyLeadClientName,
							blc.[Surname] AS BuyLeadClientSurname,
							blb.[Name] AS BuyerName,
							blb.[MobileNumber] AS BuyerMobileNumber,
							blb.[EmailAddress] AS BuyerEmailAddress,
							CAST(ISNULL(SUM(ctr.[Amount] * 1.00), 0.00) AS DECIMAL(18,2)) AS BuyLeadCompletedTotal,
							CAST(blv.[PurchasePrice] - ISNULL(SUM(ctr.[Amount] * 1.00), 0.00) AS DECIMAL(18,2)) AS BuyLeadCompletedOutstanding
						FROM 
							[FIN].[BuyLead] bl
							INNER JOIN [FIN].[BuyLeadVehicle] blv ON 
								bl.[Id] = blv.[BuyLeadId]
							INNER JOIN [FIN].[BuyLeadClient] blc ON 
								bl.[Id] = blc.[BuyLeadId]
							INNER JOIN [FIN].[BuyLeadBuyer] blb ON 
								bl.[BuyLeadBuyerId] = blb.[Id]
							LEFT JOIN [FIN].[BuyLeadCompleteStatus] blcs ON 
								bl.[BuyLeadCompleteStatusId] = blcs.[Id]
							LEFT JOIN [FIN].[Transaction] ctr ON 
								ctr.[BuyLeadReference] != '' AND 
								ctr.[BuyLeadReference] IS NOT NULL AND 
								bl.[BuyLeadCode] = ctr.[BuyLeadReference] AND 
								ctr.TransactionStatusId = 1 AND 
								ctr.[ProviderId] IN (4, 5)
						GROUP BY 
							bl.[BuyLeadCode],
							bl.[LeadCreationDateTime],
							bl.[CreatedOn],
							bl.[LastAcceptedBy],
							bl.[LastAcceptedOn],
							blcs.[Name],
							bl.[LastCompletedBy],
							bl.[LastCompletedOn],
							bl.[BankAccountsManuallyVerified],
							bl.[BankAccountsManuallyVerifiedBy],
							bl.[BankAccountsManuallyVerifiedOn],
							blv.[Year],
							blv.[Make],
							blv.[Model],
							blv.[Variant],
							blv.[VinNumber],
							blv.[RegisterNumber],
							blv.[EngineNumber],
							blv.[LicensePlateNumber],
							blv.[MmCode],
							blv.[PurchasePrice],
							blv.[PurchaseDateTime],
							blc.[Name],
							blc.[Surname],
							blb.[Name],
							blb.[MobileNumber],
							blb.[EmailAddress]
						--HAVING (ISNULL(SUM(ctr.Amount * 1.00), 0.00) - blv.PurchasePrice) < 0.00
					)
					SELECT DISTINCT
						op.[BuyLeadCode],
						op.[BuyLeadCreationDateTime],
						op.[BuyLeadRefCreatedOn],
						op.[BuyLeadAccepted],
						op.[BuyLeadLastAcceptedBy],
						op.[BuyLeadLastAcceptedOn],
						op.[BuyLeadCompleteStatus],
						op.[BuyLeadLastCompletedBy],
						op.[BuyLeadLastCompletedOn],
						op.[BuyLeadBankAccountsManuallyVerified],
						op.[BuyLeadBankAccountsManuallyVerifiedBy],
						op.[BuyLeadBankAccountsManuallyVerifiedOn],
						op.[BuyLeadVehicleYear],
						op.[BuyLeadVehicleMake],
						op.[BuyLeadVehicleModel],
						op.[BuyLeadVehicleVariant],
						op.[BuyLeadVehicleVinNumber],
						op.[BuyLeadVehicleRegisterNumber],
						op.[BuyLeadVehicleEngineNumber],
						op.[BuyLeadVehicleLicensePlateNumber],
						op.[BuyLeadVehicleMmCode],
						op.[BuyLeadVehiclePurchasePrice],
						op.[BuyLeadVehiclePurchaseDateTime],
						op.[BuyLeadClientName],
						op.[BuyLeadClientSurname],
						op.[BuyerName],
						op.[BuyerMobileNumber],
						op.[BuyerEmailAddress],
							a.[AccountNumber] AS CustomerFinancialAccountNumber,

							COALESCE(CONCAT(c.[FirstName], ' ', c.[LastName]),c.BusinessName) AS [CustomerName],
							COALESCE(c.[IdNumber],COALESCE(c.[PassportNumber],c.[CompanyRegistrationNumber])) AS [CustomerIdentification],
							c.VatNumber AS [CustomerVatNumber],
							c.BusinessRegistrationNumber AS [CustomerBusinessRegistrationNumber],

							--c.[IdNumber] AS CustomerIdNumber,
							--c.[PassportNumber] AS CustomerPassportNumber,
							CASE WHEN c.[CustomerType] = 0 THEN 'Unknown'
								WHEN c.[CustomerType] = 1 THEN 'Private'
								WHEN c.[CustomerType] = 2 THEN 'Dealership'
								WHEN c.[CustomerType] = 3 THEN 'Wholesale'
								WHEN c.[CustomerType] = 4 THEN 'Supplier'
								WHEN c.[CustomerType] = 5 THEN 'Trust'
							END AS CustomerType,
						op.[BuyLeadCompletedTotal],
						op.[BuyLeadCompletedOutstanding]
					FROM 
						CTE_OutstandingPayments op
					INNER JOIN [FIN].[Transaction] t ON 
						t.BuyLeadReference != '' AND 
						t.BuyLeadReference IS NOT NULL AND 
						op.BuyLeadCode = t.BuyLeadReference 
					INNER JOIN [FIN].[Account] a ON 
						t.AccountId = a.Id
					INNER JOIN [CRM].[Customer] c ON 
						a.AccountNumber = c.AccountNumber
			");
        }

        public static void DropOutstandingPaymentGridView(this MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"DROP VIEW [FIN].[OutstandingPaymentGridView]");
        }

    }
}