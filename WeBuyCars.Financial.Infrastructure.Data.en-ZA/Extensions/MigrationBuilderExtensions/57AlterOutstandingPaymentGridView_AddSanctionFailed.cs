using Microsoft.EntityFrameworkCore.Migrations;

namespace WeBuyCars.Financial.Infrastructure.Data.en_ZA.Extensions.MigrationBuilderExtensions;

public static partial class MigrationBuilderExtensions
{
    public static void AlterOutstandingPaymentGridView_AddSanctionFailed(this MigrationBuilder migrationBuilder)
    {
        //language=azure
        migrationBuilder.Sql(@"
            ALTER VIEW [FIN].[OutstandingPaymentGridView] AS
					WITH CTE_OutstandingPayments AS (
					    SELECT
					        bl.Id,
					        CAST(ISNULL(SUM(ctr.[Amount] * 1.00), 0.00) AS DECIMAL(18,2)) AS BuyLeadCompletedTotal,
					        CAST(blv.[PurchasePrice] - ISNULL(SUM(ctr.[Amount] * 1.00), 0.00) AS DECIMAL(18,2)) AS BuyLeadCompletedOutstanding
					    FROM
					        [FIN].[BuyLead] bl
					        INNER JOIN [FIN].[BuyLeadVehicle] blv ON
					            bl.[Id] = blv.[BuyLeadId]
					        LEFT JOIN [FIN].[Transaction] ctr ON
					            ctr.[BuyLeadReference] != '' AND
					            ctr.[BuyLeadReference] IS NOT NULL AND
					            bl.[BuyLeadCode] = ctr.[BuyLeadReference] AND
					            ctr.TransactionStatusId = 1 AND
					            ctr.[ProviderId] IN (4, 5)
					    GROUP BY
					        bl.Id,
					        blv.PurchasePrice
					)
					SELECT DISTINCT
					    CAST(bl.[BuyLeadCode] AS NVARCHAR(40)) AS [BuyLeadCode],
					    bl.[LeadCreationDateTime] AS BuyLeadCreationDateTime,
					    bl.[CreatedOn] AS BuyLeadRefCreatedOn,
						bl.[LastAllocatedTo] AS BuyLeadAllocatedTo,
						bl.[LastAllocatedOn] AS BuyLeadAllocatedOn,
					    CAST(IIF(bl.[LastAcceptedBy] IS NULL, 0, 1) AS BIT) AS BuyLeadAccepted,
					    bl.[LastAcceptedBy] AS BuyLeadLastAcceptedBy,
					    bl.[LastAcceptedOn] AS BuyLeadLastAcceptedOn,
					    blcs.[Name] AS BuyLeadCompleteStatus,
					    bl.[LastCompletedBy] AS BuyLeadLastCompletedBy,
					    bl.[LastCompletedOn] AS BuyLeadLastCompletedOn,
					    bl.[BankAccountsManuallyVerified] AS BuyLeadBankAccountsManuallyVerified,
					    bl.[BankAccountsManuallyVerifiedBy] AS BuyLeadBankAccountsManuallyVerifiedBy,
					    bl.[BankAccountsManuallyVerifiedOn] AS BuyLeadBankAccountsManuallyVerifiedOn,
					    bl.[DocumentsApproved] AS BuyLeadDocumentsApproved,
					    bl.[DocumentsApprovedBy] AS BuyLeadDocumentsApprovedBy,
					    bl.[DocumentsApprovedOn] AS BuyLeadDocumentsApprovedOn,
					    bl.UnreadQuestions AS BuyLeadUnreadQuestions,
					    CAST(blv.[Year] AS NVARCHAR(4)) AS BuyLeadVehicleYear,
					    blv.[Make] AS BuyLeadVehicleMake,
					    blv.[Model] AS BuyLeadVehicleModel,
					    blv.[Variant] AS BuyLeadVehicleVariant,
					    blv.[VinNumber] AS BuyLeadVehicleVinNumber,
					    blv.[RegisterNumber] AS BuyLeadVehicleRegisterNumber,
					    blv.[EngineNumber] AS BuyLeadVehicleEngineNumber,
					    blv.[LicensePlateNumber] AS BuyLeadVehicleLicensePlateNumber,
					    blv.[MmCode] AS BuyLeadVehicleMmCode,
                        blv.[IsTradeIn] As BuyLeadVehicleIsTradeIn,
					    CAST(blv.[PurchasePrice] AS DECIMAL(18,2)) AS BuyLeadVehiclePurchasePrice,
					    blv.[PurchaseDateTime] AS BuyLeadVehiclePurchaseDateTime,
					    blc.[Name] AS BuyLeadClientName,
					    blc.[Surname] AS BuyLeadClientSurname,
                        blc.SanctionCheckFailed AS BuyLeadClientSanctionCheckFailed,
					    blb.[Name] AS BuyerName,
					    blb.[MobileNumber] AS BuyerMobileNumber,
					    blb.[EmailAddress] AS BuyerEmailAddress,
						blb.BuyerId,
					    ns.Status AS NatisStatus,
						blp.ProcessDefinitionExternalId,
					    sic.[AccountNumber] AS [CustomerFinancialAccountNumber],
					    sic.[Initials] AS [CustomerInitials],
					    sic.[FirstName] AS [CustomerFirstName],
					    sic.[LastName] AS [CustomerLastName],
					    sic.[BusinessName] AS [CustomerBusinessName],
					    COALESCE(CONCAT(sic.[FirstName], ' ', sic.[LastName]),sic.[BusinessName]) AS [CustomerName],
					    sic.[IdNumber] AS [CustomerIdNumber],
					    sic.[PassportNumber] AS [CustomerPassportNumber],
					    sic.[RegisterNumber] AS [CustomerRegisterNumber],
					    sic.[BusinessRegistrationNumber] AS [CustomerBusinessRegistrationNumber],
					    sic.[CompanyRegistrationNumber] AS [CustomerCompanyRegistrationNumber],
					    COALESCE(sic.[IdNumber],COALESCE(sic.[PassportNumber],COALESCE(sic.[CompanyRegistrationNumber],sic.[BusinessRegistrationNumber]))) AS [CustomerIdentification],
					    CASE WHEN sic.[CustomerType] = 0 THEN 'Unknown'
					        WHEN sic.[CustomerType] = 1 THEN 'Private'
					        WHEN sic.[CustomerType] = 2 THEN 'Dealership'
					        WHEN sic.[CustomerType] = 3 THEN 'Wholesale'
					        WHEN sic.[CustomerType] = 4 THEN 'Supplier'
					        WHEN sic.[CustomerType] = 5 THEN 'Company'
					        WHEN sic.[CustomerType] = 99 THEN 'Prospect'
					    END AS [CustomerType],
					    op.[BuyLeadCompletedTotal],
					    op.[BuyLeadCompletedOutstanding]
					FROM
					    [FIN].[BuyLead] bl
					        INNER JOIN [FIN].[BuyLeadVehicle] blv ON
					            bl.[Id] = blv.[BuyLeadId]
					        INNER JOIN [FIN].[BuyLeadClient] blc ON
					            bl.[Id] = blc.[BuyLeadId]
					        INNER JOIN [FIN].[BuyLeadBuyer] blb ON
					            bl.[BuyLeadBuyerId] = blb.[Id]
					        INNER JOIN CTE_OutstandingPayments AS op ON
					            bl.Id = op.Id
							LEFT JOIN FIN.BuyLeadProcess blp ON
								bl.BuyLeadProcessId = blp.Id
					        LEFT JOIN [FIN].[BuyLeadCompleteStatus] blcs ON
					            bl.[BuyLeadCompleteStatusId] = blcs.[Id]
					        LEFT JOIN [FIN].Natis n ON bl.NatisId = n.Id
					            LEFT JOIN [FIN].NatisStatus ns ON n.NatisStatusId = ns.Id
					        LEFT JOIN [IMS].StockItem si ON
					            bl.BuyLeadCode = si.BuyLeadCode
					        LEFT JOIN [CRM].Customer sic ON
					            si.CrmSellerAccNumber = sic.AccountNumber
        ");
    }
    
    public static void DropAlterOutstandingPaymentGridView_AddSanctionFailed(this MigrationBuilder migrationBuilder)
    {
        //language=azure
        migrationBuilder.Sql(@"
            ALTER VIEW [FIN].[OutstandingPaymentGridView] AS
					WITH CTE_OutstandingPayments AS (
					    SELECT
					        bl.Id,
					        CAST(ISNULL(SUM(ctr.[Amount] * 1.00), 0.00) AS DECIMAL(18,2)) AS BuyLeadCompletedTotal,
					        CAST(blv.[PurchasePrice] - ISNULL(SUM(ctr.[Amount] * 1.00), 0.00) AS DECIMAL(18,2)) AS BuyLeadCompletedOutstanding
					    FROM
					        [FIN].[BuyLead] bl
					        INNER JOIN [FIN].[BuyLeadVehicle] blv ON
					            bl.[Id] = blv.[BuyLeadId]
					        LEFT JOIN [FIN].[Transaction] ctr ON
					            ctr.[BuyLeadReference] != '' AND
					            ctr.[BuyLeadReference] IS NOT NULL AND
					            bl.[BuyLeadCode] = ctr.[BuyLeadReference] AND
					            ctr.TransactionStatusId = 1 AND
					            ctr.[ProviderId] IN (4, 5)
					    GROUP BY
					        bl.Id,
					        blv.PurchasePrice
					)
					SELECT DISTINCT
					    CAST(bl.[BuyLeadCode] AS NVARCHAR(40)) AS [BuyLeadCode],
					    bl.[LeadCreationDateTime] AS BuyLeadCreationDateTime,
					    bl.[CreatedOn] AS BuyLeadRefCreatedOn,
						bl.[LastAllocatedTo] AS BuyLeadAllocatedTo,
						bl.[LastAllocatedOn] AS BuyLeadAllocatedOn,
					    CAST(IIF(bl.[LastAcceptedBy] IS NULL, 0, 1) AS BIT) AS BuyLeadAccepted,
					    bl.[LastAcceptedBy] AS BuyLeadLastAcceptedBy,
					    bl.[LastAcceptedOn] AS BuyLeadLastAcceptedOn,
					    blcs.[Name] AS BuyLeadCompleteStatus,
					    bl.[LastCompletedBy] AS BuyLeadLastCompletedBy,
					    bl.[LastCompletedOn] AS BuyLeadLastCompletedOn,
					    bl.[BankAccountsManuallyVerified] AS BuyLeadBankAccountsManuallyVerified,
					    bl.[BankAccountsManuallyVerifiedBy] AS BuyLeadBankAccountsManuallyVerifiedBy,
					    bl.[BankAccountsManuallyVerifiedOn] AS BuyLeadBankAccountsManuallyVerifiedOn,
					    bl.[DocumentsApproved] AS BuyLeadDocumentsApproved,
					    bl.[DocumentsApprovedBy] AS BuyLeadDocumentsApprovedBy,
					    bl.[DocumentsApprovedOn] AS BuyLeadDocumentsApprovedOn,
					    bl.UnreadQuestions AS BuyLeadUnreadQuestions,
					    CAST(blv.[Year] AS NVARCHAR(4)) AS BuyLeadVehicleYear,
					    blv.[Make] AS BuyLeadVehicleMake,
					    blv.[Model] AS BuyLeadVehicleModel,
					    blv.[Variant] AS BuyLeadVehicleVariant,
					    blv.[VinNumber] AS BuyLeadVehicleVinNumber,
					    blv.[RegisterNumber] AS BuyLeadVehicleRegisterNumber,
					    blv.[EngineNumber] AS BuyLeadVehicleEngineNumber,
					    blv.[LicensePlateNumber] AS BuyLeadVehicleLicensePlateNumber,
					    blv.[MmCode] AS BuyLeadVehicleMmCode,
                        blv.[IsTradeIn] As BuyLeadVehicleIsTradeIn,
					    CAST(blv.[PurchasePrice] AS DECIMAL(18,2)) AS BuyLeadVehiclePurchasePrice,
					    blv.[PurchaseDateTime] AS BuyLeadVehiclePurchaseDateTime,
					    blc.[Name] AS BuyLeadClientName,
					    blc.[Surname] AS BuyLeadClientSurname,
					    blb.[Name] AS BuyerName,
					    blb.[MobileNumber] AS BuyerMobileNumber,
					    blb.[EmailAddress] AS BuyerEmailAddress,
						blb.BuyerId,
					    ns.Status AS NatisStatus,
						blp.ProcessDefinitionExternalId,
					    sic.[AccountNumber] AS [CustomerFinancialAccountNumber],
					    sic.[Initials] AS [CustomerInitials],
					    sic.[FirstName] AS [CustomerFirstName],
					    sic.[LastName] AS [CustomerLastName],
					    sic.[BusinessName] AS [CustomerBusinessName],
					    COALESCE(CONCAT(sic.[FirstName], ' ', sic.[LastName]),sic.[BusinessName]) AS [CustomerName],
					    sic.[IdNumber] AS [CustomerIdNumber],
					    sic.[PassportNumber] AS [CustomerPassportNumber],
					    sic.[RegisterNumber] AS [CustomerRegisterNumber],
					    sic.[BusinessRegistrationNumber] AS [CustomerBusinessRegistrationNumber],
					    sic.[CompanyRegistrationNumber] AS [CustomerCompanyRegistrationNumber],
					    COALESCE(sic.[IdNumber],COALESCE(sic.[PassportNumber],COALESCE(sic.[CompanyRegistrationNumber],sic.[BusinessRegistrationNumber]))) AS [CustomerIdentification],
					    CASE WHEN sic.[CustomerType] = 0 THEN 'Unknown'
					        WHEN sic.[CustomerType] = 1 THEN 'Private'
					        WHEN sic.[CustomerType] = 2 THEN 'Dealership'
					        WHEN sic.[CustomerType] = 3 THEN 'Wholesale'
					        WHEN sic.[CustomerType] = 4 THEN 'Supplier'
					        WHEN sic.[CustomerType] = 5 THEN 'Company'
					        WHEN sic.[CustomerType] = 99 THEN 'Prospect'
					    END AS [CustomerType],
					    op.[BuyLeadCompletedTotal],
					    op.[BuyLeadCompletedOutstanding]
					FROM
					    [FIN].[BuyLead] bl
					        INNER JOIN [FIN].[BuyLeadVehicle] blv ON
					            bl.[Id] = blv.[BuyLeadId]
					        INNER JOIN [FIN].[BuyLeadClient] blc ON
					            bl.[Id] = blc.[BuyLeadId]
					        INNER JOIN [FIN].[BuyLeadBuyer] blb ON
					            bl.[BuyLeadBuyerId] = blb.[Id]
					        INNER JOIN CTE_OutstandingPayments AS op ON
					            bl.Id = op.Id
							LEFT JOIN FIN.BuyLeadProcess blp ON
								bl.BuyLeadProcessId = blp.Id
					        LEFT JOIN [FIN].[BuyLeadCompleteStatus] blcs ON
					            bl.[BuyLeadCompleteStatusId] = blcs.[Id]
					        LEFT JOIN [FIN].Natis n ON bl.NatisId = n.Id
					            LEFT JOIN [FIN].NatisStatus ns ON n.NatisStatusId = ns.Id
					        LEFT JOIN [IMS].StockItem si ON
					            bl.BuyLeadCode = si.BuyLeadCode
					        LEFT JOIN [CRM].Customer sic ON
					            si.CrmSellerAccNumber = sic.AccountNumber
        ");
    }
}