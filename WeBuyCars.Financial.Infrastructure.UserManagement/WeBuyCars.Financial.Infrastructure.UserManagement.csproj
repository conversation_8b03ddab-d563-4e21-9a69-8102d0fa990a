<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AutoMapper" />
        <PackageReference Include="Microsoft.Extensions.Http.Polly" />
        <PackageReference Include="Polly" />
        <PackageReference Include="Polly.Contrib.WaitAndRetry" />
        <PackageReference Include="Refit" />
        <PackageReference Include="Refit.HttpClientFactory" />
        
        <PackageReference Include="WeBuyCars.Core" />
        
        <PackageReference Include="WeBuyCars.Core.Infrastructure.ActiveDirectory.Authentication" />
    </ItemGroup>
    
</Project>
