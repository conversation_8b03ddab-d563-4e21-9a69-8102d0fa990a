using WeBuyCars.Financial.Infrastructure.Messaging.Contracts.Interfaces;

namespace Financial.Contracts;

public class BLMSLeadStatusUpdatedMessage : IMessageEvent
{
    public LeadStatusValuesDto OriginalValues { get; set; }
    public LeadStatusValuesDto UpdatedValues { get; set; }
}

public sealed class LeadStatusValuesDto
{
    public string BuyLeadCode { get; set; }
    public string LeadStatusCode { get; set; }
}