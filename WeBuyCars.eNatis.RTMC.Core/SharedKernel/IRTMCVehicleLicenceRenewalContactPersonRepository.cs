using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Core.SharedKernel
{
    public interface IRTMCVehicleLicenceRenewalContactPersonRepository : IRepository
    {
        /// <summary>
        /// Add a new vehicle license renewal contact person record
        /// </summary>
        /// <param name="entity">The contact person entity to add</param>
        /// <returns>The added entity</returns>
        RTMCVehicleLicenceRenewalContactPerson AddRTMCVehicleLicenseRenewalContactPerson(RTMCVehicleLicenceRenewalContactPerson entity);

        /// <summary>
        /// Get vehicle license renewal contact persons matching the specified predicate
        /// </summary>
        /// <param name="predicate">The filter predicate</param>
        /// <returns>List of matching contact persons</returns>
        Task<List<RTMCVehicleLicenceRenewalContactPerson>> Where(Expression<Func<RTMCVehicleLicenceRenewalContactPerson, bool>> predicate);

        /// <summary>
        /// Get the first vehicle license renewal contact person matching the specified predicate
        /// </summary>
        /// <param name="predicate">The filter predicate</param>
        /// <returns>The first matching contact person or null</returns>
        Task<RTMCVehicleLicenceRenewalContactPerson> FirstOrDefaultAsync(Expression<Func<RTMCVehicleLicenceRenewalContactPerson, bool>> predicate);
    }
}
