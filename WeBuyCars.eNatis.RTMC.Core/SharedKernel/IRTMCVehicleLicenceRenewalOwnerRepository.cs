using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Core.SharedKernel
{
    public interface IRTMCVehicleLicenceRenewalOwnerRepository : IRepository
    {
        /// <summary>
        /// Add a new vehicle license renewal owner record
        /// </summary>
        /// <param name="entity">The owner entity to add</param>
        /// <returns>The added entity</returns>
        RTMCVehicleLicenceRenewalOwner AddRTMCVehicleLicenseRenewalOwner(RTMCVehicleLicenceRenewalOwner entity);

        /// <summary>
        /// Get vehicle license renewal owners matching the specified predicate
        /// </summary>
        /// <param name="predicate">The filter predicate</param>
        /// <returns>List of matching owners</returns>
        Task<List<RTMCVehicleLicenceRenewalOwner>> Where(Expression<Func<RTMCVehicleLicenceRenewalOwner, bool>> predicate);

        /// <summary>
        /// Get the first vehicle license renewal owner matching the specified predicate
        /// </summary>
        /// <param name="predicate">The filter predicate</param>
        /// <returns>The first matching owner or null</returns>
        Task<RTMCVehicleLicenceRenewalOwner> FirstOrDefaultAsync(Expression<Func<RTMCVehicleLicenceRenewalOwner, bool>> predicate);

        /// <summary>
        /// Get owner with related data by renewal ID
        /// </summary>
        /// <param name="renewalId">The renewal ID</param>
        /// <returns>The owner with related data</returns>
        Task<RTMCVehicleLicenceRenewalOwner> GetWithRelatedDataByRenewalIdAsync(long renewalId);
    }
}
