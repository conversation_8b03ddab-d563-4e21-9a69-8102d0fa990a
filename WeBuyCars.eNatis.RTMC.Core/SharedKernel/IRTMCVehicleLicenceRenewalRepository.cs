using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Core.SharedKernel
{
    public interface IRTMCVehicleLicenceRenewalRepository : IRepository
    {
        /// <summary>
        /// Add a new vehicle license renewal record
        /// </summary>
        /// <param name="entity">The renewal entity to add</param>
        /// <returns>The added entity</returns>
        RTMCVehicleLicenceRenewal AddRTMCVehicleLicenceRenewal(RTMCVehicleLicenceRenewal entity);

        /// <summary>
        /// Get vehicle license renewals matching the specified predicate
        /// </summary>
        /// <param name="predicate">The filter predicate</param>
        /// <returns>List of matching renewals</returns>
        Task<List<RTMCVehicleLicenceRenewal>> Where(Expression<Func<RTMCVehicleLicenceRenewal, bool>> predicate);

        /// <summary>
        /// Get the first vehicle license renewal matching the specified predicate
        /// </summary>
        /// <param name="predicate">The filter predicate</param>
        /// <returns>The first matching renewal or null</returns>
        Task<RTMCVehicleLicenceRenewal> FirstOrDefaultAsync(Expression<Func<RTMCVehicleLicenceRenewal, bool>> predicate);

        /// <summary>
        /// Get vehicle license renewal by reference number
        /// </summary>
        /// <param name="referenceNumber">The reference number</param>
        /// <returns>The renewal record or null</returns>
        Task<RTMCVehicleLicenceRenewal> GetByReferenceNumberAsync(string referenceNumber);

        /// <summary>
        /// Get vehicle license renewal by request reference
        /// </summary>
        /// <param name="requestReference">The request reference GUID</param>
        /// <returns>The renewal record or null</returns>
        Task<RTMCVehicleLicenceRenewal> GetByRequestReferenceAsync(Guid requestReference);

        /// <summary>
        /// Get vehicle license renewals by business registration number
        /// </summary>
        /// <param name="businessRegistrationNumber">The business registration number</param>
        /// <returns>List of renewals for the business</returns>
        Task<List<RTMCVehicleLicenceRenewal>> GetByBusinessRegistrationNumberAsync(string businessRegistrationNumber);

        /// <summary>
        /// Get vehicle license renewal with all related entities (Owner, Vehicles, etc.)
        /// </summary>
        /// <param name="id">The renewal ID</param>
        /// <returns>The renewal with all related data</returns>
        Task<RTMCVehicleLicenceRenewal> GetWithRelatedDataAsync(long id);

        /// <summary>
        /// Get vehicle license renewal with all related entities by reference number
        /// </summary>
        /// <param name="referenceNumber">The reference number</param>
        /// <returns>The renewal with all related data</returns>
        Task<RTMCVehicleLicenceRenewal> GetWithRelatedDataByReferenceAsync(string referenceNumber);
    }
}
