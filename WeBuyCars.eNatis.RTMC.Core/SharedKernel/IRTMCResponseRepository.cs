using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Core.SharedKernel
{
    public interface IRTMCResponseRepository : IRepository
    {
        RTMCResponse AddRTMCResponse(RTMCResponse entity);

        Task<List<RTMCResponse>> Where(Expression<Func<RTMCResponse, bool>> predicate);

        Task<RTMCResponse> FirstOrDefaultAsync(Expression<Func<RTMCResponse, bool>> predicate);

        Task<RTMCResponse> LastOrDefaultAsync(Expression<Func<RTMCResponse, bool>> predicate);

    }
}