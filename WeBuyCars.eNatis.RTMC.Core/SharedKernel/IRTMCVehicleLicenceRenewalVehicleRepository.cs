using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Core.SharedKernel
{
    public interface IRTMCVehicleLicenceRenewalVehicleRepository : IRepository
    {
        /// <summary>
        /// Add a new vehicle license renewal vehicle record
        /// </summary>
        /// <param name="entity">The vehicle entity to add</param>
        /// <returns>The added entity</returns>
        RTMCVehicleLicenceRenewalVehicle AddRTMCVehicleLicenceRenewalVehicle(RTMCVehicleLicenceRenewalVehicle entity);

        /// <summary>
        /// Add multiple vehicle license renewal vehicle records
        /// </summary>
        /// <param name="entities">The vehicle entities to add</param>
        /// <returns>True if successful</returns>
        bool AddRTMCVehicleLicenceRenewalVehicleRange(List<RTMCVehicleLicenceRenewalVehicle> entities);

        /// <summary>
        /// Update an existing vehicle license renewal vehicle record
        /// </summary>
        /// <param name="entity">The vehicle entity to update</param>
        /// <returns>The updated entity</returns>
        RTMCVehicleLicenceRenewalVehicle UpdateRTMCVehicleLicenceRenewalVehicle(RTMCVehicleLicenceRenewalVehicle entity);

        /// <summary>
        /// Get vehicle license renewal vehicles matching the specified predicate
        /// </summary>
        /// <param name="predicate">The filter predicate</param>
        /// <returns>List of matching vehicles</returns>
        Task<List<RTMCVehicleLicenceRenewalVehicle>> Where(Expression<Func<RTMCVehicleLicenceRenewalVehicle, bool>> predicate);

        /// <summary>
        /// Get the first vehicle license renewal vehicle matching the specified predicate
        /// </summary>
        /// <param name="predicate">The filter predicate</param>
        /// <returns>The first matching vehicle or null</returns>
        Task<RTMCVehicleLicenceRenewalVehicle> FirstOrDefaultAsync(Expression<Func<RTMCVehicleLicenceRenewalVehicle, bool>> predicate);

        /// <summary>
        /// Get vehicles by renewal ID
        /// </summary>
        /// <param name="renewalId">The renewal ID</param>
        /// <returns>List of vehicles for the renewal</returns>
        Task<List<RTMCVehicleLicenceRenewalVehicle>> GetByRenewalIdAsync(long renewalId);

        /// <summary>
        /// Get vehicles by renewal ID and status
        /// </summary>
        /// <param name="renewalId">The renewal ID</param>
        /// <param name="status">The vehicle status</param>
        /// <returns>List of vehicles for the renewal with the specified status</returns>
        Task<List<RTMCVehicleLicenceRenewalVehicle>> GetByRenewalIdAndStatusAsync(long renewalId, string status);
    }
}
