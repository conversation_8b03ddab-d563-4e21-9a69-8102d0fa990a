using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Core.SharedKernel
{
    public interface IRTMCVehicleLicenceRenewalAddressRepository : IRepository
    {
        /// <summary>
        /// Add a new vehicle license renewal address record
        /// </summary>
        /// <param name="entity">The address entity to add</param>
        /// <returns>The added entity</returns>
        RTMCVehicleLicenceRenewalAddress AddRTMCVehicleLicenceRenewalAddress(RTMCVehicleLicenceRenewalAddress entity);

        /// <summary>
        /// Get vehicle license renewal addresses matching the specified predicate
        /// </summary>
        /// <param name="predicate">The filter predicate</param>
        /// <returns>List of matching addresses</returns>
        Task<List<RTMCVehicleLicenceRenewalAddress>> Where(Expression<Func<RTMCVehicleLicenceRenewalAddress, bool>> predicate);

        /// <summary>
        /// Get the first vehicle license renewal address matching the specified predicate
        /// </summary>
        /// <param name="predicate">The filter predicate</param>
        /// <returns>The first matching address or null</returns>
        Task<RTMCVehicleLicenceRenewalAddress> FirstOrDefaultAsync(Expression<Func<RTMCVehicleLicenceRenewalAddress, bool>> predicate);
    }
}
