using WeBuyCars.eNatis.RTMC.Core.SharedKernel;

namespace WeBuyCars.eNatis.RTMC.Core.Entities
{
    public sealed class RTMCVehicleLicenceRenewalAddress : Entity
    {
        #region Properties

        /// <summary>
        /// Foreign key to the owner record
        /// </summary>
        public long VehicleLicenseRenewalOwnerId { get; set; }

        /// <summary>
        /// Type of address AddressTypeEnum)
        /// </summary>
        public string AddressType { get; set; }

        /// <summary>
        /// Address line 1 (street address)
        /// </summary>
        public string Address1 { get; set; }

        /// <summary>
        /// Address line 2 (apartment, suite, etc.)
        /// </summary>
        public string Address2 { get; set; }

        /// <summary>
        /// Address line 3 (additional address info)
        /// </summary>
        public string Address3 { get; set; }

        /// <summary>
        /// Address line 4 (suburb/area)
        /// </summary>
        public string Address4 { get; set; }

        /// <summary>
        /// Address line 5 (city/town)
        /// </summary>
        public string Address5 { get; set; }

        /// <summary>
        /// Postal code
        /// </summary>
        public string PostalCode { get; set; }

        #endregion

        #region Navigation Properties

        /// <summary>
        /// Reference to the owner record
        /// </summary>
        public RTMCVehicleLicenceRenewalOwner Owner { get; set; }

        #endregion
    }

    public static class RTMCVehicleLicenceRenewalAddressConstants
    {
        public const int AddressTypeMaxLength = 50;
        public const int Address1MaxLength = 200;
        public const int Address2MaxLength = 200;
        public const int Address3MaxLength = 200;
        public const int Address4MaxLength = 200;
        public const int Address5MaxLength = 200;
        public const int PostalCodeMaxLength = 10;
    }
}
