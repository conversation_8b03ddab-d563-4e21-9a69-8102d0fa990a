using WeBuyCars.eNatis.RTMC.Core.SharedKernel;

namespace WeBuyCars.eNatis.RTMC.Core.Entities
{
    public sealed class RTMCVehicleLicenceRenewalContactPerson : Entity
    {
        #region Properties

        /// <summary>
        /// Foreign key to the owner record
        /// </summary>
        public long VehicleLicenceRenewalOwnerId { get; set; }

        /// <summary>
        /// Contact person's full name
        /// </summary>
        public string ContactName { get; set; }

        /// <summary>
        /// Primary contact number
        /// </summary>
        public string ContactNumber { get; set; }

        /// <summary>
        /// Alternative contact number
        /// </summary>
        public string AlternativeContactNumber { get; set; }

        #endregion

        #region Navigation Properties

        /// <summary>
        /// Reference to the owner record
        /// </summary>
        public RTMCVehicleLicenceRenewalOwner Owner { get; set; }

        #endregion
    }

    public class RTMCVehicleLicenceRenewalContactPersonConstants
    {
        public const int ContactNameMaxLength = 200;
        public const int ContactNumberMaxLength = 20;
    }
}
