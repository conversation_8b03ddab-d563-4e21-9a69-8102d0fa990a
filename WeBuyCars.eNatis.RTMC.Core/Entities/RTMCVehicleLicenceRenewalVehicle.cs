using WeBuyCars.eNatis.RTMC.Core.Enumerations;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;

namespace WeBuyCars.eNatis.RTMC.Core.Entities
{
    public sealed class RTMCVehicleLicenceRenewalVehicle : Entity
    {
        #region Properties

        /// <summary>
        /// Foreign key to the main renewal record
        /// </summary>
        public long VehicleLicenceRenewalId { get; set; }

        /// <summary>
        /// VIN or Chassis number of the vehicle
        /// </summary>
        public string VinOrChassis { get; set; }

        /// <summary>
        /// License number of the vehicle
        /// </summary>
        public string LicenseNumber { get; set; }

        /// <summary>
        /// Register number of the vehicle
        /// </summary>
        public string RegisterNumber { get; set; }

        /// <summary>
        /// Engine number of the vehicle
        /// </summary>
        public string EngineNumber { get; set; }

        /// <summary>
        /// Current license expiry date
        /// </summary>
        public string LicenseExpiryDate { get; set; }

        /// <summary>
        /// Roadworthy status
        /// </summary>
        public string Roadworthy { get; set; }

        /// <summary>
        /// Vehicle make
        /// </summary>
        public string Make { get; set; }

        /// <summary>
        /// Vehicle model
        /// </summary>
        public string Model { get; set; }

        /// <summary>
        /// Series name
        /// </summary>
        public string SeriesName { get; set; }

        /// <summary>
        /// Vehicle colour
        /// </summary>
        public string Colour { get; set; }

        /// <summary>
        /// Vehicle tare weight
        /// </summary>
        public int? Tare { get; set; }

        /// <summary>
        /// Renewal amount for this specific vehicle
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// Courier fees for this vehicle
        /// </summary>
        public decimal? CourierFees { get; set; }

        /// <summary>
        /// License fees for this vehicle
        /// </summary>
        public decimal? LicenseFees { get; set; }

        /// <summary>
        /// Transaction fees for this vehicle
        /// </summary>
        public decimal? TransactionFees { get; set; }

        /// <summary>
        /// Convenience fees for this vehicle
        /// </summary>
        public decimal? ConvenienceFees { get; set; }

        /// <summary>
        /// Minimum amount due for this vehicle
        /// </summary>
        public decimal? MinAmountDue { get; set; }

        /// <summary>
        /// Maximum amount due for this vehicle
        /// </summary>
        public decimal? MaxAmountDue { get; set; }

        /// <summary>
        /// Outstanding debt for offences fees
        /// </summary>
        public decimal? OutstandingDebtForOffencesFees { get; set; }

        /// <summary>
        /// Pre fees
        /// </summary>
        public decimal? PreFees { get; set; }

        /// <summary>
        /// Post fees
        /// </summary>
        public decimal? PostFees { get; set; }

        /// <summary>
        /// Status of this vehicle's renewal
        /// </summary>
        public TransactionStatusEnum Status { get; set; }

        #endregion

        #region Navigation Properties

        /// <summary>
        /// Reference to the main renewal record
        /// </summary>
        public RTMCVehicleLicenceRenewal VehicleLicenceRenewal { get; set; }

        #endregion
    }
}
