using System;
using System.Collections.Generic;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;

namespace WeBuyCars.eNatis.RTMC.Core.Entities
{
    public sealed class RTMCVehicleLicenceRenewal : Entity
    {
        #region Properties

        /// <summary>
        /// Reference number for the renewal transaction
        /// </summary>
        public string ReferenceNumber { get; set; }

        /// <summary>
        /// Payment redirect URL from NATIS
        /// </summary>
        public string PaymentRedirectUrl { get; set; }

        /// <summary>
        /// Total renewal amount for all vehicles
        /// </summary>
        public decimal TotalRenewalAmount { get; set; }

        /// <summary>
        /// Total delivery amount
        /// </summary>
        public decimal TotalDeliveryAmount { get; set; }

        /// <summary>
        /// Total paid amount
        /// </summary>
        public decimal TotalPaidAmount { get; set; }

        /// <summary>
        /// Business registration number
        /// </summary>
        public string BusinessRegistrationNumber { get; set; }

        /// <summary>
        /// Status of the renewal (Initiated, Completed, Failed, etc.)
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// Request reference for tracking
        /// </summary>
        public Guid RequestReference { get; set; }

        /// <summary>
        /// Response reference for tracking
        /// </summary>
        public Guid? ResponseReference { get; set; }

        /// <summary>
        /// User who initiated the renewal
        /// </summary>
        public string User { get; set; }

        /// <summary>
        /// Workstation where renewal was initiated
        /// </summary>
        public string WorkStation { get; set; }

        /// <summary>
        /// Locality information
        /// </summary>
        public string Locality { get; set; }

        /// <summary>
        /// Network address
        /// </summary>
        public string NetworkAddress { get; set; }

        /// <summary>
        /// Date when renewal was initiated
        /// </summary>
        public DateTimeOffset InitiatedDate { get; set; }

        /// <summary>
        /// Date when renewal was completed (if applicable)
        /// </summary>
        public DateTimeOffset? CompletedDate { get; set; }

        /// <summary>
        /// Error message if renewal failed
        /// </summary>
        public string ErrorMessage { get; set; }

        #endregion

        #region Navigation Properties

        /// <summary>
        /// Owner information for this renewal
        /// </summary>
        public RTMCVehicleLicenceRenewalOwner Owner { get; set; }

        /// <summary>
        /// Vehicles included in this renewal
        /// </summary>
        public ICollection<RTMCVehicleLicenceRenewalVehicle> Vehicles { get; set; } = new List<RTMCVehicleLicenceRenewalVehicle>();

        #endregion
    }

    public static class RTMCVehicleLicenceRenewalConstants
    {
        public const int ReferenceNumberMaxLength = 50;
        public const int PaymentRedirectUrlMaxLength = 500;
        public const int BusinessRegistrationNumberMaxLength = 50;
        public const int StatusMaxLength = 50;
        public const int UserMaxLength = 50;
        public const int WorkStationMaxLength = 50;
        public const int LocalityMaxLength = 50;
        public const int NetworkAddressMaxLength = 50;
    }
}
