using WeBuyCars.eNatis.RTMC.Core.SharedKernel;

namespace WeBuyCars.eNatis.RTMC.Core.Entities
{
    public sealed class RTMCVehicleLicenceRenewalOwner : Entity
    {
        #region Properties

        /// <summary>
        /// Foreign key to the main renewal record
        /// </summary>
        public long VehicleLicenceRenewalId { get; set; }

        /// <summary>
        /// Type of identification (e.g., ID, Passport, Company Registration)
        /// </summary>
        public string IdentificationType { get; set; }

        /// <summary>
        /// Identification number
        /// </summary>
        public string IdentificationNumber { get; set; }

        #endregion

        #region Navigation Properties

        /// <summary>
        /// Reference to the main renewal record
        /// </summary>
        public RTMCVehicleLicenceRenewal VehicleLicenceRenewal { get; set; }

        /// <summary>
        /// Delivery address for this owner
        /// </summary>
        public RTMCVehicleLicenceRenewalAddress DeliveryAddress { get; set; }

        /// <summary>
        /// Contact person for this owner
        /// </summary>
        public RTMCVehicleLicenceRenewalContactPerson ContactPerson { get; set; }

        #endregion
    }

    public static class RTMCVehicleLicenceRenewalOwnerConstants
    {
        public const int IdentificationTypeMaxLength = 25;
        public const int IdentificationNumberMaxLength = 50;
    }
}
