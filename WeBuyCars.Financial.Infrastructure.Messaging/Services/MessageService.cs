using MassTransit;
using Microsoft.Extensions.Logging;
using WeBuyCars.Financial.Infrastructure.Messaging.Contracts.Interfaces;
using WeBuyCars.Financial.Infrastructure.Messaging.Services.Interfaces;

namespace WeBuyCars.Financial.Infrastructure.Messaging.Services;

public class MessageService : IMessageService
{
    #region Constructor

    public MessageService(IPublishEndpoint publishEndpoint, ISendEndpointProvider sendEndpointProvider,
        ILogger<MessageService> logger)
    {
        _publishEndpoint = publishEndpoint;
        _sendEndpointProvider = sendEndpointProvider;
        _logger = logger;
    }

    #endregion

    #region Fields

    private readonly IPublishEndpoint _publishEndpoint;
    private readonly ISendEndpointProvider _sendEndpointProvider;
    private readonly ILogger<MessageService> _logger;

    #endregion

    #region Public Methods

    public async Task PublishMessageAsync(IMessageEvent message, CancellationToken cancellationToken)
    {
        try
        {
            ArgumentNullException.ThrowIfNull(message);

            _logger.LogInformation("Publishing message to topic of type {MessageType}", message.GetType().Name);
                
            await _publishEndpoint.Publish((object)message, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error on publishing service bus message to topic");
            throw;
        }
    }

    public async Task SendMessageToQueue(IMessageEvent message, string queueName,
        CancellationToken cancellationToken)
    {
        try
        {
            ArgumentNullException.ThrowIfNull(message);
            ArgumentNullException.ThrowIfNull(queueName);

            _logger.LogInformation("Sending message to queue {QueueName} of type {MessageType}", queueName,
                message.GetType().Name);
                
            var endpoint = await _sendEndpointProvider.GetSendEndpoint(new Uri($"queue:{queueName}"));
            await endpoint.Send((object)message, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error on sending service bus message to queue");
            throw;
        }
    }

    #endregion
}